#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SERVICE-SPECIFIC OPTIMIZATION (Phase 11)
# =============================================================================
# Service-Specific Resource Optimization for 4 vCPU / 8GB RAM VPS
# - PostgreSQL performance tuning (2GB shared_buffers, 200 max_connections)
# - PM2 cluster configuration (4 instances, 1500M memory limit each)
# - NGINX optimization (4 workers, 2048 connections each = 8192 total)
# - Automatic backup creation and rollback on failure
#
# This script runs AFTER all services are installed (Phase 11) to ensure
# configuration files exist and can be properly optimized.
#
# Author: Hauling QR Trip System Deployment Team
# Version: 2.0.0 - Service-specific optimizations only
# =============================================================================

set -euo pipefail

# Load shared configuration and intelligent deployment framework
readonly SERVICE_OPTIMIZATION_SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SHARED_CONFIG="${SERVICE_OPTIMIZATION_SCRIPT_DIR}/shared-config.sh"
readonly INTELLIGENT_FRAMEWORK="${SERVICE_OPTIMIZATION_SCRIPT_DIR}/intelligent-deployment-framework.sh"

if [[ -f "$SHARED_CONFIG" ]]; then
  source "$SHARED_CONFIG"
else
  echo "❌ ERROR: Shared configuration not found: $SHARED_CONFIG"
  exit 1
fi

if [[ -f "$INTELLIGENT_FRAMEWORK" ]]; then
  source "$INTELLIGENT_FRAMEWORK"
else
  echo "❌ ERROR: Intelligent deployment framework not found: $INTELLIGENT_FRAMEWORK"
  exit 1
fi

# =============================================================================
# SERVICE OPTIMIZATION PARAMETERS
# =============================================================================

# System specifications for 4 vCPU / 8GB RAM VPS
readonly SYSTEM_VCPUS=4
readonly SYSTEM_RAM_GB=8
readonly SYSTEM_RAM_MB=$((SYSTEM_RAM_GB * 1024))

# PostgreSQL optimization parameters (calculated for 8GB RAM)
readonly PG_SHARED_BUFFERS="2GB"           # 25% of RAM
readonly PG_EFFECTIVE_CACHE_SIZE="6GB"     # 75% of RAM
readonly PG_WORK_MEM="16MB"                # RAM/(4*max_connections)
readonly PG_MAINTENANCE_WORK_MEM="512MB"   # 1/16 of RAM
readonly PG_WAL_BUFFERS="16MB"             # 3% of shared_buffers
readonly PG_MAX_WAL_SIZE="2GB"             # For write-heavy workloads
readonly PG_MAX_CONNECTIONS=200            # Concurrent trip operations
readonly PG_CHECKPOINT_COMPLETION_TARGET="0.9"
readonly PG_RANDOM_PAGE_COST="1.1"        # SSD optimized
readonly PG_EFFECTIVE_IO_CONCURRENCY=200   # SSD optimized
readonly PG_DEFAULT_STATISTICS_TARGET=100

# PM2 optimization parameters
readonly PM2_INSTANCES=4                   # One per vCPU
readonly PM2_MAX_MEMORY="1500M"           # Safe limit per process
readonly PM2_EXEC_MODE="cluster"          # Load balancing
readonly PM2_NODE_ARGS="--max-old-space-size=1400"

# NGINX optimization parameters
readonly NGINX_WORKER_PROCESSES=4         # Match vCPU count
readonly NGINX_WORKER_CONNECTIONS=2048    # Optimal for 8GB RAM
readonly NGINX_CLIENT_BODY_BUFFER_SIZE="128k"
readonly NGINX_CLIENT_HEADER_BUFFER_SIZE="32k"
readonly NGINX_GZIP_COMP_LEVEL=6
readonly NGINX_KEEPALIVE_TIMEOUT="65s"
readonly NGINX_KEEPALIVE_REQUESTS=1000

# =============================================================================
# BACKUP AND ROLLBACK FUNCTIONS
# =============================================================================

# REMOVED: Backup file creation functionality eliminated per user request

# REMOVED: Rollback functionality eliminated per user request

# =============================================================================
# POSTGRESQL OPTIMIZATION
# =============================================================================

optimize_postgresql() {
  log_info "🐘 Optimizing PostgreSQL for 8GB RAM system..."
  
  # Find PostgreSQL configuration file
  local pg_config_file
  pg_config_file=$(sudo find /etc/postgresql -name "postgresql.conf" | head -1)
  
  if [[ -z "$pg_config_file" ]]; then
    log_error "❌ PostgreSQL configuration file not found"
    log_error "❌ Ensure PostgreSQL is installed before running service optimization"
    return 1
  fi
  
  log_info "Found PostgreSQL config: $pg_config_file"
  
  # Apply optimizations with idempotent configuration management
  log_info "🔧 Applying idempotent PostgreSQL configuration..."

  # Define unique markers for PostgreSQL configuration
  local PG_START_MARKER="# === HAULING QR TRIP SYSTEM - POSTGRESQL OPTIMIZATIONS START ==="
  local PG_END_MARKER="# === HAULING QR TRIP SYSTEM - POSTGRESQL OPTIMIZATIONS END ==="

  # Remove any existing Hauling QR Trip System PostgreSQL configuration
  if grep -q "$PG_START_MARKER" "$pg_config_file"; then
    log_info "Removing existing Hauling QR Trip System PostgreSQL configuration..."
    sudo sed -i "/$PG_START_MARKER/,/$PG_END_MARKER/d" "$pg_config_file"
  fi

  # Add our PostgreSQL configuration block
  sudo tee -a "$pg_config_file" > /dev/null << EOF

$PG_START_MARKER
# Applied: $(date)
# System: 4 vCPU / 8GB RAM VPS
# Version: Phase 11 Service-Specific Optimizations

# Memory Configuration
shared_buffers = $PG_SHARED_BUFFERS
effective_cache_size = $PG_EFFECTIVE_CACHE_SIZE
work_mem = $PG_WORK_MEM
maintenance_work_mem = $PG_MAINTENANCE_WORK_MEM

# Connection Configuration
max_connections = $PG_MAX_CONNECTIONS

# WAL Configuration
wal_buffers = $PG_WAL_BUFFERS
max_wal_size = $PG_MAX_WAL_SIZE
checkpoint_completion_target = $PG_CHECKPOINT_COMPLETION_TARGET

# Query Planner Configuration
random_page_cost = $PG_RANDOM_PAGE_COST
effective_io_concurrency = $PG_EFFECTIVE_IO_CONCURRENCY
default_statistics_target = $PG_DEFAULT_STATISTICS_TARGET

# Logging Configuration (for monitoring)
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# Additional Performance Settings
synchronous_commit = on
full_page_writes = on
wal_compression = on
$PG_END_MARKER
EOF

  # Restart PostgreSQL to apply configuration changes
  log_info "🔄 Restarting PostgreSQL to apply optimizations..."
  if sudo systemctl restart postgresql; then
    log_success "✅ PostgreSQL restarted successfully"
    # Wait for PostgreSQL to be ready
    sleep 3
    if sudo systemctl is-active --quiet postgresql; then
      log_success "✅ PostgreSQL is running with new configuration"
    else
      log_error "❌ PostgreSQL failed to start with new configuration"
      return 1
    fi
  else
    log_error "❌ Failed to restart PostgreSQL"
    return 1
  fi

  log_success "✅ PostgreSQL configuration optimized and applied"
}

# =============================================================================
# PM2 ECOSYSTEM OPTIMIZATION
# =============================================================================

optimize_pm2_ecosystem() {
  log_info "⚡ Creating optimized PM2 ecosystem configuration..."
  
  # Ensure application directory exists
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    log_error "❌ Ensure application is deployed before running service optimization"
    return 1
  fi
  
  # Create optimized ecosystem.config.js (with conflict prevention marker)
  log_info "Creating optimized PM2 ecosystem configuration..."
  cat > "$APP_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: '$APP_DIR',
    instances: $PM2_INSTANCES,
    exec_mode: '$PM2_EXEC_MODE',
    instance_var: 'INSTANCE_ID',
    env: {
      NODE_ENV: 'production',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false,
      INSTANCE_ID: 0
    },
    env_production: {
      NODE_ENV: 'production',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false
    },
    log_file: '/home/<USER>/.pm2/logs/hauling-qr-server.log',
    out_file: '/home/<USER>/.pm2/logs/hauling-qr-server-out.log',
    error_file: '/home/<USER>/.pm2/logs/hauling-qr-server-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '$PM2_MAX_MEMORY',
    node_args: '$PM2_NODE_ARGS',
    kill_timeout: 5000,
    listen_timeout: 10000,
    restart_delay: 1000,
    max_restarts: 10,
    min_uptime: '10s',
    autorestart: true,
    watch: false,
    ignore_watch: ['node_modules', 'logs', '.git'],
    source_map_support: false,
    instance_var: 'INSTANCE_ID'
  }]
};
EOF
  
  # Set proper ownership
  sudo chown "$UBUNTU_USER:$UBUNTU_USER" "$APP_DIR/ecosystem.config.js"

  # CRITICAL FIX: Mark as optimized using JavaScript comment syntax
  echo "" >> "$APP_DIR/ecosystem.config.js"
  echo "// OPTIMIZED BY PHASE 11 - DO NOT OVERWRITE" >> "$APP_DIR/ecosystem.config.js"
  echo "// Generated at: $(date)" >> "$APP_DIR/ecosystem.config.js"

  # Apply PM2 configuration if PM2 is running
  log_info "🔄 Applying PM2 ecosystem configuration..."
  if command -v pm2 >/dev/null 2>&1; then
    # Stop existing PM2 processes
    sudo -u "$UBUNTU_USER" pm2 kill >/dev/null 2>&1 || true

    # CRITICAL FIX: Validate configuration syntax before applying
    log_info "🔍 Validating PM2 configuration syntax..."
    if node -c "$APP_DIR/ecosystem.config.js" 2>/dev/null; then
      log_success "✅ PM2 configuration syntax is valid"

      # Start with new ecosystem configuration
      if sudo -u "$UBUNTU_USER" pm2 start "$APP_DIR/ecosystem.config.js" --env production 2>/dev/null; then
        log_success "✅ PM2 started with optimized configuration"
        # Save PM2 configuration
        sudo -u "$UBUNTU_USER" pm2 save >/dev/null 2>&1 || true
      else
        log_warning "⚠️ PM2 start failed, but configuration is saved for later use"
      fi
    else
      log_error "❌ PM2 configuration has syntax errors, fixing..."
      # Recreate configuration without problematic comments
      create_clean_pm2_config
    fi
  else
    log_info "PM2 not installed yet, configuration saved for later use"
  fi

  log_success "✅ PM2 ecosystem configuration optimized for $PM2_INSTANCES instances"
}

# Create clean PM2 configuration without syntax issues
create_clean_pm2_config() {
  log_info "🔧 Creating clean PM2 configuration..."

  # Use the template file if available, otherwise create inline
  local template_file="${SCRIPT_DIR}/templates/pm2-ecosystem-optimized.config.js"

  if [[ -f "$template_file" ]]; then
    log_info "📋 Using PM2 template: $template_file"
    cp "$template_file" "$APP_DIR/ecosystem.config.js"

    # Update paths in template
    sed -i "s|/var/www/hauling-qr-system|$APP_DIR|g" "$APP_DIR/ecosystem.config.js"
  else
    log_info "📝 Creating PM2 configuration inline..."
    # Create minimal but functional configuration
    cat > "$APP_DIR/ecosystem.config.js" << 'EOF'
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: './server/server.js',
    instances: 4,
    exec_mode: 'cluster',
    max_memory_restart: '1500M',
    env: {
      NODE_ENV: 'production',
      BACKEND_HTTP_PORT: 8080,
      HTTPS_PORT: 8443,
      ENABLE_HTTPS: false
    },
    env_production: {
      NODE_ENV: 'production',
      BACKEND_HTTP_PORT: 8080,
      HTTPS_PORT: 8443,
      ENABLE_HTTPS: false
    }
  }]
};
EOF
  fi

  # Set proper ownership
  sudo chown "$UBUNTU_USER:$UBUNTU_USER" "$APP_DIR/ecosystem.config.js"

  log_success "✅ Clean PM2 configuration created"
}

# =============================================================================
# NGINX OPTIMIZATION
# =============================================================================

optimize_nginx() {
  log_info "🌐 Optimizing NGINX for high-concurrency operations..."

  # Check if NGINX is installed
  if ! command -v nginx >/dev/null 2>&1; then
    log_error "❌ NGINX is not installed"
    log_error "❌ Ensure NGINX is installed before running service optimization"
    return 1
  fi

  # Create optimized nginx.conf
  sudo tee /etc/nginx/nginx.conf > /dev/null << EOF
user www-data;
worker_processes $NGINX_WORKER_PROCESSES;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

# =============================================================================
# HAULING QR TRIP SYSTEM - OPTIMIZED NGINX CONFIGURATION
# =============================================================================

# Worker configuration optimized for 4 vCPU / 8GB RAM
events {
    worker_connections $NGINX_WORKER_CONNECTIONS;
    use epoll;
    multi_accept on;
}

http {
    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout $NGINX_KEEPALIVE_TIMEOUT;
    keepalive_requests $NGINX_KEEPALIVE_REQUESTS;
    types_hash_max_size 2048;
    server_tokens off;

    # Buffer Settings (optimized for QR code uploads)
    client_body_buffer_size $NGINX_CLIENT_BODY_BUFFER_SIZE;
    client_header_buffer_size $NGINX_CLIENT_HEADER_BUFFER_SIZE;
    client_max_body_size 10M;
    large_client_header_buffers 4 32k;

    # MIME Types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip Compression (optimized for React frontend)
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level $NGINX_GZIP_COMP_LEVEL;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;

    # Rate Limiting (prevent API abuse)
    limit_req_zone \$binary_remote_addr zone=api:10m rate=30r/m;
    limit_req_zone \$binary_remote_addr zone=qr:10m rate=60r/m;

    # Logging
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for" '
                    'rt=\$request_time uct="\$upstream_connect_time" '
                    'uht="\$upstream_header_time" urt="\$upstream_response_time"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF

  # Test and reload NGINX configuration
  log_info "🔄 Testing and reloading NGINX configuration..."
  if sudo nginx -t; then
    if sudo systemctl reload nginx; then
      log_success "✅ NGINX configuration reloaded successfully"
    else
      log_warning "⚠️ NGINX reload failed, trying restart..."
      if sudo systemctl restart nginx; then
        log_success "✅ NGINX restarted successfully"
      else
        log_error "❌ NGINX restart failed"
        return 1
      fi
    fi
  else
    log_error "❌ NGINX configuration test failed"
    return 1
  fi

  log_success "✅ NGINX configuration optimized and applied"

  # CRITICAL FIX: Apply optimized site configuration with CORS support
  log_info "🔧 Applying optimized NGINX site configuration with CORS support..."
  if ! optimize_nginx_site_configuration; then
    log_warning "⚠️ NGINX site configuration optimization failed - applying basic CORS fix"
    if ! apply_basic_cors_fix; then
      log_error "❌ Both advanced and basic CORS fixes failed"
      return 1
    fi
  fi

  return 0
}

# =============================================================================
# NGINX SITE CONFIGURATION WITH CORS SUPPORT
# =============================================================================

optimize_nginx_site_configuration() {
  log_info "🔧 Applying optimized NGINX site configuration with CORS support..."

  # Detect VPS IP and domain configuration
  local DETECTED_VPS_IP
  DETECTED_VPS_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "127.0.0.1")

  # CRITICAL: Prioritize environment variable over .env file for dynamic deployment
  local domain="${PRODUCTION_DOMAIN}"

  # Only read from .env file if environment variable is not set or is default
  if [[ -z "${PRODUCTION_DOMAIN}" || "${PRODUCTION_DOMAIN}" == "truckhaul.top" ]]; then
    if [[ -f "$APP_DIR/.env" ]]; then
      domain=$(grep -E '^PRODUCTION_DOMAIN=' "$APP_DIR/.env" | sed -E 's/PRODUCTION_DOMAIN=\"?([^\"]*)\"?/\1/' || echo "${PRODUCTION_DOMAIN:-truckhaul.top}")
    fi
  fi

  # Final fallback
  domain="${domain:-truckhaul.top}"

  log_info "Configuring optimized NGINX site for domain: $domain (IP: $DETECTED_VPS_IP)"

  # Create optimized site configuration with CORS support
  sudo tee /etc/nginx/sites-available/hauling-qr-system > /dev/null << EOF
# =============================================================================
# HAULING QR TRIP SYSTEM - OPTIMIZED SITE CONFIGURATION WITH CORS
# =============================================================================
# Applied by service optimization (Phase 11)
# Includes proper CORS headers and API proxy configuration
# =============================================================================

server {
    listen 80;
    server_name ${domain} www.${domain} api.${domain} ${DETECTED_VPS_IP};

    root /var/www/hauling-qr-system/client/build;
    index index.html index.htm;

    # Real IP from Cloudflare
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;

    # Connection limits (removed to prevent shared memory zone errors)
    # limit_conn perip 20;
    # limit_conn perserver 1000;

    # ==========================================================================
    # CORS HEADERS FOR ALL RESPONSES (CREDENTIALS-COMPATIBLE)
    # ==========================================================================

    # CORS configuration disabled - handled by Phase 5 deployment
    # Phase 5 (5_install-nginx.sh) handles all CORS headers to prevent duplication

    # ==========================================================================
    # API ENDPOINTS WITH CORS PREFLIGHT SUPPORT
    # ==========================================================================

    # Handle preflight OPTIONS requests for all API endpoints
    location ~ ^/api/ {
        # Preflight handling disabled - Phase 5 handles all CORS
        # Phase 5 (5_install-nginx.sh) handles preflight requests to prevent duplication

        # Proxy to backend with CORS headers
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;

        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # CORS headers disabled - Phase 5 handles all CORS configuration
        # Phase 5 (5_install-nginx.sh) manages CORS to prevent duplicate headers
    }

    # WebSocket endpoints
    location /socket.io/ {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 86400;
    }

    # ==========================================================================
    # STATIC FILE SERVING WITH CACHING
    # ==========================================================================

    # Serve React frontend
    location / {
        try_files \$uri \$uri/ /index.html;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            # CORS disabled - Phase 5 handles static asset CORS
        }
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Content Security Policy for QR scanner
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: http://${domain}:8080 ws://${domain}:8080 https://${domain};" always;
}
EOF

  # Enable the site
  sudo ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
  sudo rm -f /etc/nginx/sites-enabled/default 2>/dev/null || true

  # Test and reload NGINX
  if sudo nginx -t; then
    if sudo systemctl reload nginx; then
      log_success "✅ Optimized NGINX site configuration applied with CORS support"
    else
      log_warning "⚠️ NGINX reload failed, trying restart..."
      if sudo systemctl restart nginx; then
        log_success "✅ NGINX restarted with optimized site configuration"
      else
        log_error "❌ NGINX restart failed"
        return 1
      fi
    fi
  else
    log_error "❌ NGINX site configuration test failed"
    return 1
  fi

  log_success "✅ NGINX site configuration optimized with CORS support"
  return 0
}

# =============================================================================
# BASIC CORS FIX (FALLBACK STRATEGY)
# =============================================================================

apply_basic_cors_fix() {
  log_info "🔧 Basic CORS fix disabled - Phase 5 handles CORS configuration"
  log_info "💡 CORS headers are configured in Phase 5 (5_install-nginx.sh)"
  log_info "💡 This prevents duplicate CORS headers that cause browser errors"
  log_success "✅ CORS configuration handled by Phase 5 deployment"
  return 0
}

# =============================================================================
# SERVICE VALIDATION FUNCTIONS
# =============================================================================

validate_postgresql_optimization() {
  log_info "🔍 Validating PostgreSQL optimization..."

  # Check if PostgreSQL is running
  if ! sudo systemctl is-active --quiet postgresql; then
    log_error "❌ PostgreSQL is not running"
    return 1
  fi

  # Test configuration syntax (PostgreSQL 17+ compatible)
  local pg_data_dir=$(sudo -u postgres psql -t -c "SHOW data_directory;" 2>/dev/null | xargs)
  if [[ -n "$pg_data_dir" ]]; then
    if ! sudo -u postgres /usr/lib/postgresql/*/bin/postgres --check-config -D "$pg_data_dir" >/dev/null 2>&1; then
      log_warning "⚠️ PostgreSQL configuration syntax check failed, but continuing..."
    else
      log_success "✅ PostgreSQL configuration syntax is valid"
    fi
  else
    log_warning "⚠️ Could not determine PostgreSQL data directory, skipping syntax check"
  fi

  # Check key parameters
  local shared_buffers
  shared_buffers=$(sudo -u postgres psql -t -c "SHOW shared_buffers;" 2>/dev/null | xargs)

  if [[ "$shared_buffers" == "$PG_SHARED_BUFFERS" ]]; then
    log_success "✅ PostgreSQL shared_buffers: $shared_buffers"
  else
    log_warning "⚠️ PostgreSQL shared_buffers not applied: $shared_buffers (expected: $PG_SHARED_BUFFERS)"
  fi

  return 0
}

validate_nginx_optimization() {
  log_info "🔍 Validating NGINX optimization..."

  # Test NGINX configuration
  if ! sudo nginx -t; then
    log_error "❌ NGINX configuration has syntax errors"
    return 1
  fi

  # Check if NGINX is running
  if ! sudo systemctl is-active --quiet nginx; then
    log_warning "⚠️ NGINX is not running, but configuration is valid"
  else
    log_success "✅ NGINX configuration validated and service running"
  fi

  return 0
}

validate_pm2_optimization() {
  log_info "🔍 Validating PM2 optimization..."

  # Check if ecosystem.config.js exists
  if [[ ! -f "$APP_DIR/ecosystem.config.js" ]]; then
    log_error "❌ PM2 ecosystem configuration not found"
    return 1
  fi

  # Check if configuration has cluster mode
  if grep -q "exec_mode: '$PM2_EXEC_MODE'" "$APP_DIR/ecosystem.config.js" && grep -q "instances: $PM2_INSTANCES" "$APP_DIR/ecosystem.config.js"; then
    log_success "✅ PM2 ecosystem configured for $PM2_INSTANCES cluster instances"
  else
    log_warning "⚠️ PM2 ecosystem configuration may not be optimized"
  fi

  return 0
}

# =============================================================================
# MAIN FUNCTION
# =============================================================================

main() {
  log_info "🚀 Starting Service-Specific Resource Optimization (Phase 11)"
  log_info "📅 Started at: $(date)"
  log_info "🖥️ System: $SYSTEM_VCPUS vCPU / ${SYSTEM_RAM_GB}GB RAM VPS"

  # Define log file for this script
  local LOG_FILE="${LOG_DIR}/service-optimization-$(date +%Y%m%d-%H%M%S).log"
  log_info "📝 Log file: $LOG_FILE"

  # Initialize intelligent deployment tracking
  init_intelligent_progress_tracking "service-optimization" "essential"

  # Check root privileges
  if [[ $EUID -ne 0 ]]; then
    log_error "❌ This script must be run as root (use sudo)"
    exit 1
  fi

  # REMOVED: Backup and rollback functionality eliminated per user request

  # Step 1: Optimize PostgreSQL
  if ! optimize_postgresql; then
    log_error "❌ PostgreSQL optimization failed"
    exit 1
  fi

  # Step 2: Optimize PM2 ecosystem (with graceful fallback)
  if ! optimize_pm2_ecosystem; then
    log_warning "⚠️ PM2 ecosystem optimization failed - continuing with existing configuration"
    log_info "💡 PM2 will continue running with current settings"
  fi

  # Step 3: Optimize NGINX (with graceful fallback)
  if ! optimize_nginx; then
    log_warning "⚠️ NGINX optimization failed - continuing with existing configuration"
    log_info "💡 NGINX will continue running with current settings"
  fi

  # Step 4: Validate optimizations
  log_info "🔍 Validating service optimizations..."

  validate_postgresql_optimization || log_warning "⚠️ PostgreSQL validation had issues"
  validate_nginx_optimization || log_warning "⚠️ NGINX validation had issues"
  validate_pm2_optimization || log_warning "⚠️ PM2 validation had issues"

  # Update deployment progress
  update_deployment_progress "service-optimization" "completed" "essential" "All service optimizations applied successfully"

  # REMOVED: Error trap clearing (no longer needed)

  log_success "✅ Service-Specific Resource Optimization completed successfully"
  log_info "📊 Service performance improvements applied:"
  log_info "   • PostgreSQL: Optimized for ${SYSTEM_RAM_GB}GB RAM with $PG_MAX_CONNECTIONS connections"
  log_info "   • PM2: $PM2_INSTANCES cluster instances with ${PM2_MAX_MEMORY} memory limit each"
  log_info "   • NGINX: $NGINX_WORKER_PROCESSES workers with $NGINX_WORKER_CONNECTIONS connections each"
  log_info ""
  log_info "🔄 Services will be restarted to apply optimizations"
  log_info "💡 Combined with Phase 0 system optimizations for maximum performance"
}

# Execute main function
main "$@"
