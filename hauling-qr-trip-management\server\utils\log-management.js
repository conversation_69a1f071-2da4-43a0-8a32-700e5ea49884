/**
 * Log Management Utility
 * 
 * Provides utilities for managing log files, cleanup, and monitoring
 * log system performance and health.
 */

const fs = require('fs').promises;
const path = require('path');
const { getServerConfig } = require('../config/unified-config');
const { asyncLogBuffer } = require('./logger');

class LogManagement {
  constructor() {
    this.config = getServerConfig();
    this.logsDir = path.join(__dirname, '../logs');
  }

  /**
   * Get log file statistics
   * @returns {Object} Log file statistics
   */
  async getLogStats() {
    try {
      const files = await fs.readdir(this.logsDir);
      const logFiles = files.filter(file => file.endsWith('.log'));
      
      const stats = {
        totalFiles: logFiles.length,
        totalSize: 0,
        files: [],
        oldestFile: null,
        newestFile: null
      };

      for (const file of logFiles) {
        const filePath = path.join(this.logsDir, file);
        const fileStat = await fs.stat(filePath);
        
        const fileInfo = {
          name: file,
          size: fileStat.size,
          sizeHuman: this.formatBytes(fileStat.size),
          created: fileStat.birthtime,
          modified: fileStat.mtime,
          age: Date.now() - fileStat.mtime.getTime()
        };
        
        stats.files.push(fileInfo);
        stats.totalSize += fileStat.size;
        
        if (!stats.oldestFile || fileStat.mtime < stats.oldestFile.modified) {
          stats.oldestFile = fileInfo;
        }
        
        if (!stats.newestFile || fileStat.mtime > stats.newestFile.modified) {
          stats.newestFile = fileInfo;
        }
      }
      
      stats.totalSizeHuman = this.formatBytes(stats.totalSize);
      stats.files.sort((a, b) => b.modified - a.modified);
      
      return stats;
    } catch (error) {
      console.error('Error getting log stats:', error);
      return null;
    }
  }

  /**
   * Clean up old log files based on retention policy
   * @param {number} maxAgeMs Maximum age in milliseconds
   * @param {number} maxFiles Maximum number of files to keep
   * @returns {Object} Cleanup results
   */
  async cleanupOldLogs(maxAgeMs = 7 * 24 * 60 * 60 * 1000, maxFiles = 50) {
    try {
      const stats = await this.getLogStats();
      if (!stats) return null;

      const now = Date.now();
      const filesToDelete = [];
      
      // Find files older than maxAge
      const oldFiles = stats.files.filter(file => 
        now - file.modified.getTime() > maxAgeMs
      );
      
      // Find excess files (keep only maxFiles newest)
      const excessFiles = stats.files.slice(maxFiles);
      
      // Combine and deduplicate
      const allFilesToDelete = [...new Set([...oldFiles, ...excessFiles])];
      
      for (const file of allFilesToDelete) {
        const filePath = path.join(this.logsDir, file.name);
        await fs.unlink(filePath);
        filesToDelete.push(file);
      }
      
      return {
        deletedFiles: filesToDelete.length,
        deletedSize: filesToDelete.reduce((sum, file) => sum + file.size, 0),
        deletedSizeHuman: this.formatBytes(filesToDelete.reduce((sum, file) => sum + file.size, 0)),
        remainingFiles: stats.totalFiles - filesToDelete.length,
        files: filesToDelete.map(f => f.name)
      };
    } catch (error) {
      console.error('Error cleaning up logs:', error);
      return null;
    }
  }

  /**
   * Analyze log patterns and provide optimization recommendations
   * @returns {Object} Analysis results
   */
  async analyzeLogPatterns() {
    try {
      const combinedLogPath = path.join(this.logsDir, 'combined.log');
      
      // Check if file exists
      try {
        await fs.access(combinedLogPath);
      } catch {
        return { error: 'Combined log file not found' };
      }
      
      const content = await fs.readFile(combinedLogPath, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      const analysis = {
        totalLines: lines.length,
        contexts: {},
        levels: {},
        timeRange: { start: null, end: null },
        duplicateMessages: {},
        recommendations: []
      };
      
      // Analyze each log entry
      for (const line of lines) {
        try {
          const entry = JSON.parse(line);
          
          // Count by context
          analysis.contexts[entry.context] = (analysis.contexts[entry.context] || 0) + 1;
          
          // Count by level
          analysis.levels[entry.level] = (analysis.levels[entry.level] || 0) + 1;
          
          // Track time range
          if (entry.timestamp) {
            const timestamp = new Date(entry.timestamp);
            if (!analysis.timeRange.start || timestamp < analysis.timeRange.start) {
              analysis.timeRange.start = timestamp;
            }
            if (!analysis.timeRange.end || timestamp > analysis.timeRange.end) {
              analysis.timeRange.end = timestamp;
            }
          }
          
          // Track duplicate messages
          const messageKey = `${entry.context}:${entry.message}`;
          analysis.duplicateMessages[messageKey] = (analysis.duplicateMessages[messageKey] || 0) + 1;
          
        } catch (parseError) {
          // Skip invalid JSON lines
        }
      }
      
      // Generate recommendations
      const topContexts = Object.entries(analysis.contexts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
      
      const duplicates = Object.entries(analysis.duplicateMessages)
        .filter(([, count]) => count > 10)
        .sort(([,a], [,b]) => b - a);
      
      if (duplicates.length > 0) {
        analysis.recommendations.push(`Found ${duplicates.length} message types with >10 duplicates. Consider implementing deduplication.`);
      }
      
      if (analysis.contexts['SYNC_MONITOR.CHECK_START'] > 100) {
        analysis.recommendations.push('High frequency of sync monitor checks. Consider increasing check intervals.');
      }
      
      if (analysis.levels['INFO'] > analysis.totalLines * 0.8) {
        analysis.recommendations.push('High percentage of INFO logs. Consider raising log level in production.');
      }
      
      analysis.topContexts = topContexts;
      analysis.topDuplicates = duplicates.slice(0, 10);
      
      return analysis;
    } catch (error) {
      console.error('Error analyzing log patterns:', error);
      return { error: error.message };
    }
  }

  /**
   * Format bytes to human readable format
   * @param {number} bytes Number of bytes
   * @returns {string} Human readable size
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get current log buffer statistics (if available)
   * @returns {Object|null} Buffer statistics
   */
  getBufferStats() {
    if (asyncLogBuffer) {
      return asyncLogBuffer.getStats();
    }
    return null;
  }

  /**
   * Force flush log buffer (if available)
   */
  async flushBuffer() {
    if (asyncLogBuffer) {
      await asyncLogBuffer.flush();
      return true;
    }
    return false;
  }
}

module.exports = LogManagement;
