// Service Worker Registration Utility
const isLocalhost = Boolean(
  window.location.hostname === 'localhost' ||
  window.location.hostname === '[::1]' ||
  window.location.hostname.match(
    /^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/
  )
);

export function registerSW(config = {}) {
  if ('serviceWorker' in navigator) {
    // Check if we're on a PWA page (driver-connect or trip-scanner)
    const currentPath = window.location.pathname;
    const isPWAPage = currentPath === '/driver-connect' || currentPath === '/trip-scanner';
    
    // Check if we're running in PWA mode (standalone display)
    const isPWAMode = window.matchMedia('(display-mode: standalone)').matches || 
                      window.navigator.standalone === true; // iOS Safari
    
    console.log('PWA Detection:', { currentPath, isPWAPage, isPWAMode });
    
    // Always register service worker for PWA functionality
    // The service worker itself will handle which requests to intercept
    const shouldRegister = process.env.NODE_ENV === 'production' || config.enableInDev;
    console.log('Registration conditions:', {
      nodeEnv: process.env.NODE_ENV,
      enableInDev: config.enableInDev,
      shouldRegister: shouldRegister
    });
    
    if (shouldRegister) {
      window.addEventListener('load', () => {
        const swUrl = '/sw.js';
        
        // Pass PWA mode info to service worker
        const enhancedConfig = {
          ...config,
          isPWAMode: isPWAMode,
          currentPath: currentPath
        };
        
        if (isLocalhost) {
          // Check if service worker exists in localhost
          checkValidServiceWorker(swUrl, enhancedConfig);
        } else {
          // Register service worker in production
          registerValidSW(swUrl, enhancedConfig);
        }
      });
    } else {
      console.log('Service worker registration skipped - not in production and enableInDev not set');
    }
  } else {
    console.log('Service worker not supported in this browser');
  }
}

function registerValidSW(swUrl, config) {
  navigator.serviceWorker
    .register(swUrl, {
      updateViaCache: 'none' // Prevent caching of the service worker itself
    })
    .then((registration) => {
      // Send PWA mode info to service worker
      if (navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'PWA_MODE_INFO',
          isPWAMode: config.isPWAMode,
          currentPath: config.currentPath
        });
        console.log('[SW Registration] Sent PWA mode info:', config.isPWAMode, config.currentPath);
      } else {
        console.log('[SW Registration] No controller available to send PWA mode info');
      }
      
      // Handle updates
      registration.addEventListener('updatefound', () => {
        const installingWorker = registration.installing;
        if (installingWorker == null) return;

        installingWorker.addEventListener('statechange', () => {
          if (installingWorker.state === 'installed') {
            if (navigator.serviceWorker.controller) {
              // New content is available
              // Call onUpdate callback if provided
              if (config && config.onUpdate) {
                config.onUpdate(registration);
              }
            } else {
              // Content is cached for offline use
              // Send PWA mode info to new service worker
              if (navigator.serviceWorker.controller) {
                navigator.serviceWorker.controller.postMessage({
                  type: 'PWA_MODE_INFO',
                  isPWAMode: config.isPWAMode,
                  currentPath: config.currentPath
                });
                console.log('[SW Registration] Sent PWA mode info to new SW:', config.isPWAMode);
              }
              
              // Call onSuccess callback if provided
              if (config && config.onSuccess) {
                config.onSuccess(registration);
              }
            }
          }
        });
      });
    })
    .catch((error) => {
      console.error('SW registration failed:', error);
      
      // Call onError callback if provided
      if (config && config.onError) {
        config.onError(error);
      }
    });
}

function checkValidServiceWorker(swUrl, config) {
  // Check if the service worker can be found
  fetch(swUrl, {
    headers: { 'Service-Worker': 'script' },
  })
    .then((response) => {
      const contentType = response.headers.get('content-type');
      if (
        response.status === 404 ||
        (contentType != null && contentType.indexOf('javascript') === -1)
      ) {
        // No service worker found, probably a different app
        navigator.serviceWorker.ready.then((registration) => {
          registration.unregister().then(() => {
            window.location.reload();
          });
        });
      } else {
        // Service worker found, proceed with registration
        registerValidSW(swUrl, config);
      }
    })
    .catch(() => {
      console.log('No internet connection found. App is running in offline mode.');
    });
}

export function unregister() {
  if ('serviceWorker' in navigator) {
    // Get all registrations and unregister them
    navigator.serviceWorker.getRegistrations()
      .then((registrations) => {
        registrations.forEach((registration) => {
          console.log('Unregistering service worker:', registration.scope);
          registration.unregister();
        });
      })
      .catch((error) => {
        console.error('Error unregistering service workers:', error);
      });
    
    // Also clear all caches to prevent interference
    if ('caches' in window) {
      caches.keys()
        .then((cacheNames) => {
          return Promise.all(
            cacheNames.map((cacheName) => {
              console.log('Deleting cache:', cacheName);
              return caches.delete(cacheName);
            })
          );
        })
        .catch((error) => {
          console.error('Error clearing caches:', error);
        });
    }
  }
}