#!/usr/bin/env node
/**
 * Production Mode Startup Script (HTTP)
 * Automatically configures and starts the system in production mode with HTTP
 */

const { spawn } = require('child_process');
const { loadConfig, writeClientEnv, displayConfig } = require('../config-loader');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Hauling QR Trip System in Production Mode (HTTP)...\n');

// Configure environment for production HTTP
const envPath = path.join(__dirname, '..', '.env');
let envContent = fs.readFileSync(envPath, 'utf8');

// Update configuration
envContent = envContent.replace(/^NODE_ENV=.*/m, 'NODE_ENV=production');
envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, 'ENABLE_HTTPS=false');

// Write updated .env file
fs.writeFileSync(envPath, envContent);

// Load configuration and generate client env
const config = loadConfig();
writeClientEnv(config);
displayConfig(config);

console.log('\n🏗️  Building client for production...\n');

// Build the client first
const buildProcess = spawn('npm', ['run', 'build'], {
  cwd: path.join(__dirname, '..', 'client'),
  stdio: 'inherit'
});

buildProcess.on('exit', (code) => {
  if (code !== 0) {
    console.error('❌ Client build failed');
    process.exit(1);
  }
  
  console.log('\n✅ Client build completed');
  console.log('\n🔄 Starting production server...\n');
  
  // Start the production server
  const serverProcess = spawn('node', ['server.js'], {
    cwd: path.join(__dirname, '..', 'server'),
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production' }
  });
  
  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down production server...');
    serverProcess.kill('SIGINT');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down production server...');
    serverProcess.kill('SIGTERM');
    process.exit(0);
  });

  serverProcess.on('exit', (code) => {
    console.log(`\n✅ Production server stopped with code ${code}`);
    process.exit(code);
  });
});
