/**
 * Disable Webpack WebSocket for Dev Tunnels
 * This script patches the webpack-dev-server to disable WebSocket connections
 * that cause issues in dev tunnel environments.
 */

const fs = require('fs');
const path = require('path');

// Check if we're in a dev tunnel environment
const isDevTunnel = process.env.REACT_APP_DEV_TUNNEL === 'true' ||
                   process.env.CODESPACES ||
                   process.env.GITPOD_WORKSPACE_ID ||
                   process.env.VSCODE_TUNNEL ||
                   (process.env.HOST && process.env.HOST.includes('devtunnels.ms'));

if (isDevTunnel) {
  console.log('🚫 Dev tunnel detected - disabling webpack WebSocket...');
  
  // Override the WebSocket client to prevent connections
  const webSocketClientPath = path.join(__dirname, 'node_modules/webpack-dev-server/client/clients/WebSocketClient.js');
  
  if (fs.existsSync(webSocketClientPath)) {
    try {
      let content = fs.readFileSync(webSocketClientPath, 'utf8');
      
      // Replace the WebSocket constructor to prevent connections
      const patchedContent = content.replace(
        'this.client = new WebSocket(this.url, this.protocol);',
        `
        console.log('🚫 Webpack WebSocket disabled in dev tunnel environment');
        this.client = {
          readyState: 3, // CLOSED
          close: () => {},
          addEventListener: () => {},
          removeEventListener: () => {}
        };
        // Simulate immediate close
        setTimeout(() => {
          if (this.onClose) this.onClose();
        }, 100);
        return;
        `
      );
      
      if (content !== patchedContent) {
        fs.writeFileSync(webSocketClientPath, patchedContent);
        console.log('✅ Webpack WebSocket successfully disabled');
      }
    } catch (error) {
      console.warn('⚠️ Could not patch webpack WebSocket:', error.message);
    }
  }
  
  // Also set environment variables to disable WebSocket
  process.env.WDS_SOCKET_PORT = '0';
  process.env.FAST_REFRESH = 'false';
}

module.exports = { isDevTunnel };
