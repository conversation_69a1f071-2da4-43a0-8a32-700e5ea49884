import React from 'react';

const ResponsiveTableContainer = ({ children, className = "" }) => {
  return (
    <div className={`bg-white shadow-lg overflow-hidden sm:rounded-lg ${className}`}>
      {/* Desktop View */}
      <div className="hidden md:block">
        <div className="overflow-x-auto">
          <div className="min-w-full inline-block align-middle">
            {children}
          </div>
        </div>
      </div>
      
      {/* Mobile View */}
      <div className="md:hidden">
        <div className="overflow-x-auto">
          <div className="min-w-full inline-block align-middle">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveTableContainer;
