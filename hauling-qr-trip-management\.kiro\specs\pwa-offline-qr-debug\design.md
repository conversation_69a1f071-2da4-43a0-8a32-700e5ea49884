# Design Document

## Overview

This design enhances the PWA offline functionality by implementing PWA-only offline mode for driver-connect and improving QR data storage debugging capabilities. The solution involves service worker modifications, enhanced debug page functionality, and improved PWA mode detection.

## Architecture

### PWA Mode Detection System
```
Client App (PWA/Browser)
    ↓ (PWA mode detection)
usePWAStatus Hook
    ↓ (sends PWA mode status)
Service Worker
    ↓ (uses mode for routing decisions)
Offline Content Serving Logic
```

### QR Data Storage Inspection System
```
Debug Page
    ↓ (IndexedDB queries)
HaulingQROfflineDB
    ↓ (connectionQueue store)
Stored QR Connection Data
    ↓ (display/clear operations)
Debug Interface
```

## Components and Interfaces

### 0. Sync API Integration

**Endpoint**: `/api/driver/connect`
**Method**: POST

**Sync Process Flow:**
```javascript
async function syncOfflineData() {
  // 1. Get stored data from IndexedDB
  const storedConnections = await getAllStoredConnections();
  
  // 2. Send each connection to server
  for (const connection of storedConnections) {
    try {
      const response = await fetch('/api/driver/connect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(connection.apiPayload)
      });
      
      if (response.ok) {
        // 3. Remove successfully synced data
        await removeFromIndexedDB(connection.id);
      } else {
        // 4. Handle sync failure
        throw new Error(`Sync failed: ${response.statusText}`);
      }
    } catch (error) {
      // 5. Log error and continue with next item
      console.error('Sync error:', error);
    }
  }
}

### 1. Service Worker Enhancement

**File**: `client/public/sw.js`

**New Functions:**
- `detectPWAMode()` - Determines if running in PWA or browser mode
- Enhanced `handleNavigation()` - Only serves offline content in PWA mode

**PWA Mode Detection Logic:**
```javascript
// Global variable to track PWA mode from clients
let clientPWAMode = false;

// Message listener for PWA mode updates
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'PWA_MODE_STATUS') {
    clientPWAMode = event.data.isPWA;
  }
});

// Enhanced navigation handling
async function handleNavigation(request) {
  const isPWAMode = await detectPWAMode();
  const isPWARoute = ['/driver-connect', '/trip-scanner'].some(route => 
    url.pathname.startsWith(route)
  );
  
  // Only serve cached content for PWA routes when in PWA mode
  if (isPWARoute && isPWAMode) {
    return serveFromCache(request);
  }
  
  // Let browser handle normally (will show offline error)
  return fetch(request);
}
```

### 2. PWA Status Hook Enhancement

**File**: `client/src/hooks/usePWAStatus.js`

**Enhanced Functionality:**
- Detects PWA mode using multiple methods
- Sends PWA mode status to service worker
- Listens for service worker PWA mode requests

**PWA Detection Methods:**
```javascript
const detectPWA = () => {
  const isPWAMode = window.matchMedia('(display-mode: standalone)').matches ||
                   window.navigator.standalone === true ||
                   document.referrer.includes('android-app://');
  
  // Send to service worker
  if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
    navigator.serviceWorker.controller.postMessage({
      type: 'PWA_MODE_STATUS',
      isPWA: isPWAMode
    });
  }
  
  return isPWAMode;
};
```

### 3. Enhanced Debug Page

**File**: `client/public/debug-pwa.html`

**New QR Data Inspection Functions:**

**inspectQRData():**
- Opens HaulingQROfflineDB database
- Queries connectionQueue store
- Displays stored QR connections with full details
- Shows driver ID, truck ID, action, status, timestamp

**clearQRData():**
- Clears all data from connectionQueue store
- Provides confirmation of clearing operation
- Updates display to show empty state

**syncOfflineData():**
- Checks for stored QR data in IndexedDB
- Makes actual API calls to /api/driver/connect endpoint
- Sends stored connection data to server for processing
- Removes successfully synced data from IndexedDB
- Handles sync failures with error messages and retry options
- Provides real-time sync status updates

**testManualSync():**
- Checks for stored QR data
- Simulates sync process without actual API calls
- Shows count of items that would be synced
- Handles offline/online state appropriately

## Data Models

### QR Connection Storage Structure
```javascript
{
  id: 1, // Auto-generated
  apiPayload: {
    driver_qr_data: {
      id: "DR-001",
      employee_id: "DR-001",
      driver_id: 123,
      generated_date: "2025-01-30T10:00:00.000Z",
      type: "driver",
      checksum: "abc12345"
    },
    truck_qr_data: {
      id: "T001",
      type: "truck"
    },
    action: "check_in"
  },
  syncMetadata: {
    status: "pending",
    priority: 4,
    retryCount: 0,
    maxRetries: 3,
    timestamp: "2025-01-30T10:00:00.000Z",
    scheduledSync: "2025-01-30T10:00:00.000Z",
    dataIntegrity: true
  },
  status: "pending",
  action: "check_in",
  employeeId: "DR-001",
  truckId: "T001",
  priority: 4,
  timestamp: "2025-01-30T10:00:00.000Z"
}
```

### PWA Mode Status Message
```javascript
{
  type: 'PWA_MODE_STATUS',
  isPWA: boolean,
  currentPath: string,
  timestamp: string
}
```

## Error Handling

### Service Worker Error Handling
- Graceful fallback when PWA mode detection fails
- Proper error logging for debugging
- Default to browser mode if detection is uncertain

### IndexedDB Error Handling
- Handle database not found scenarios
- Manage store access errors
- Provide user-friendly error messages in debug interface

### Network Error Handling
- Detect online/offline state for sync operations
- Show appropriate messages for network-dependent operations
- Handle service worker communication failures

## Testing Strategy

### PWA Mode Testing
1. **Browser Mode Test**: Access driver-connect in regular browser tab, go offline, verify "site can't be reached" error
2. **PWA Mode Test**: Access driver-connect in PWA, go offline, verify cached page loads
3. **Mode Detection Test**: Verify PWA mode is correctly detected and communicated to service worker

### QR Data Storage Testing
1. **Storage Test**: Scan QR codes offline, verify data is stored in IndexedDB
2. **Inspection Test**: Use debug page to verify stored data is visible and accurate
3. **Clear Test**: Clear stored data, verify it's removed from IndexedDB
4. **Sync Test**: Test manual sync functionality with and without stored data

### Integration Testing
1. **End-to-End Offline Flow**: Complete driver-connect flow offline, verify all data is stored
2. **Online Sync Test**: Go online after offline usage, verify sync functionality works
3. **Cross-Mode Testing**: Test behavior switching between browser and PWA modes

## Performance Considerations

### Service Worker Performance
- Minimize PWA mode detection overhead
- Cache PWA mode status to avoid repeated detection
- Use efficient message passing between client and service worker

### IndexedDB Performance
- Use indexed queries for efficient data retrieval
- Implement proper transaction handling
- Minimize database open/close operations

### Debug Page Performance
- Lazy load debug functions to avoid impacting main app
- Efficient data display for large numbers of stored connections
- Proper memory management for debug operations

## Security Considerations

### Data Privacy
- Debug page should only be accessible in development/testing environments
- Sensitive QR data should be properly handled in debug displays
- Clear data functionality should require appropriate confirmation

### Service Worker Security
- Validate PWA mode messages to prevent spoofing
- Ensure offline content serving doesn't expose sensitive data
- Proper error handling to avoid information leakage

## Browser Compatibility

### PWA Mode Detection
- Support for `display-mode: standalone` media query (modern browsers)
- Fallback for iOS Safari using `navigator.standalone`
- Android app detection using document referrer

### IndexedDB Support
- Modern browser IndexedDB API support
- Proper error handling for unsupported browsers
- Graceful degradation when IndexedDB is unavailable

### Service Worker Support
- Modern browser service worker support
- Proper registration and update handling
- Message passing compatibility across browsers