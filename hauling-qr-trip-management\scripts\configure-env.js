#!/usr/bin/env node
/**
 * Environment Configuration Script
 * Configures the system for different modes: development/production with HTTP/HTTPS
 */

const { loadConfig, writeClientEnv, displayConfig } = require('../config-loader');
const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const environment = args[0] || 'development'; // development or production
const enableHttps = args[1] === 'true'; // true or false

if (!['development', 'production'].includes(environment)) {
  console.error('❌ Invalid environment. Use "development" or "production"');
  process.exit(1);
}

console.log(`🔧 Configuring environment for: ${environment} ${enableHttps ? '(HTTPS)' : '(HTTP)'}`);

// Update .env file with the specified configuration
const envPath = path.join(__dirname, '..', '.env');
let envContent = fs.readFileSync(envPath, 'utf8');

// Update NODE_ENV
envContent = envContent.replace(/^NODE_ENV=.*/m, `NODE_ENV=${environment}`);

// Update ENABLE_HTTPS
envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, `ENABLE_HTTPS=${enableHttps}`);

// Write updated .env file
fs.writeFileSync(envPath, envContent);

// Load the updated configuration
const config = loadConfig();

// Generate and write client environment
writeClientEnv(config);

// Display configuration summary
displayConfig(config);

console.log(`✅ Environment configured for ${environment} ${enableHttps ? '(HTTPS)' : '(HTTP)'}`);
console.log(`📝 Updated: .env and client/.env.local`);
console.log(`🚀 Ready to start with: npm run ${environment === 'development' ? 'dev' : 'prod'}${enableHttps ? ':https' : ''}`);
