import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../services/api';
import useWebSocket from '../hooks/useWebSocket';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export { AuthContext };

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [permissions, setPermissions] = useState({});
  const [loading, setLoading] = useState(true);

  // Initialize WebSocket connection for authenticated users
  const webSocket = useWebSocket(isAuthenticated ? user : null);

  useEffect(() => {
    // Check for existing token on app load
    const token = localStorage.getItem('hauling_token');
    const storedUser = localStorage.getItem('hauling_user');

    if (token && storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
        setIsAuthenticated(true);

        // Verify token is still valid with proper error handling
        authAPI.verifyToken()
          .then((result) => {
            if (!result.success) {
              // Token is invalid, clear everything silently
              localStorage.removeItem('hauling_token');
              localStorage.removeItem('hauling_user');
              setIsAuthenticated(false);
              setUser(null);
            }
          })
          .catch((error) => {
            // If verification fails (network error, etc.), don't clear the token
            // Just log the error and continue with the stored session
            console.warn('Token verification failed, continuing with stored session:', error);
          });
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        localStorage.removeItem('hauling_token');
        localStorage.removeItem('hauling_user');
      }
    }
    setLoading(false);
  }, []);
  const login = async (credentials) => {
    try {
      setLoading(true);
      const result = await authAPI.login(credentials);

      if (result.success) {
        setIsAuthenticated(true);
        setUser(result.data.user);
        setPermissions(result.data.permissions || {});
        toast.success('Login successful! Welcome back.');
        return { success: true };
      } else {
        // Handle specific error types
        if (result.status === 429) {
          toast.error('Too many login attempts. Please wait 15 minutes before trying again.');
          return { success: false, error: 'Rate limit exceeded. Please try again later.' };
        } else {
          toast.error(result.error || 'Login failed');
          return { success: false, error: result.error };
        }
      }
    } catch (error) {
      console.error('Login error:', error);

      // Handle rate limiting errors
      if (error.response?.status === 429) {
        const retryAfter = error.response.headers['retry-after'] || '15 minutes';
        toast.error(`Too many login attempts. Please try again after ${retryAfter}.`);
        return { success: false, error: 'Rate limit exceeded. Please try again later.' };
      }

      toast.error('An unexpected error occurred during login');
      return { success: false, error: 'An unexpected error occurred' };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      await authAPI.logout();
      setIsAuthenticated(false);
      setUser(null);
      setPermissions({});
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      console.error('Logout error:', error);
      // Still clear local state even if API call fails
      setIsAuthenticated(false);
      setUser(null);
      setPermissions({});
    } finally {
      setLoading(false);
    }
  };

  const updateUser = (userData) => {
    setUser(userData);
    localStorage.setItem('hauling_user', JSON.stringify(userData));
  };

  const updatePermissions = (newPermissions) => {
    setPermissions(newPermissions);
  };

  // Check if user has a specific permission
  const hasPermission = (pageKey) => {
    if (!user || !user.role) {
      return false;
    }

    // Admin always has all permissions
    if (user.role === 'admin') {
      return true;
    }

    // Check specific permission
    return permissions[pageKey] === true;
  };

  const value = {
    isAuthenticated,
    user,
    permissions,
    loading,
    login,
    logout,
    updateUser,
    updatePermissions,
    hasPermission,
    // WebSocket functionality
    webSocket: {
      isConnected: webSocket.isConnected,
      connectionStatus: webSocket.connectionStatus,
      notifications: webSocket.notifications,
      unreadCount: webSocket.unreadCount,
      clearNotifications: webSocket.clearNotifications,
      markNotificationAsRead: webSocket.markNotificationAsRead,
      reconnect: webSocket.reconnect
    }
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};