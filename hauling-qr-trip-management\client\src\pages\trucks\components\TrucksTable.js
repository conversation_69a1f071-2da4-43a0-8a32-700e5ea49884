import React from 'react';

const TrucksTable = ({
  trucks,
  loading,
  pagination,
  filters,
  onPageChange,
  onSort,
  onEdit,
  onDelete,
  onViewQR
}) => {
  // Sortable header component with modern styling
  const SortableHeader = ({ column, children, className = "" }) => {
    const isSorted = filters.sortBy === column;
    const isAsc = filters.sortOrder === 'asc';

    return (
      <th 
        scope="col" 
        className={`px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider cursor-pointer hover:bg-secondary-50 select-none transition-colors duration-150 ${className}`}
        onClick={() => onSort(column)}
      >
        <div className="flex items-center space-x-1">
          <span>{children}</span>
          <div className="flex flex-col">
            <svg 
              className={`w-3 h-3 ${isSorted && isAsc ? 'text-primary-600' : 'text-secondary-400'}`} 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            <svg 
              className={`w-3 h-3 -mt-1 ${isSorted && !isAsc ? 'text-primary-600' : 'text-secondary-400'}`} 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </th>
    );
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      'active': 'bg-success-100 text-success-800',
      'inactive': 'bg-secondary-100 text-secondary-800',
      'maintenance': 'bg-warning-100 text-warning-800',
      'retired': 'bg-danger-100 text-danger-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status] || 'bg-secondary-100 text-secondary-800'}`}>
        {status?.charAt(0).toUpperCase() + status?.slice(1)}
      </span>
    );
  };
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Enhanced pagination component
  const Pagination = () => {
    if (pagination.totalPages <= 1) return null;

    const getPageNumbers = () => {
      const pages = [];
      const { currentPage, totalPages } = pagination;
      const maxVisible = 5;

      if (totalPages <= maxVisible) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (currentPage <= 3) {
          for (let i = 1; i <= 4; i++) pages.push(i);
          pages.push('...');
          pages.push(totalPages);
        } else if (currentPage >= totalPages - 2) {
          pages.push(1);
          pages.push('...');
          for (let i = totalPages - 3; i <= totalPages; i++) pages.push(i);
        } else {
          pages.push(1);
          pages.push('...');
          for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
          pages.push('...');
          pages.push(totalPages);
        }
      }
      return pages;
    };

    return (
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-secondary-200 sm:px-6">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => onPageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage}
            className="relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => onPageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-secondary-700">
              Showing{' '}
              <span className="font-medium">
                {(pagination.currentPage - 1) * pagination.itemsPerPage + 1}
              </span>{' '}
              to{' '}
              <span className="font-medium">
                {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)}
              </span>{' '}
              of{' '}
              <span className="font-medium">{pagination.totalItems}</span>{' '}
              results
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={!pagination.hasPrevPage}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Previous</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>
              
              {getPageNumbers().map((page, index) => (
                page === '...' ? (
                  <span key={index} className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-700">
                    ...
                  </span>
                ) : (
                  <button
                    key={index}
                    onClick={() => onPageChange(page)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      page === pagination.currentPage
                        ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                        : 'bg-white border-secondary-300 text-secondary-500 hover:bg-secondary-50'
                    }`}
                  >
                    {page}
                  </button>
                )
              ))}
              
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={!pagination.hasNextPage}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Next</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="bg-white shadow-lg overflow-hidden sm:rounded-lg border border-secondary-200">
        <div className="px-4 py-5 sm:p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex space-x-4">
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="bg-white shadow-lg overflow-hidden sm:rounded-lg border border-secondary-200">
      {/* Table Header Info */}
      <div className="px-4 sm:px-6 py-4 border-b border-secondary-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-secondary-900">
              🚛 Trucks ({pagination.totalItems})
            </h3>
            <p className="text-sm text-secondary-500">
              Manage your fleet of trucks
            </p>
          </div>
          <div className="text-sm text-secondary-500">
            Page {pagination.currentPage} of {pagination.totalPages}
          </div>
        </div>
      </div>

      {/* Enhanced responsive table container */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-secondary-200">
          <thead className="bg-gradient-to-r from-secondary-50 to-secondary-100">
            <tr>
              <SortableHeader column="truck_number">
                <span className="hidden sm:inline">Truck </span>Number
              </SortableHeader>
              <SortableHeader column="license_plate" className="hidden md:table-cell">
                License Plate
              </SortableHeader>
              <SortableHeader column="make">
                <span className="hidden lg:inline">Make & </span>Model
              </SortableHeader>
              <SortableHeader column="year" className="hidden lg:table-cell">Year</SortableHeader>
              <SortableHeader column="capacity_tons" className="hidden xl:table-cell">Capacity</SortableHeader>
              <SortableHeader column="status">Status</SortableHeader>
              <SortableHeader column="created_at" className="hidden xl:table-cell">Created</SortableHeader>
              <th scope="col" className="px-4 sm:px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {trucks.length === 0 ? (
              <tr>
                <td colSpan="8" className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center">
                    <svg className="w-12 h-12 text-secondary-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                    </svg>
                    <h3 className="text-lg font-medium text-secondary-900 mb-2">No trucks found</h3>
                    <p className="text-secondary-500">
                      Get started by adding your first truck to the fleet
                    </p>
                  </div>
                </td>
              </tr>
            ) : (
              trucks.map((truck) => (
                <tr key={truck.id} className="hover:bg-secondary-50 transition-colors duration-150">
                  <td className="px-4 sm:px-6 py-4">
                    <div className="flex items-center">
                      <span className="text-2xl mr-2 sm:mr-3 flex-shrink-0">🚛</span>
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium text-secondary-900 truncate">
                          {truck.truck_number}
                        </div>
                        {/* Mobile: Show license plate here */}
                        <div className="md:hidden text-sm text-secondary-500 truncate">
                          {truck.license_plate}
                        </div>
                        {/* Mobile: Show capacity */}
                        <div className="xl:hidden mt-1 text-xs text-secondary-500">
                          {truck.capacity_tons ? `${truck.capacity_tons} tons` : 'Capacity: N/A'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="hidden md:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-900 truncate">{truck.license_plate}</div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="text-sm text-secondary-900 truncate">
                      {truck.make && truck.model ? `${truck.make} ${truck.model}` : truck.make || truck.model || '-'}
                    </div>
                    {/* Mobile: Show year here */}
                    <div className="lg:hidden mt-1 text-xs text-secondary-500">
                      Year: {truck.year || 'N/A'}
                    </div>
                    {/* Mobile: Show created date */}
                    <div className="xl:hidden mt-1 text-xs text-secondary-500">
                      Created: {formatDate(truck.created_at)}
                    </div>
                  </td>
                  <td className="hidden lg:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-900">{truck.year || '-'}</div>
                  </td>
                  <td className="hidden xl:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-900">
                      {truck.capacity_tons ? `${truck.capacity_tons} tons` : '-'}
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(truck.status)}
                  </td>
                  <td className="hidden xl:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-500">{formatDate(truck.created_at)}</div>
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => onViewQR(truck)}
                        className="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded-md hover:bg-indigo-50"
                        title="View QR Code"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => onEdit(truck)}
                        className="text-green-600 hover:text-green-900 transition-colors p-1 rounded-md hover:bg-green-50"
                        title="Edit Truck"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => onDelete(truck)}
                        className="text-red-600 hover:text-red-900 transition-colors p-1 rounded-md hover:bg-red-50"
                        title="Delete Truck"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      <Pagination />
    </div>
  );
};

export default TrucksTable;