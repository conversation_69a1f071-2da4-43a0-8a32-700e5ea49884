#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SYSTEM OPTIMIZATION ROLLBACK (DISABLED)
# =============================================================================
# DISABLED: Rollback functionality eliminated per user request
# This script has been disabled as backup file creation has been removed
# =============================================================================

echo "❌ ROLLBACK FUNCTIONALITY DISABLED"
echo "This script has been disabled because backup file creation has been eliminated."
echo "The deployment system now uses a simplified approach without backup/rollback functionality."
echo "If you need to restore system configuration, please use manual system administration."
exit 1

# ORIGINAL FUNCTIONALITY COMMENTED OUT BELOW:
# =============================================================================
# Safely rollback system optimizations to previous state
# Restores PostgreSQL, NGINX, PM2, and system configurations
# =============================================================================

set -euo pipefail

# Load shared configuration
readonly ROLLBACK_SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SHARED_CONFIG="${ROLLBACK_SCRIPT_DIR}/shared-config.sh"

if [[ -f "$SHARED_CONFIG" ]]; then
  source "$SHARED_CONFIG"
else
  echo "❌ ERROR: Shared configuration not found: $SHARED_CONFIG"
  exit 1
fi

# =============================================================================
# ROLLBACK CONFIGURATION
# =============================================================================

readonly BACKUP_BASE_DIR="/var/backups"
readonly ROLLBACK_LOG="/var/log/hauling-qr-rollback.log"

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

find_latest_backup() {
  local latest_backup
  latest_backup=$(find "$BACKUP_BASE_DIR" -maxdepth 1 -name "hauling-qr-optimization-*" -type d | sort -r | head -1)
  
  if [[ -z "$latest_backup" ]]; then
    log_error "❌ No optimization backup found in $BACKUP_BASE_DIR"
    return 1
  fi
  
  echo "$latest_backup"
}

list_available_backups() {
  log_info "📦 Available optimization backups:"
  
  local backups
  backups=$(find "$BACKUP_BASE_DIR" -maxdepth 1 -name "hauling-qr-optimization-*" -type d | sort -r)
  
  if [[ -z "$backups" ]]; then
    log_warning "⚠️ No optimization backups found"
    return 1
  fi
  
  local count=1
  while IFS= read -r backup; do
    local backup_name
    backup_name=$(basename "$backup")
    local backup_date
    backup_date=$(echo "$backup_name" | sed 's/hauling-qr-optimization-//' | sed 's/-/ /g' | sed 's/\([0-9]\{8\}\) \([0-9]\{6\}\)/\1 \2/')
    log_info "  $count. $backup_name (Created: $backup_date)"
    count=$((count + 1))
  done <<< "$backups"
  
  return 0
}

validate_backup_directory() {
  local backup_dir="$1"
  
  if [[ ! -d "$backup_dir" ]]; then
    log_error "❌ Backup directory not found: $backup_dir"
    return 1
  fi
  
  # Check for essential backup files
  local required_files=(
    "postgresql.conf"
    "nginx.conf"
    "sysctl.conf"
    "limits.conf"
  )
  
  local missing_files=()
  for file in "${required_files[@]}"; do
    if [[ ! -f "$backup_dir/$file" ]]; then
      missing_files+=("$file")
    fi
  done
  
  if [[ ${#missing_files[@]} -gt 0 ]]; then
    log_warning "⚠️ Some backup files are missing: ${missing_files[*]}"
    log_warning "⚠️ Rollback may be incomplete"
  fi
  
  log_success "✅ Backup directory validated: $backup_dir"
  return 0
}

# =============================================================================
# SERVICE MANAGEMENT
# =============================================================================

stop_services() {
  log_info "🛑 Stopping services for rollback..."
  
  # Stop PM2 processes
  if command -v pm2 >/dev/null 2>&1; then
    log_info "Stopping PM2 processes..."
    sudo -u "$UBUNTU_USER" pm2 stop all 2>/dev/null || true
    sudo -u "$UBUNTU_USER" pm2 kill 2>/dev/null || true
  fi
  
  # Stop NGINX
  if systemctl is-active --quiet nginx; then
    log_info "Stopping NGINX..."
    sudo systemctl stop nginx
  fi
  
  # Stop PostgreSQL
  if systemctl is-active --quiet postgresql; then
    log_info "Stopping PostgreSQL..."
    sudo systemctl stop postgresql
  fi
  
  log_success "✅ Services stopped"
}

start_services() {
  log_info "🚀 Starting services after rollback..."
  
  local service_errors=0
  
  # Start PostgreSQL
  log_info "Starting PostgreSQL..."
  if ! sudo systemctl start postgresql; then
    log_error "❌ Failed to start PostgreSQL"
    service_errors=$((service_errors + 1))
  else
    log_success "✅ PostgreSQL started"
  fi
  
  # Start NGINX
  log_info "Starting NGINX..."
  if ! sudo systemctl start nginx; then
    log_error "❌ Failed to start NGINX"
    service_errors=$((service_errors + 1))
  else
    log_success "✅ NGINX started"
  fi
  
  # Start PM2 (if ecosystem config exists)
  if [[ -f "$APP_DIR/ecosystem.config.js" ]]; then
    log_info "Starting PM2 application..."
    if ! sudo -u "$UBUNTU_USER" pm2 start "$APP_DIR/ecosystem.config.js"; then
      log_error "❌ Failed to start PM2 application"
      service_errors=$((service_errors + 1))
    else
      log_success "✅ PM2 application started"
    fi
  fi
  
  if [[ $service_errors -eq 0 ]]; then
    log_success "✅ All services started successfully"
    return 0
  else
    log_warning "⚠️ $service_errors service(s) failed to start"
    return 1
  fi
}

# =============================================================================
# CONFIGURATION ROLLBACK
# =============================================================================

rollback_postgresql() {
  local backup_dir="$1"
  
  log_info "🐘 Rolling back PostgreSQL configuration..."
  
  if [[ ! -f "$backup_dir/postgresql.conf" ]]; then
    log_warning "⚠️ PostgreSQL backup not found, skipping"
    return 0
  fi
  
  # Find PostgreSQL configuration directory
  local pg_config_dir
  pg_config_dir=$(sudo find /etc/postgresql -name "main" -type d | head -1)
  
  if [[ -z "$pg_config_dir" ]]; then
    log_error "❌ PostgreSQL configuration directory not found"
    return 1
  fi
  
  # Restore postgresql.conf
  sudo cp "$backup_dir/postgresql.conf" "$pg_config_dir/"
  
  # Restore pg_hba.conf if available
  if [[ -f "$backup_dir/pg_hba.conf" ]]; then
    sudo cp "$backup_dir/pg_hba.conf" "$pg_config_dir/"
  fi
  
  # Set proper ownership
  sudo chown postgres:postgres "$pg_config_dir/postgresql.conf"
  if [[ -f "$pg_config_dir/pg_hba.conf" ]]; then
    sudo chown postgres:postgres "$pg_config_dir/pg_hba.conf"
  fi
  
  log_success "✅ PostgreSQL configuration restored"
}

rollback_nginx() {
  local backup_dir="$1"
  
  log_info "🌐 Rolling back NGINX configuration..."
  
  if [[ ! -f "$backup_dir/nginx.conf" ]]; then
    log_warning "⚠️ NGINX backup not found, skipping"
    return 0
  fi
  
  # Restore nginx.conf
  sudo cp "$backup_dir/nginx.conf" /etc/nginx/
  
  # Test configuration
  if ! sudo nginx -t; then
    log_error "❌ Restored NGINX configuration has syntax errors"
    return 1
  fi
  
  log_success "✅ NGINX configuration restored"
}

rollback_pm2() {
  local backup_dir="$1"
  
  log_info "⚡ Rolling back PM2 configuration..."
  
  if [[ ! -f "$backup_dir/ecosystem.config.js" ]]; then
    log_warning "⚠️ PM2 ecosystem backup not found, skipping"
    return 0
  fi
  
  # Restore ecosystem.config.js
  sudo cp "$backup_dir/ecosystem.config.js" "$APP_DIR/"
  sudo chown "$UBUNTU_USER:$UBUNTU_USER" "$APP_DIR/ecosystem.config.js"
  
  log_success "✅ PM2 configuration restored"
}

rollback_system_parameters() {
  local backup_dir="$1"
  
  log_info "🔧 Rolling back system parameters..."
  
  # Restore sysctl.conf
  if [[ -f "$backup_dir/sysctl.conf" ]]; then
    sudo cp "$backup_dir/sysctl.conf" /etc/
    sudo sysctl -p
    log_success "✅ Kernel parameters restored"
  else
    log_warning "⚠️ sysctl.conf backup not found, skipping"
  fi
  
  # Restore limits.conf
  if [[ -f "$backup_dir/limits.conf" ]]; then
    sudo cp "$backup_dir/limits.conf" /etc/security/
    log_success "✅ User limits restored"
  else
    log_warning "⚠️ limits.conf backup not found, skipping"
  fi
  
  # Reset transparent huge pages (if they were disabled)
  if [[ -f /sys/kernel/mm/transparent_hugepage/enabled ]]; then
    echo madvise | sudo tee /sys/kernel/mm/transparent_hugepage/enabled > /dev/null
    echo madvise | sudo tee /sys/kernel/mm/transparent_hugepage/defrag > /dev/null
    log_info "📝 Transparent huge pages reset to default (madvise)"
  fi
}

# =============================================================================
# VALIDATION AFTER ROLLBACK
# =============================================================================

validate_rollback() {
  log_info "🔍 Validating rollback..."
  
  local validation_errors=0
  
  # Check PostgreSQL
  if systemctl is-active --quiet postgresql; then
    if sudo -u postgres psql -c "SELECT 1;" >/dev/null 2>&1; then
      log_success "✅ PostgreSQL is running and accessible"
    else
      log_error "❌ PostgreSQL is running but not accessible"
      validation_errors=$((validation_errors + 1))
    fi
  else
    log_error "❌ PostgreSQL is not running"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check NGINX
  if systemctl is-active --quiet nginx; then
    if sudo nginx -t >/dev/null 2>&1; then
      log_success "✅ NGINX is running with valid configuration"
    else
      log_error "❌ NGINX configuration has errors"
      validation_errors=$((validation_errors + 1))
    fi
  else
    log_error "❌ NGINX is not running"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check PM2
  if command -v pm2 >/dev/null 2>&1; then
    local pm2_status
    pm2_status=$(sudo -u "$UBUNTU_USER" pm2 list 2>/dev/null | grep -c "online" || echo "0")
    if [[ $pm2_status -gt 0 ]]; then
      log_success "✅ PM2 has $pm2_status running process(es)"
    else
      log_warning "⚠️ No PM2 processes are running"
    fi
  else
    log_warning "⚠️ PM2 not found"
  fi
  
  if [[ $validation_errors -eq 0 ]]; then
    log_success "✅ Rollback validation passed"
    return 0
  else
    log_error "❌ Rollback validation failed with $validation_errors error(s)"
    return 1
  fi
}

# =============================================================================
# MAIN ROLLBACK FUNCTION
# =============================================================================

perform_rollback() {
  local backup_dir="$1"
  
  log_info "🔄 Starting system optimization rollback"
  log_info "📦 Using backup: $backup_dir"
  
  # Validate backup directory
  if ! validate_backup_directory "$backup_dir"; then
    return 1
  fi
  
  # Stop services
  if ! stop_services; then
    log_error "❌ Failed to stop services"
    return 1
  fi
  
  # Perform rollback
  rollback_postgresql "$backup_dir"
  rollback_nginx "$backup_dir"
  rollback_pm2 "$backup_dir"
  rollback_system_parameters "$backup_dir"
  
  # Start services
  if ! start_services; then
    log_error "❌ Failed to start services after rollback"
    return 1
  fi
  
  # Validate rollback
  if ! validate_rollback; then
    log_error "❌ Rollback validation failed"
    return 1
  fi
  
  log_success "✅ System optimization rollback completed successfully"
  return 0
}

# =============================================================================
# MAIN FUNCTION
# =============================================================================

main() {
  log_info "🔄 Hauling QR Trip System - Optimization Rollback"
  log_info "📅 Started at: $(date)"
  log_info "📝 Log file: $ROLLBACK_LOG"
  
  # Check root privileges
  if [[ $EUID -ne 0 ]]; then
    log_error "❌ This script must be run as root (use sudo)"
    exit 1
  fi
  
  # Parse command line arguments
  local backup_dir=""
  local list_backups=false
  local auto_latest=false
  
  while [[ $# -gt 0 ]]; do
    case $1 in
      --backup-dir)
        backup_dir="$2"
        shift 2
        ;;
      --list)
        list_backups=true
        shift
        ;;
      --latest)
        auto_latest=true
        shift
        ;;
      --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo "Options:"
        echo "  --backup-dir DIR    Use specific backup directory"
        echo "  --latest           Use latest backup automatically"
        echo "  --list             List available backups"
        echo "  --help             Show this help message"
        exit 0
        ;;
      *)
        log_error "❌ Unknown option: $1"
        exit 1
        ;;
    esac
  done
  
  # List backups if requested
  if [[ "$list_backups" == true ]]; then
    list_available_backups
    exit 0
  fi
  
  # Determine backup directory
  if [[ -z "$backup_dir" ]]; then
    if [[ "$auto_latest" == true ]]; then
      backup_dir=$(find_latest_backup)
      if [[ $? -ne 0 ]]; then
        exit 1
      fi
    else
      log_error "❌ No backup directory specified"
      log_info "Use --backup-dir DIR, --latest, or --list to see available backups"
      exit 1
    fi
  fi
  
  # Confirm rollback
  log_warning "⚠️ This will rollback system optimizations to previous state"
  log_warning "⚠️ Backup directory: $backup_dir"
  read -p "Continue with rollback? (y/N): " -n 1 -r
  echo
  
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "Rollback cancelled by user"
    exit 0
  fi
  
  # Perform rollback
  if perform_rollback "$backup_dir"; then
    log_success "✅ System optimization rollback completed successfully"
    log_info "🔍 Run validate-system-optimization.sh to verify system state"
    exit 0
  else
    log_error "❌ System optimization rollback failed"
    exit 1
  fi
}

# Execute main function
main "$@"
