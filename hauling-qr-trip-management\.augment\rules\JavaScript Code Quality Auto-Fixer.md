---
type: "always_apply"
description: "Automatically fixes common JavaScript/TypeScript code quality issues from IDE diagnostics and linting output, including unused variables, imports, function parameters, and declaration issues while preserving functionality"
---
# JavaScript/TypeScript Code Quality Auto-Fixer

Automatically analyze and fix common JavaScript/TypeScript code quality issues that appear in IDE diagnostics and linting output for the Hauling QR Trip System. This rule provides automated fixes while maintaining code functionality and following React/Express.js best practices.

## 🔍 CRITICAL: Cross-Reference Analysis Required

**BEFORE removing ANY declared variables, imports, or exports, perform comprehensive cross-reference analysis:**

### 1. Cross-File Dependency Check
Scan the entire codebase to verify if the variable/function/import is referenced in other files through:
- **Direct imports**: `import { functionName } from './file'`, `import functionName from './file'`
- **Dynamic imports**: `import('./file').then(...)`, `const module = await import('./file')`
- **Require statements**: `const { functionName } = require('./file')`, `const functionName = require('./file')`
- **String-based references**: Template literals, configuration objects, dynamic property access

### 2. Export Usage Validation
Before removing any declared item, verify it's not exported and used elsewhere by:
- **Module exports**: `module.exports = functionName`, `module.exports.functionName = functionName`, `exports.functionName = functionName`
- **ES6 exports**: `export function functionName()`, `export { functionName }`, `export default functionName`
- **Re-export patterns**: `export * from './file'`, `export { functionName } from './file'`
- **Dynamic exports**: `module.exports[variableName] = value`

### 3. AI-Generated Code Preservation Logic
Since the original code was AI-generated, apply extra caution by:
- **Flagging AI patterns**: Descriptive names, utility functions, scaffolding code
- **Preserving framework setup**: Configuration objects, initialization code, middleware setup
- **Maintaining extensibility**: Variables that might be used for future features
- **Adding preservation comments**: `// Preserved: Potential cross-file usage` instead of removal when uncertain

### 4. Conflict Prevention Strategy
- **Dry-run analysis**: Show what would be removed before making changes
- **Dependency mapping**: Create a dependency map of the file being modified
- **Import/export chain verification**: Ensure removal won't break dependency chains
- **Compilation testing**: Test compilation after each removal to catch immediate errors

### 5. Safe Removal Criteria
Only remove variables/imports/exports when ALL conditions are met:
- ✅ Not referenced anywhere in the codebase (including dynamic references)
- ✅ Not exported from the current file
- ✅ Not part of a public API or interface
- ✅ Not used in configuration objects, arrays, or computed properties
- ✅ Removal doesn't break any existing functionality
- ✅ Cross-file dependency scan shows no usage

**⚠️ RULE: When in doubt, preserve the code with underscore prefix or explanatory comment rather than removing entirely.**

## 1. Unused Variable Issues

### Variables "assigned a value but never used"
**Detection**: Variables declared and assigned but never referenced
**Action**: 
- Remove completely unused variables that don't affect functionality
- For variables that might be needed for debugging, add `// eslint-disable-next-line no-unused-vars` comment
- For destructured variables, replace with underscore: `const { used, _unused } = object`

**Examples**:
```javascript
// BEFORE (Issue)
const unusedVar = calculateSomething();
const { data, metadata } = response; // metadata never used

// AFTER (Fixed)
const { data, _metadata } = response; // Prefix with underscore
// Remove unusedVar entirely if truly unused
```

### Variables "declared but never read"
**Detection**: Variables declared with `let`/`const`/`var` but never accessed
**Action**:
- Remove declaration entirely if variable serves no purpose
- Convert to `const` if variable is assigned once and could be used
- Add underscore prefix for intentionally unused variables

### Imported modules never referenced
**Detection**: Import statements for modules/functions never used in file
**Action**:
- **CRITICAL**: Before removal, scan entire codebase for cross-file usage
- Check if import is re-exported: `export { debounce } from '../utils/debounce'`
- Verify not used in dynamic references: `this[functionName]()`, `window[functionName]`
- Remove unused imports only after comprehensive dependency analysis
- For type-only imports in TypeScript, ensure `import type` is used
- Preserve imports that might be used dynamically or in JSX

**Cross-Reference Analysis Required**:
```javascript
// BEFORE (Issue) - MUST verify cross-file usage first
import React, { useState, useEffect, useMemo } from 'react'; // useMemo never used
import { debounce } from '../utils/debounce'; // debounce never used

// STEP 1: Check if debounce is exported elsewhere
// STEP 2: Scan for dynamic usage patterns
// STEP 3: Verify no string-based references

// AFTER (Fixed) - Only if cross-reference analysis confirms safe removal
import React, { useState, useEffect } from 'react';
// Remove unused debounce import ONLY after verification
```

### Function parameters unused
**Detection**: Function parameters declared but never referenced in function body
**Action**:
- Add underscore prefix: `function handler(_unusedParam, usedParam)`
- For React components, preserve props even if unused (might be passed down)
- For Express.js routes, preserve `req`, `res`, `next` parameters with underscore if unused

**Examples**:
```javascript
// BEFORE (Issue)
function processData(data, options, callback) {
  return data.map(item => item.value); // options and callback unused
}

// AFTER (Fixed)  
function processData(data, _options, _callback) {
  return data.map(item => item.value);
}
```

## 2. Declaration Issues

### Variables declared without proper initialization
**Detection**: Variables declared but not initialized when they should be
**Action**:
- Initialize with appropriate default values
- Convert to `const` if value never changes
- Use proper TypeScript types if applicable

### Redundant variable declarations
**Detection**: Variables declared multiple times or unnecessarily
**Action**:
- Remove duplicate declarations
- Consolidate related declarations
- Use destructuring where appropriate

### Variables that could be constants
**Detection**: Variables declared with `let` but never reassigned
**Action**:
- Convert `let` to `const` for immutable variables
- Preserve `let` only when variable is actually reassigned

**Examples**:
```javascript
// BEFORE (Issue)
let apiUrl = process.env.REACT_APP_API_URL; // Never reassigned
let data = [];
data = await fetchData(); // Could use const with direct assignment

// AFTER (Fixed)
const apiUrl = process.env.REACT_APP_API_URL;
const data = await fetchData();
```

## 3. Hauling QR Trip System Specific Patterns

### React Components
- Preserve all props even if unused (component API consistency)
- Keep state variables that might be used in JSX conditionally
- Maintain useEffect dependencies even if they seem unused

### Express.js Routes
- Always preserve `req`, `res`, `next` parameters (use underscore prefix if unused)
- Keep middleware function parameters intact
- Preserve database query variables that might be used in error handling

### Database Operations
- Keep transaction variables even if they seem unused (rollback scenarios)
- Preserve query result variables that might be used in error logging
- Maintain connection variables for proper cleanup

## 4. Enhanced Safety Considerations

### Never Remove These Variables:
- Variables used in `eval()` or dynamic code execution
- Variables referenced in template literals or string interpolation
- Variables that are part of public APIs or exports
- Variables used in JSX expressions (even if linter doesn't detect)
- Variables used in error handling or logging
- Variables used in cleanup functions (useEffect cleanup, event listeners)

### Dynamic Usage Patterns:
- Variables accessed via bracket notation: `object[variableName]`
- Variables used in computed property names
- Variables referenced in configuration objects or arrays
- Variables used in conditional rendering or computed styles

### Preserve for Debugging:
- Variables that provide useful debugging information
- Variables used in development-only code blocks
- Variables that help with code readability even if unused

### Cross-File Usage Patterns to Check:
- **Route definitions**: `app.use('/api/path', require('./routes/file'))`
- **Middleware chains**: Functions passed to Express.js middleware
- **React component props**: Props passed down to child components
- **Service exports**: Functions exported from service files
- **Utility functions**: Helper functions used across multiple files
- **Configuration objects**: Settings and config files that reference functions
- **Event handlers**: Functions used in event listener registrations
- **Database operations**: Query functions and transaction handlers

### Hauling QR Trip System Specific Preservation:
- **Trip workflow functions**: Any function related to 4-phase trip progression
- **QR code processing**: Functions handling QR validation and scanning
- **Assignment management**: Functions for AutoAssignmentCreator and dynamic assignments
- **Driver shift management**: Functions for shift handovers and time tracking
- **WebSocket handlers**: Real-time communication functions
- **PWA service workers**: Offline functionality and background sync
- **Database migrations**: Schema and data migration functions

## 5. Implementation Process

### Analysis Phase:
1. Parse IDE diagnostics and linting output for specific error messages
2. Identify the exact line numbers and variable names
3. Analyze variable usage patterns throughout the file
4. Check for dynamic usage patterns that linters might miss

### Safety Checks:
1. Verify variable is not used in JSX expressions
2. Check for usage in template literals or string concatenation
3. Ensure variable is not part of destructuring that affects other variables
4. Confirm variable is not used in error handling or cleanup

### Automated Fixes:
1. Remove truly unused imports and variables
2. Add underscore prefixes for intentionally unused parameters
3. Convert `let` to `const` where appropriate
4. Consolidate redundant declarations

### Verification:
1. Ensure code still compiles and runs
2. Verify no breaking changes to functionality
3. Check that all tests still pass
4. Confirm no runtime errors introduced

## 6. Error Message Patterns

### Common IDE Messages to Fix:
- `'variableName' is assigned a value but never used`
- `'variableName' is declared but its value is never read`
- `'moduleName' is defined but never used`
- `'parameter' is declared but its value is never read`
- `Variable 'variableName' is declared but never used`

### TypeScript Specific:
- `'TypeName' is defined but never used`
- `Parameter 'paramName' implicitly has an 'any' type`
- `Variable 'varName' is used before being assigned`

This rule ensures code quality improvements while maintaining the robust functionality of the Hauling QR Trip System's React frontend, Express.js backend, and database operations.
