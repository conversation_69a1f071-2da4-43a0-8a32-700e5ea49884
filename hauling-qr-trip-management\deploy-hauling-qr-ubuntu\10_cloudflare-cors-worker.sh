#!/bin/bash

# =============================================================================
# CLOUDFLARE CORS WORKER DEPLOYMENT - PHASE 10.5
# =============================================================================
# Version: 1.0.0
# Description: Deploy Cloudflare Worker to fix CORS preflight issues
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# Purpose: Fix CORS preflight OPTIONS requests intercepted by Cloudflare
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION AND INITIALIZATION
# =============================================================================
readonly SCRIPT_NAME="$(basename "${BASH_SOURCE[0]}")"
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly LOG_PREFIX="[CLOUDFLARE-CORS-WORKER]"

# Load shared configuration
readonly SHARED_CONFIG="${SCRIPT_DIR}/shared-config.sh"
if [[ -f "$SHARED_CONFIG" ]]; then
  source "$SHARED_CONFIG"
else
  echo "❌ ERROR: Shared configuration not found: $SHARED_CONFIG"
  exit 1
fi

# =============================================================================
# CLOUDFLARE WORKER CONFIGURATION
# =============================================================================
readonly WORKER_NAME="hauling-qr-cors-worker"
readonly WORKER_SUBDOMAIN="api"
readonly PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"
readonly FRONTEND_DOMAIN="https://${PRODUCTION_DOMAIN}"
readonly API_DOMAIN="https://${WORKER_SUBDOMAIN}.${PRODUCTION_DOMAIN}"

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
log_info() {
  echo -e "${GREEN}${LOG_PREFIX} $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
  echo -e "${YELLOW}${LOG_PREFIX} $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
  echo -e "${RED}${LOG_PREFIX} $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
  echo -e "${GREEN}${LOG_PREFIX} ✅ $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# CLOUDFLARE WORKER JAVASCRIPT CODE
# =============================================================================
generate_worker_code() {
  cat << 'EOF'
/**
 * Hauling QR Trip System - CORS Worker
 * Fixes CORS preflight issues for api.truckhaul.top
 * Version: 1.0.0
 */

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

async function handleRequest(request) {
  const origin = request.headers.get('Origin');
  const method = request.method;
  
  // Define allowed origins (specific origins only, no wildcards with credentials)
  const allowedOrigins = [
    'https://truckhaul.top',
    'https://www.truckhaul.top'
  ];
  
  // CORS headers for all responses
  const corsHeaders = {
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token, DNT, User-Agent, If-Modified-Since, Range',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '1728000',
    'Access-Control-Expose-Headers': 'Authorization, Content-Length, X-Requested-With'
  };
  
  // Check if origin is allowed
  if (origin && allowedOrigins.includes(origin)) {
    corsHeaders['Access-Control-Allow-Origin'] = origin;
  } else {
    // Default to main domain if origin not recognized
    corsHeaders['Access-Control-Allow-Origin'] = 'https://truckhaul.top';
  }
  
  // Handle preflight OPTIONS requests
  if (method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  
  // Forward all other requests to origin server
  const response = await fetch(request);
  
  // Create new response with CORS headers
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers
  });
  
  // Add CORS headers to response
  Object.entries(corsHeaders).forEach(([key, value]) => {
    newResponse.headers.set(key, value);
  });
  
  return newResponse;
}
EOF
}

# =============================================================================
# WORKER DEPLOYMENT FUNCTIONS
# =============================================================================
create_worker_files() {
  log_info "📝 Creating Cloudflare Worker files..."
  
  # Create worker directory
  local worker_dir="/tmp/hauling-qr-cors-worker"
  mkdir -p "$worker_dir"
  
  # Generate worker script
  generate_worker_code > "$worker_dir/worker.js"
  
  # Create wrangler.toml configuration
  cat << EOF > "$worker_dir/wrangler.toml"
name = "$WORKER_NAME"
main = "worker.js"
compatibility_date = "2024-01-01"

[[routes]]
pattern = "${WORKER_SUBDOMAIN}.${PRODUCTION_DOMAIN}/*"
zone_name = "${PRODUCTION_DOMAIN}"
EOF
  
  # Create package.json for dependencies
  cat << EOF > "$worker_dir/package.json"
{
  "name": "$WORKER_NAME",
  "version": "1.0.0",
  "description": "CORS Worker for Hauling QR Trip System",
  "main": "worker.js",
  "scripts": {
    "deploy": "wrangler deploy"
  },
  "devDependencies": {
    "@cloudflare/workers-types": "^4.20240129.0"
  }
}
EOF
  
  log_success "Worker files created in $worker_dir"
  echo "$worker_dir"
}

# =============================================================================
# DEPLOYMENT INSTRUCTIONS
# =============================================================================
show_deployment_instructions() {
  local worker_dir="$1"
  
  log_info "📋 CLOUDFLARE WORKER DEPLOYMENT INSTRUCTIONS"
  echo ""
  echo "=============================================================================="
  echo "🚀 MANUAL CLOUDFLARE WORKER DEPLOYMENT REQUIRED"
  echo "=============================================================================="
  echo ""
  echo "The Cloudflare Worker files have been created and are ready for deployment."
  echo "Due to API key security, manual deployment is required."
  echo ""
  echo "📁 Worker files location: $worker_dir"
  echo ""
  echo "🔧 DEPLOYMENT STEPS:"
  echo ""
  echo "1. Install Wrangler CLI (if not already installed):"
  echo "   npm install -g wrangler"
  echo ""
  echo "2. Authenticate with Cloudflare:"
  echo "   wrangler auth login"
  echo ""
  echo "3. Navigate to worker directory:"
  echo "   cd $worker_dir"
  echo ""
  echo "4. Deploy the worker:"
  echo "   wrangler deploy"
  echo ""
  echo "5. Verify the worker is active:"
  echo "   curl -I -H \"Origin: https://${PRODUCTION_DOMAIN}\" -X OPTIONS https://${WORKER_SUBDOMAIN}.${PRODUCTION_DOMAIN}/api/auth/login"
  echo ""
  echo "✅ EXPECTED RESULT:"
  echo "   HTTP/2 204"
  echo "   Access-Control-Allow-Origin: https://${PRODUCTION_DOMAIN}"
  echo "   Access-Control-Allow-Credentials: true"
  echo "   Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD"
  echo ""
  echo "=============================================================================="
  echo ""
}

# =============================================================================
# VERIFICATION FUNCTIONS
# =============================================================================
test_cors_preflight() {
  log_info "🧪 Testing CORS preflight after deployment..."
  
  local test_url="https://${WORKER_SUBDOMAIN}.${PRODUCTION_DOMAIN}/api/auth/login"
  local origin="https://${PRODUCTION_DOMAIN}"
  
  echo ""
  echo "Testing preflight request:"
  echo "URL: $test_url"
  echo "Origin: $origin"
  echo ""
  
  # Test preflight request
  local response
  if response=$(curl -s -I -H "Origin: $origin" -X OPTIONS "$test_url" 2>/dev/null); then
    echo "Response headers:"
    echo "$response"
    echo ""
    
    # Check for required headers
    if echo "$response" | grep -q "Access-Control-Allow-Origin"; then
      log_success "✅ Access-Control-Allow-Origin header present"
    else
      log_error "❌ Access-Control-Allow-Origin header missing"
    fi
    
    if echo "$response" | grep -q "Access-Control-Allow-Credentials"; then
      log_success "✅ Access-Control-Allow-Credentials header present"
    else
      log_error "❌ Access-Control-Allow-Credentials header missing"
    fi
    
    if echo "$response" | grep -q "Access-Control-Allow-Methods"; then
      log_success "✅ Access-Control-Allow-Methods header present"
    else
      log_error "❌ Access-Control-Allow-Methods header missing"
    fi
  else
    log_error "❌ Failed to test preflight request"
    return 1
  fi
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
  log_info "🚀 Starting Cloudflare CORS Worker deployment preparation..."
  
  # Create worker files
  local worker_dir
  worker_dir=$(create_worker_files)
  
  # Show deployment instructions
  show_deployment_instructions "$worker_dir"
  
  # Provide testing command
  echo "🧪 TESTING COMMAND (run after manual deployment):"
  echo "   bash $0 --test"
  echo ""
  
  log_success "✅ Cloudflare CORS Worker preparation completed"
  log_info "📝 Manual deployment required - follow instructions above"
  
  return 0
}

# =============================================================================
# COMMAND LINE HANDLING
# =============================================================================
case "${1:-}" in
  --test)
    test_cors_preflight
    ;;
  *)
    main "$@"
    ;;
esac
