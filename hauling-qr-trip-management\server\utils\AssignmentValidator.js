/**
 * Assignment Validator Service
 * 
 * This module provides centralized assignment validation logic for the
 * Hauling QR Trip System. It ensures consistent validation across all
 * components while maintaining the strict admin-based assignment workflow.
 * 
 * Key Features:
 * - Centralized assignment validation logic
 * - Consistent error handling and messaging
 * - Integration with exception handling system
 * - Performance optimized queries
 * - Maintains audit trail for validation decisions
 */

const { getClient } = require('../config/database');
const { logger } = require('./logger');

/**
 * Assignment Validation Results
 */
const VALIDATION_RESULTS = {
  VALID: 'valid',
  NO_ASSIGNMENT: 'no_assignment',
  WRONG_LOCATION: 'wrong_location',
  INACTIVE_ASSIGNMENT: 'inactive_assignment',
  EXPIRED_ASSIGNMENT: 'expired_assignment'
};

/**
 * Assignment Validator Class
 */
class AssignmentValidator {
  constructor() {
    this.logger = logger;
  }

  /**
   * Validate truck assignment at a specific location
   * @param {Object} params - Validation parameters
   * @param {string} params.truckNumber - Truck number (e.g., "DT-100")
   * @param {number} params.locationId - Location ID where truck was scanned
   * @param {Date} params.scanDate - Date of the scan (optional, defaults to today)
   * @param {Object} params.client - Database client (optional)
   * @returns {Promise<Object>} Validation result
   */
  async validateTruckAssignment(params) {
    const {
      truckNumber,
      locationId,
      scanDate = new Date(),
      client: providedClient
    } = params;

    const client = providedClient || await getClient();
    const shouldRelease = !providedClient;

    try {
      // Get truck information
      const truckResult = await client.query(`
        SELECT id, truck_number, license_plate, status
        FROM dump_trucks
        WHERE truck_number = $1
      `, [truckNumber]);

      if (truckResult.rows.length === 0) {
        return {
          isValid: false,
          result: VALIDATION_RESULTS.NO_ASSIGNMENT,
          message: `Truck ${truckNumber} not found in system`,
          truck: null,
          assignment: null,
          location: null
        };
      }

      const truck = truckResult.rows[0];

      if (truck.status !== 'active') {
        return {
          isValid: false,
          result: VALIDATION_RESULTS.INACTIVE_ASSIGNMENT,
          message: `Truck ${truckNumber} is not active`,
          truck,
          assignment: null,
          location: null
        };
      }

      // Get location information
      const locationResult = await client.query(`
        SELECT id, location_code, name, type, status
        FROM locations
        WHERE id = $1
      `, [locationId]);

      if (locationResult.rows.length === 0) {
        return {
          isValid: false,
          result: VALIDATION_RESULTS.NO_ASSIGNMENT,
          message: `Location not found`,
          truck,
          assignment: null,
          location: null
        };
      }

      const location = locationResult.rows[0];

      if (location.status !== 'active') {
        return {
          isValid: false,
          result: VALIDATION_RESULTS.INACTIVE_ASSIGNMENT,
          message: `Location ${location.name} is not active`,
          truck,
          assignment: null,
          location
        };
      }

      // Look for ANY assignment where this location is either loading OR unloading location
      // This supports the requirement that trucks can return to any previously assigned location
      const assignmentAtLocationResult = await client.query(`
        SELECT
          a.*,
          ll.name as loading_location,
          ul.name as unloading_location,
          d.full_name as driver_name,
          d.employee_id as driver_employee_id,
          CASE
            WHEN a.loading_location_id = $2 THEN 'loading'
            WHEN a.unloading_location_id = $2 THEN 'unloading'
            ELSE 'unknown'
          END as location_role
        FROM assignments a
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
        JOIN drivers d ON a.driver_id = d.id
        WHERE a.truck_id = $1
          AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
          AND a.status IN ('assigned', 'in_progress')
        ORDER BY a.created_at DESC
        LIMIT 1
      `, [truck.id, locationId]);

      if (assignmentAtLocationResult.rows.length > 0) {
        // Found assignment where this location is either loading or unloading
        const assignment = assignmentAtLocationResult.rows[0];

        this.logger.info('Assignment validation successful', {
          truck_number: truckNumber,
          location_name: location.name,
          location_role: assignment.location_role,
          assignment_id: assignment.id,
          assignment_code: assignment.assignment_code
        });

        return {
          isValid: true,
          result: VALIDATION_RESULTS.VALID,
          message: `Valid assignment found for truck ${truckNumber} at ${location.name} (${assignment.location_role} location)`,
          truck,
          assignment,
          location,
          locationRole: assignment.location_role
        };
      }

      // Check if truck has any assignment today (for route deviation detection)
      const anyAssignmentTodayResult = await client.query(`
        SELECT 
          a.*,
          ll.name as loading_location,
          ul.name as unloading_location,
          d.full_name as driver_name,
          d.employee_id as driver_employee_id
        FROM assignments a
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
        JOIN drivers d ON a.driver_id = d.id
        WHERE a.truck_id = $1 
          AND a.status IN ('assigned', 'in_progress')
        ORDER BY a.created_at DESC
        LIMIT 1
      `, [truck.id]);

      if (anyAssignmentTodayResult.rows.length > 0) {
        // Truck has assignment but at different location - route deviation
        const assignment = anyAssignmentTodayResult.rows[0];
        
        this.logger.info('Route deviation detected', {
          truck_number: truckNumber,
          expected_location: assignment.loading_location,
          actual_location: location.name,
          assignment_id: assignment.id
        });

        return {
          isValid: false,
          result: VALIDATION_RESULTS.WRONG_LOCATION,
          message: `Route deviation: Truck ${truckNumber} is assigned to ${assignment.loading_location} but scanned at ${location.name}`,
          truck,
          assignment,
          location,
          expectedLocation: {
            id: assignment.loading_location_id,
            name: assignment.loading_location
          }
        };
      }

      // No assignment found for this truck
      this.logger.info('No assignment found', {
        truck_number: truckNumber,
        location_name: location.name
      });

      return {
        isValid: false,
        result: VALIDATION_RESULTS.NO_ASSIGNMENT,
        message: `No assignment found for truck ${truckNumber}. Please contact administrator to create assignment.`,
        truck,
        assignment: null,
        location
      };

    } catch (error) {
      this.logger.error('Assignment validation failed', {
        error: error.message,
        truck_number: truckNumber,
        location_id: locationId
      });
      throw error;
    } finally {
      if (shouldRelease && client) {
        client.release();
      }
    }
  }

  /**
   * Get current trip for assignment
   * @param {Object} params - Parameters
   * @param {number} params.assignmentId - Assignment ID
   * @param {Object} params.client - Database client (optional)
   * @returns {Promise<Object>} Current trip information
   */
  async getCurrentTrip(params) {
    const {
      assignmentId,
      client: providedClient
    } = params;

    const client = providedClient || await getClient();
    const shouldRelease = !providedClient;

    try {
      const tripResult = await client.query(`
        SELECT *
        FROM trip_logs
        WHERE assignment_id = $1 
          AND status NOT IN ('trip_completed', 'cancelled')
        ORDER BY created_at DESC
        LIMIT 1
      `, [assignmentId]);

      if (tripResult.rows.length === 0) {
        return {
          hasActiveTrip: false,
          trip: null
        };
      }

      const trip = tripResult.rows[0];

      return {
        hasActiveTrip: true,
        trip,
        canProgress: trip.status !== 'exception_pending'
      };

    } catch (error) {
      this.logger.error('Failed to get current trip', {
        error: error.message,
        assignment_id: assignmentId
      });
      throw error;
    } finally {
      if (shouldRelease && client) {
        client.release();
      }
    }
  }

  /**
   * Check if assignment allows duplicate prevention
   * Focus on truck-location combination regardless of date to support exception flows
   * @param {Object} params - Parameters
   * @param {number} params.truckId - Truck ID
   * @param {number} params.loadingLocationId - Loading location ID
   * @param {number} params.unloadingLocationId - Unloading location ID
   * @param {Object} params.client - Database client (optional)
   * @returns {Promise<Object>} Duplicate check result
   */
  async checkDuplicateAssignment(params) {
    const {
      truckId,
      loadingLocationId,
      unloadingLocationId,
      client: providedClient
    } = params;

    const client = providedClient || await getClient();
    const shouldRelease = !providedClient;

    try {
      const duplicateResult = await client.query(`
        SELECT id, assignment_code, status, assigned_date
        FROM assignments
        WHERE truck_id = $1
          AND loading_location_id = $2
          AND unloading_location_id = $3
          AND status IN ('pending_approval', 'assigned', 'in_progress')
        ORDER BY created_at DESC
        LIMIT 1
      `, [truckId, loadingLocationId, unloadingLocationId]);

      if (duplicateResult.rows.length > 0) {
        const existing = duplicateResult.rows[0];
        return {
          isDuplicate: true,
          existingAssignment: existing,
          message: `Active assignment exists: ${existing.assignment_code} (Status: ${existing.status}, Date: ${existing.assigned_date})`
        };
      }

      return {
        isDuplicate: false,
        existingAssignment: null,
        message: 'No duplicate assignment found'
      };

    } catch (error) {
      this.logger.error('Failed to check duplicate assignment', {
        error: error.message,
        truck_id: truckId,
        loading_location_id: loadingLocationId,
        unloading_location_id: unloadingLocationId
      });
      throw error;
    } finally {
      if (shouldRelease && client) {
        client.release();
      }
    }
  }

  /**
   * Check if truck has any valid assignments for a specific location
   * This method supports the Trip Flow Logic requirement that trucks can operate
   * at any previously assigned location without triggering exceptions
   * @param {Object} params - Parameters
   * @param {string} params.truckNumber - Truck number
   * @param {number} params.locationId - Location ID
   * @param {Object} params.client - Database client (optional)
   * @returns {Promise<Object>} Assignment check result
   */
  async hasValidAssignmentForLocation(params) {
    const {
      truckNumber,
      locationId,
      client: providedClient
    } = params;

    const client = providedClient || await getClient();
    const shouldRelease = !providedClient;

    try {
      // Get truck ID first
      const truckResult = await client.query(`
        SELECT id, truck_number, status
        FROM dump_trucks
        WHERE truck_number = $1 AND status = 'active'
      `, [truckNumber]);

      if (truckResult.rows.length === 0) {
        return {
          hasValidAssignment: false,
          assignments: [],
          message: `Truck ${truckNumber} not found or inactive`
        };
      }

      const truck = truckResult.rows[0];

      // Find ALL assignments where this location is either loading or unloading
      const assignmentsResult = await client.query(`
        SELECT
          a.*,
          ll.name as loading_location,
          ul.name as unloading_location,
          d.full_name as driver_name,
          CASE
            WHEN a.loading_location_id = $2 THEN 'loading'
            WHEN a.unloading_location_id = $2 THEN 'unloading'
            ELSE 'unknown'
          END as location_role
        FROM assignments a
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
        JOIN drivers d ON a.driver_id = d.id
        WHERE a.truck_id = $1
          AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
          AND a.status IN ('assigned', 'in_progress')
        ORDER BY a.created_at DESC
      `, [truck.id, locationId]);

      const hasAssignments = assignmentsResult.rows.length > 0;

      this.logger.info('Assignment location check', {
        truck_number: truckNumber,
        location_id: locationId,
        assignments_found: assignmentsResult.rows.length,
        has_valid_assignment: hasAssignments
      });

      return {
        hasValidAssignment: hasAssignments,
        assignments: assignmentsResult.rows,
        truck,
        message: hasAssignments
          ? `Truck ${truckNumber} has ${assignmentsResult.rows.length} valid assignment(s) for this location`
          : `Truck ${truckNumber} has no assignments for this location - exception required`
      };

    } catch (error) {
      this.logger.error('Failed to check assignment for location', {
        error: error.message,
        truck_number: truckNumber,
        location_id: locationId
      });
      throw error;
    } finally {
      if (shouldRelease && client) {
        client.release();
      }
    }
  }

  /**
   * Validate trip progression
   * @param {Object} params - Parameters
   * @param {Object} params.currentTrip - Current trip object
   * @param {Object} params.location - Scanned location
   * @param {Object} params.assignment - Assignment object
   * @returns {Object} Progression validation result
   */
  validateTripProgression(params) {
    const { currentTrip, location, assignment } = params;

    if (!currentTrip) {
      return {
        canProgress: false,
        reason: 'No active trip found',
        nextAction: 'create_trip'
      };
    }

    if (currentTrip.status === 'exception_pending') {
      return {
        canProgress: false,
        reason: 'Trip has pending exception awaiting approval',
        nextAction: 'await_approval'
      };
    }

    // Validate state transitions based on current status and location type
    const validTransitions = this._getValidTransitions(currentTrip.status, location.type, assignment);
    
    if (!validTransitions.isValid) {
      return {
        canProgress: false,
        reason: validTransitions.reason,
        nextAction: validTransitions.nextAction
      };
    }

    return {
      canProgress: true,
      nextStatus: validTransitions.nextStatus,
      nextAction: validTransitions.nextAction
    };
  }

  /**
   * Get valid state transitions
   * @private
   */
  _getValidTransitions(currentStatus, locationType, assignment) {
    const transitions = {
      'loading_start': {
        'loading': {
          isValid: true,
          nextStatus: 'loading_end',
          nextAction: 'complete_loading'
        }
      },
      'loading_end': {
        'unloading': {
          isValid: true,
          nextStatus: 'unloading_start',
          nextAction: 'start_unloading'
        }
      },
      'unloading_start': {
        'unloading': {
          isValid: true,
          nextStatus: 'unloading_end',
          nextAction: 'complete_unloading'
        }
      },
      'unloading_end': {
        'loading': {
          isValid: true,
          nextStatus: 'trip_completed',
          nextAction: 'complete_trip'
        }
      }
    };

    const transition = transitions[currentStatus]?.[locationType];
    
    if (!transition) {
      return {
        isValid: false,
        reason: `Invalid transition from ${currentStatus} at ${locationType} location`,
        nextAction: 'check_location'
      };
    }

    return transition;
  }
}

// Export singleton instance
const assignmentValidator = new AssignmentValidator();

module.exports = {
  AssignmentValidator,
  assignmentValidator,
  VALIDATION_RESULTS
};
