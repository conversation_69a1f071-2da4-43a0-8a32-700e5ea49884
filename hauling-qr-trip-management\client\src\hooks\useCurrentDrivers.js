/**
 * React Hook for Current Drivers Display
 * Fetches current active drivers for display purposes only
 * Does NOT affect the 4-phase workflow
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { getApiBaseUrl } from '../utils/network-utils';

const useCurrentDrivers = (truckIds = [], refreshInterval = 30000) => {
  const [currentDrivers, setCurrentDrivers] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Memoize truckIds to prevent infinite re-renders
  const memoizedTruckIds = useMemo(() => {
    if (!truckIds || truckIds.length === 0) return [];
    return [...truckIds].sort((a, b) => a - b); // Sort for consistent comparison
  }, [truckIds]);

  // Memoize the truck IDs string for API calls
  const truckIdsParam = useMemo(() => {
    return memoizedTruckIds.join(',');
  }, [memoizedTruckIds]);

  const fetchCurrentDrivers = useCallback(async () => {
    if (!memoizedTruckIds || memoizedTruckIds.length === 0) {
      setCurrentDrivers({});
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const apiUrl = getApiBaseUrl();

      const response = await fetch(`${apiUrl}/shifts/current-drivers?truck_ids=${truckIdsParam}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch current drivers: ${response.statusText}`);
      }

      const result = await response.json();
      setCurrentDrivers(result.data || {});

    } catch (err) {
      console.error('Error fetching current drivers:', err);
      setError(err.message);
      setCurrentDrivers({});
    } finally {
      setLoading(false);
    }
  }, [memoizedTruckIds, truckIdsParam]);

  // Initial fetch
  useEffect(() => {
    fetchCurrentDrivers();
  }, [fetchCurrentDrivers]);

  // Auto-refresh
  useEffect(() => {
    if (refreshInterval > 0) {
      const interval = setInterval(fetchCurrentDrivers, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchCurrentDrivers, refreshInterval]);

  /**
   * Get formatted driver display text
   */
  const getDriverDisplay = useCallback((truckId, originalDriverName = null) => {
    const driverInfo = currentDrivers[truckId];

    if (!driverInfo) {
      return originalDriverName || 'Loading...';
    }

    if (driverInfo.hasActiveShift) {
      // Use display_type if available (intelligent classification), otherwise use shift_type
      const displayType = driverInfo.display_type || driverInfo.shift_type;
      const shiftIndicator = displayType === 'day' ? '☀️' :
                           displayType === 'night' ? '🌙' : '🔧';
      return `${shiftIndicator} ${driverInfo.driver_name}`;
    }

    return originalDriverName || 'No Active Shift';
  }, [currentDrivers]);

  /**
   * Get shift status for display
   */
  const getShiftStatus = useCallback((truckId) => {
    const driverInfo = currentDrivers[truckId];

    if (!driverInfo || !driverInfo.hasActiveShift) {
      return {
        status: 'no_shift',
        text: 'No Active Shift',
        color: 'text-gray-500 bg-gray-100'
      };
    }

    // Use display_type if available (intelligent classification), otherwise use shift_type
    const displayType = driverInfo.display_type || driverInfo.shift_type;

    const shiftTypes = {
      'day': {
        status: 'day_shift',
        text: 'Day Shift',
        color: 'text-yellow-700 bg-yellow-100'
      },
      'night': {
        status: 'night_shift',
        text: 'Night Shift',
        color: 'text-blue-700 bg-blue-100'
      },
      'custom': {
        status: 'custom_shift',
        text: 'Custom Shift',
        color: 'text-purple-700 bg-purple-100'
      }
    };

    return shiftTypes[displayType] || {
      status: 'unknown_shift',
      text: 'Unknown Shift',
      color: 'text-gray-700 bg-gray-100'
    };
  }, [currentDrivers]);

  /**
   * Check if truck has active shift
   */
  const hasActiveShift = useCallback((truckId) => {
    const driverInfo = currentDrivers[truckId];
    return driverInfo && driverInfo.hasActiveShift;
  }, [currentDrivers]);

  /**
   * Get current driver info for a truck
   */
  const getCurrentDriver = useCallback((truckId) => {
    return currentDrivers[truckId] || null;
  }, [currentDrivers]);

  /**
   * Manual refresh function
   */
  const refresh = useCallback(() => {
    fetchCurrentDrivers();
  }, [fetchCurrentDrivers]);

  return {
    currentDrivers,
    loading,
    error,
    getDriverDisplay,
    getShiftStatus,
    hasActiveShift,
    getCurrentDriver,
    refresh
  };
};

export default useCurrentDrivers;
