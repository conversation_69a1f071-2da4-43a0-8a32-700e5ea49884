import React from 'react';

const PerformanceRankings = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="animate-pulse bg-secondary-200 h-96 rounded-lg"></div>
    );
  }

  if (!data || !data.rankings || data.rankings.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-8 text-center">
        <span className="text-4xl block mb-2">🏆</span>
        <p className="text-secondary-500">No performance rankings data available</p>
      </div>
    );
  }

  const getRankIcon = (index) => {
    switch (index) {
      case 0: return '🥇';
      case 1: return '🥈';
      case 2: return '🥉';
      default: return `#${index + 1}`;
    }
  };

  const getPerformanceColor = (score) => {
    if (score >= 90) return 'text-green-600 bg-green-50';
    if (score >= 75) return 'text-yellow-600 bg-yellow-50';
    if (score >= 60) return 'text-orange-600 bg-orange-50';
    return 'text-red-600 bg-red-50';
  };

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 overflow-hidden">
      <div className="px-6 py-4 bg-secondary-50 border-b border-secondary-200">
        <h3 className="text-lg font-medium text-secondary-900">
          🏆 Truck Performance Rankings
        </h3>
        <p className="text-sm text-secondary-500 mt-1">
          Based on trip completion, efficiency, and exception rates
        </p>
      </div>

      {/* Desktop View */}
      <div className="hidden lg:block">
        <table className="min-w-full divide-y divide-secondary-200">
          <thead className="bg-secondary-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Rank
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Truck
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Driver
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Trips
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Loading
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Forward Travel
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Unloading
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Return Travel
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Total
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Performance
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {data.rankings.map((truck, index) => (
              <tr key={truck.truckId} className="hover:bg-secondary-50">
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-lg">
                    {getRankIcon(index)}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-secondary-900">
                    {truck.truckNumber}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.driverName}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-secondary-900">
                    {truck.completedTrips || truck.totalTrips || 0}
                  </div>
                  <div className="text-xs text-secondary-500">
                    completed
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.avgLoadingDuration ? `${truck.avgLoadingDuration}m` : 'N/A'}
                  </div>
                  <div className="text-xs text-secondary-500">
                    loading
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.avgForwardTravelDuration ? `${truck.avgForwardTravelDuration}m` : 'N/A'}
                  </div>
                  <div className="text-xs text-secondary-500">
                    forward
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.avgUnloadingDuration ? `${truck.avgUnloadingDuration}m` : 'N/A'}
                  </div>
                  <div className="text-xs text-secondary-500">
                    unloading
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-secondary-900">
                    {truck.avgReturnTravelDuration ? `${truck.avgReturnTravelDuration}m` : 'N/A'}
                  </div>
                  <div className="text-xs text-secondary-500">
                    return
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-secondary-900">
                    {truck.avgTotalDuration ? `${truck.avgTotalDuration}m` : 'N/A'}
                  </div>
                  <div className="text-xs text-secondary-500">
                    total
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPerformanceColor(truck.performanceScore)}`}>
                    {truck.performanceScore}
                  </span>
                  <div className="text-xs text-secondary-500 mt-1">
                    {truck.completionRate}% completion
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Tablet/Mobile View */}
      <div className="lg:hidden">
        <div className="space-y-4 p-4">
          {data.rankings.map((truck, index) => (
            <div key={truck.truckId} className="bg-secondary-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="text-lg mr-3">
                    {getRankIcon(index)}
                  </div>
                  <div>
                    <div className="font-medium text-secondary-900">
                      {truck.truckNumber}
                    </div>
                    <div className="text-sm text-secondary-500">
                      {truck.driverName}
                    </div>
                  </div>
                </div>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPerformanceColor(truck.performanceScore)}`}>
                  {truck.performanceScore}
                </span>
              </div>
              
              <div className="space-y-3">
                {/* Trip Counts */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-secondary-500">Completed:</span>
                    <span className="ml-2 font-medium text-secondary-900">{truck.completedTrips || 0}</span>
                  </div>
                  <div>
                    <span className="text-secondary-500">Total:</span>
                    <span className="ml-2 text-secondary-900">{truck.totalTrips || truck.tripCount || 0}</span>
                  </div>
                </div>

                {/* Duration Breakdown */}
                <div className="bg-white rounded p-3">
                  <div className="text-xs font-medium text-secondary-700 mb-2">Duration Breakdown (avg)</div>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-secondary-500">Loading:</span>
                      <span className="text-secondary-900">{truck.avgLoadingDuration ? `${truck.avgLoadingDuration}m` : 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-secondary-500">Forward:</span>
                      <span className="text-secondary-900">{truck.avgForwardTravelDuration ? `${truck.avgForwardTravelDuration}m` : 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-secondary-500">Unloading:</span>
                      <span className="text-secondary-900">{truck.avgUnloadingDuration ? `${truck.avgUnloadingDuration}m` : 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-secondary-500">Return:</span>
                      <span className="text-secondary-900">{truck.avgReturnTravelDuration ? `${truck.avgReturnTravelDuration}m` : 'N/A'}</span>
                    </div>
                  </div>
                  <div className="border-t border-secondary-200 mt-2 pt-2">
                    <div className="flex justify-between text-sm font-medium">
                      <span className="text-secondary-700">Total:</span>
                      <span className="text-secondary-900">{truck.avgTotalDuration ? `${truck.avgTotalDuration}m` : 'N/A'}</span>
                    </div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-secondary-500">Completion:</span>
                    <span className="ml-2 text-secondary-900">{truck.completionRate}%</span>
                  </div>
                  <div>
                    <span className="text-secondary-500">Stopped:</span>
                    <span className="ml-2 text-secondary-900">{truck.stoppedTrips || 0}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="px-6 py-4 bg-secondary-50 border-t border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-blue-600">
              {data.totalTrucks}
            </div>
            <div className="text-sm text-secondary-500">Total Trucks Ranked</div>
          </div>
          
          <div>
            <div className="text-lg font-bold text-green-600">
              {data.rankings.filter(t => t.performanceScore >= 90).length}
            </div>
            <div className="text-sm text-secondary-500">Excellent Performance</div>
          </div>
          
          <div>
            <div className="text-lg font-bold text-yellow-600">
              {data.rankings.filter(t => t.performanceScore >= 75 && t.performanceScore < 90).length}
            </div>
            <div className="text-sm text-secondary-500">Good Performance</div>
          </div>
          
          <div>
            <div className="text-lg font-bold text-red-600">
              {data.rankings.filter(t => t.performanceScore < 60).length}
            </div>
            <div className="text-sm text-secondary-500">Needs Improvement</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceRankings;
