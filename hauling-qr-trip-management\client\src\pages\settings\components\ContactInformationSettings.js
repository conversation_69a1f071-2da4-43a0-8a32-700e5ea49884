import React, { useState, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';
import { getApiBaseUrl } from '../../../utils/network-utils';

/**
 * Contact Information Settings Component
 * Allows administrators to configure contact information for different driver and truck status types
 * Used by DriverStatusErrorModal to display dynamic contact information
 */

const ContactInformationSettings = () => {
  const [contactSettings, setContactSettings] = useState({
    driver: {},
    truck: {}
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({
    category: 'driver',
    status_type: '',
    primary_contact: '',
    phone: '',
    email: '',
    description: ''
  });

  // Status type options for each category
  const statusOptions = {
    driver: [
      { value: 'inactive', label: 'Inactive', description: 'Driver account is inactive' },
      { value: 'suspended', label: 'Suspended', description: 'Driver account is suspended' },
      { value: 'on_leave', label: 'On Leave', description: 'Driver is currently on leave' },
      { value: 'terminated', label: 'Terminated', description: 'Driver employment has ended' }
    ],
    truck: [
      { value: 'inactive', label: 'Inactive', description: 'Truck is inactive' },
      { value: 'maintenance', label: 'Under Maintenance', description: 'Truck is under maintenance' },
      { value: 'retired', label: 'Retired', description: 'Truck has been retired from service' }
    ]
  };

  useEffect(() => {
    loadContactSettings();
  }, []);

  const loadContactSettings = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('hauling_token');
      if (!token) {
        toast.error('Authentication required');
        return;
      }

      const config = {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      const apiBaseUrl = getApiBaseUrl();
      const response = await axios.get(`${apiBaseUrl}/contact-settings`, config);

      if (response.data.success) {
        setContactSettings(response.data.data);
      }
    } catch (error) {
      console.error('Error loading contact settings:', error);
      toast.error('Failed to load contact settings');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (category, statusType, data) => {
    setEditingItem(`${category}-${statusType}`);
    setFormData({
      category,
      status_type: statusType,
      primary_contact: data.primary || '',
      phone: data.phone || '',
      email: data.email || '',
      description: data.description || ''
    });
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const token = localStorage.getItem('hauling_token');
      if (!token) {
        toast.error('Authentication required');
        return;
      }

      // Validate required fields
      if (!formData.primary_contact || !formData.phone || !formData.email) {
        toast.error('Primary contact, phone, and email are required');
        return;
      }

      const config = {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      const apiBaseUrl = getApiBaseUrl();
      const response = await axios.post(`${apiBaseUrl}/contact-settings`, formData, config);

      if (response.data.success) {
        toast.success('Contact information saved successfully');
        setEditingItem(null);
        loadContactSettings(); // Reload data
      }
    } catch (error) {
      console.error('Error saving contact setting:', error);
      const message = error.response?.data?.message || 'Failed to save contact information';
      toast.error(message);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingItem(null);
    setFormData({
      category: 'driver',
      status_type: '',
      primary_contact: '',
      phone: '',
      email: '',
      description: ''
    });
  };

  const renderContactCard = (category, statusType, data, statusInfo) => {
    const isEditing = editingItem === `${category}-${statusType}`;
    const cardKey = `${category}-${statusType}`;

    return (
      <div key={cardKey} className="bg-white border border-secondary-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h4 className="font-medium text-secondary-900 capitalize">
              {statusInfo.label}
            </h4>
            <p className="text-sm text-secondary-600">{statusInfo.description}</p>
          </div>
          {!isEditing && (
            <button
              onClick={() => handleEdit(category, statusType, data)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Edit
            </button>
          )}
        </div>

        {isEditing ? (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Primary Contact
              </label>
              <input
                type="text"
                value={formData.primary_contact}
                onChange={(e) => setFormData({ ...formData, primary_contact: e.target.value })}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Supervisor, HR Department"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Phone Number
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="(*************"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Email Address
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Description (Optional)
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows="2"
                placeholder="Additional information about this contact"
              />
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                disabled={saving}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm font-medium"
              >
                {saving ? 'Saving...' : 'Save'}
              </button>
              <button
                onClick={handleCancel}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm font-medium"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-secondary-700">Contact:</span>
              <span className="text-sm text-secondary-900">{data?.primary || 'Not set'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-secondary-700">Phone:</span>
              <span className="text-sm text-secondary-900">{data?.phone || 'Not set'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-secondary-700">Email:</span>
              <span className="text-sm text-secondary-900">{data?.email || 'Not set'}</span>
            </div>
            {data?.description && (
              <div className="mt-2">
                <span className="text-xs text-secondary-600">{data.description}</span>
              </div>
            )}
            {data?.updated_at && (
              <div className="text-xs text-secondary-500 mt-2">
                Last updated: {new Date(data.updated_at).toLocaleString()}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-secondary-600">Loading contact settings...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-secondary-900">📞 Contact Information Settings</h2>
        <p className="text-secondary-600 mt-1">
          Configure contact information for different driver and truck status types. 
          This information is displayed in error modals and status notifications.
        </p>
      </div>

      {/* Driver Contact Settings */}
      <div className="bg-white rounded-lg border border-secondary-200 p-6">
        <h3 className="text-lg font-semibold text-secondary-900 mb-4 flex items-center">
          <span className="mr-2">👤</span>
          Driver Status Contacts
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {statusOptions.driver.map((statusInfo) => {
            const data = contactSettings.driver[statusInfo.value];
            return renderContactCard('driver', statusInfo.value, data, statusInfo);
          })}
        </div>
      </div>

      {/* Truck Contact Settings */}
      <div className="bg-white rounded-lg border border-secondary-200 p-6">
        <h3 className="text-lg font-semibold text-secondary-900 mb-4 flex items-center">
          <span className="mr-2">🚛</span>
          Truck Status Contacts
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {statusOptions.truck.map((statusInfo) => {
            const data = contactSettings.truck[statusInfo.value];
            return renderContactCard('truck', statusInfo.value, data, statusInfo);
          })}
        </div>
      </div>

      {/* Information Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <span className="text-blue-600 text-lg mr-3">ℹ️</span>
          <div>
            <h4 className="text-blue-800 font-medium">How This Works</h4>
            <p className="text-blue-700 text-sm mt-1">
              When drivers or trucks have status issues (inactive, suspended, maintenance, etc.), 
              the system displays error modals with the contact information configured here. 
              Users can click phone numbers to call or email addresses to send emails directly.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactInformationSettings;
