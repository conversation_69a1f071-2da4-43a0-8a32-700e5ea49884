import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { getApiBaseUrl } from '../../../../utils/network-utils';
import FleetMetrics from './FleetMetrics';
import FleetStatusGrid from './FleetStatusGrid';
import TrendCharts from './TrendCharts';

const FleetOverview = ({ lastUpdated, isConnected, loading }) => {
  const [fleetData, setFleetData] = useState(null);
  const [statusData, setStatusData] = useState(null);
  const [dataLoading, setDataLoading] = useState(true);

  // Load fleet overview data
  const loadFleetOverviewData = async () => {
    try {
      setDataLoading(true);

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/analytics/fleet-overview`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch fleet overview data');
      }
      
      const result = await response.json();
      setFleetData(result.data);
      
    } catch (error) {
      console.error('Error loading fleet overview data:', error);
      toast.error('Failed to load fleet overview data');
    } finally {
      setDataLoading(false);
    }
  };

  // Load fleet status data
  const loadFleetStatusData = async () => {
    try {
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/analytics/fleet-status`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch fleet status data');
      }
      
      const result = await response.json();
      setStatusData(result.data);
      
    } catch (error) {
      console.error('Error loading fleet status data:', error);
      toast.error('Failed to load fleet status data');
    }
  };

  // Load data on component mount and when lastUpdated changes
  useEffect(() => {
    loadFleetOverviewData();
    loadFleetStatusData();
  }, [lastUpdated]);

  // Show loading state
  if (dataLoading && !fleetData) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-secondary-200 h-24 rounded-lg"></div>
          ))}
        </div>
        <div className="bg-secondary-200 h-64 rounded-lg"></div>
        <div className="bg-secondary-200 h-96 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Connection Status Alert */}
      {!isConnected && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400 text-xl">⚠️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Real-time updates unavailable
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>WebSocket connection is disconnected. Data may not be current.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Fleet Metrics */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          Fleet Metrics
        </h2>
        <FleetMetrics 
          data={fleetData} 
          loading={dataLoading || loading}
        />
      </div>

      {/* Fleet Status Grid */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-secondary-900">
            Fleet Status
          </h2>
          <div className="text-sm text-secondary-500">
            {statusData?.totalTrucks || 0} trucks
          </div>
        </div>
        <FleetStatusGrid 
          data={statusData} 
          loading={dataLoading || loading}
        />
      </div>

      {/* Trend Charts */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          Performance Trends (Last 7 Days)
        </h2>
        <TrendCharts 
          data={fleetData?.trends || []} 
          loading={dataLoading || loading}
        />
      </div>

      {/* Summary Stats */}
      {fleetData && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-secondary-900 mb-4">
            Summary
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">
                {fleetData.metrics?.fleetUtilization || 0}%
              </div>
              <div className="text-sm text-secondary-500">Fleet Utilization</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {fleetData.metrics?.todayCompletionRate || '0.0'}%
              </div>
              <div className="text-sm text-secondary-500">Today's Completion Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {fleetData.metrics?.todayAvgTripTime || 0}m
              </div>
              <div className="text-sm text-secondary-500">Avg Trip Time Today</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FleetOverview;
