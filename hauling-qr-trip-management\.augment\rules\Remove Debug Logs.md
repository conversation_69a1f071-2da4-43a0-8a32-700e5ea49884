---
type: "always_apply"
description: "Automatically removes console.log, console.debug, console.warn, console.error statements and other debug logging from JavaScript, JSX, and related files to keep code clean and reduce browser/server console output"
---
Please review the modified files and remove all console.log(), console.debug(), console.warn(), console.error(), console.info(), console.trace(), and similar debug logging statements. Also remove any temporary debug variables or debugging code blocks. Keep only essential error logging that is part of the application's proper error handling system. Make the code cleaner by removing unnecessary console output that clutters the browser and server logs.