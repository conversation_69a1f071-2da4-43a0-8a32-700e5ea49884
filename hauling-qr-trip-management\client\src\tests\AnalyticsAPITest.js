import React, { useState } from 'react';
import { getApiBaseUrl } from '../utils/network-utils';
import toast from 'react-hot-toast';

const AnalyticsAPITest = () => {
  const [testResults, setTestResults] = useState({});
  const [testing, setTesting] = useState(false);
  const [overallStatus, setOverallStatus] = useState('pending');

  const endpoints = [
    {
      name: 'Fleet Overview',
      url: '/analytics/fleet-overview',
      method: 'GET',
      description: 'Fleet metrics and overview data'
    },
    {
      name: 'Fleet Status',
      url: '/analytics/fleet-status',
      method: 'GET',
      description: 'Current fleet status and truck details'
    },
    {
      name: 'Trip Performance',
      url: '/analytics/trip-performance',
      method: 'GET',
      params: { start_date: '2025-06-07', end_date: '2025-07-07' },
      description: 'Trip performance analytics with date range'
    },
    {
      name: 'Stopped Analytics',
      url: '/analytics/stopped-analytics',
      method: 'GET',
      params: { start_date: '2025-06-07', end_date: '2025-07-07' },
      description: 'Stopped analytics and trends'
    },
    {
      name: 'Live Operations',
      url: '/analytics/live-operations',
      method: 'GET',
      description: 'Real-time operations monitoring data'
    },
    {
      name: 'Truck Rankings',
      url: '/analytics/truck-rankings',
      method: 'GET',
      params: { start_date: '2025-06-07', end_date: '2025-07-07' },
      description: 'Truck performance rankings'
    }
  ];

  const testEndpoint = async (endpoint) => {
    try {
      const apiUrl = getApiBaseUrl();
      const token = localStorage.getItem('hauling_token');
      
      if (!token) {
        return {
          status: 'error',
          message: 'No authentication token found',
          data: null,
          responseTime: 0
        };
      }

      const startTime = Date.now();
      
      // Build URL with parameters if provided
      let fullUrl = `${apiUrl}${endpoint.url}`;
      if (endpoint.params) {
        const params = new URLSearchParams(endpoint.params);
        fullUrl += `?${params}`;
      }

      const response = await fetch(fullUrl, {
        method: endpoint.method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        const data = await response.json();
        return {
          status: 'success',
          message: `${response.status} ${response.statusText}`,
          data: data,
          responseTime: responseTime,
          dataSize: JSON.stringify(data).length
        };
      } else {
        const errorText = await response.text();
        return {
          status: 'error',
          message: `${response.status} ${response.statusText}`,
          data: errorText,
          responseTime: responseTime
        };
      }
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        data: null,
        responseTime: 0
      };
    }
  };

  const runAllTests = async () => {
    setTesting(true);
    setTestResults({});
    setOverallStatus('testing');
    
    const results = {};
    let successCount = 0;
    
    for (const endpoint of endpoints) {
      console.log(`Testing ${endpoint.name}...`);
      const result = await testEndpoint(endpoint);
      results[endpoint.name] = result;
      
      if (result.status === 'success') {
        successCount++;
      }
      
      // Update results incrementally
      setTestResults({ ...results });
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    const overallSuccess = successCount === endpoints.length;
    setOverallStatus(overallSuccess ? 'success' : 'partial');
    
    if (overallSuccess) {
      toast.success(`All ${endpoints.length} API endpoints working correctly!`, {
        duration: 4000,
        icon: '✅'
      });
    } else {
      toast.error(`${successCount}/${endpoints.length} endpoints working. Check results below.`, {
        duration: 4000,
        icon: '⚠️'
      });
    }
    
    setTesting(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'testing': return '🔄';
      default: return '⏳';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50 border-green-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'testing': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                🧪 Analytics API Test Suite
              </h1>
              <p className="text-gray-600 mt-1">
                Comprehensive testing of all analytics endpoints
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(overallStatus)}`}>
                {getStatusIcon(overallStatus)} {overallStatus.toUpperCase()}
              </div>
              
              <button
                onClick={runAllTests}
                disabled={testing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {testing ? (
                  <>
                    <span className="animate-spin mr-2">🔄</span>
                    Testing...
                  </>
                ) : (
                  <>
                    <span className="mr-2">🚀</span>
                    Run All Tests
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="grid gap-4">
            {endpoints.map((endpoint, index) => {
              const result = testResults[endpoint.name];
              const status = testing && !result ? 'testing' : result?.status || 'pending';
              
              return (
                <div
                  key={endpoint.name}
                  className={`border rounded-lg p-4 transition-colors ${getStatusColor(status)}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">{getStatusIcon(status)}</span>
                        <h3 className="font-medium text-lg">
                          {endpoint.name}
                        </h3>
                        {result?.responseTime && (
                          <span className="ml-2 text-sm opacity-75">
                            ({result.responseTime}ms)
                          </span>
                        )}
                      </div>
                      
                      <p className="text-sm opacity-75 mt-1">
                        {endpoint.description}
                      </p>
                      
                      <div className="mt-2 text-sm font-mono">
                        <span className="font-medium">{endpoint.method}</span> {endpoint.url}
                        {endpoint.params && (
                          <span className="text-blue-600">
                            ?{new URLSearchParams(endpoint.params).toString()}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    {result && (
                      <div className="ml-4 text-right">
                        <div className="text-sm font-medium">
                          {result.message}
                        </div>
                        {result.dataSize && (
                          <div className="text-xs opacity-75">
                            {(result.dataSize / 1024).toFixed(1)}KB response
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  
                  {result?.data && result.status === 'success' && (
                    <div className="mt-3 p-3 bg-white bg-opacity-50 rounded border">
                      <details>
                        <summary className="cursor-pointer text-sm font-medium">
                          View Response Data
                        </summary>
                        <pre className="mt-2 text-xs overflow-auto max-h-40">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                  
                  {result?.data && result.status === 'error' && (
                    <div className="mt-3 p-3 bg-red-50 rounded border border-red-200">
                      <div className="text-sm font-medium text-red-800">Error Details:</div>
                      <div className="text-sm text-red-700 mt-1">
                        {typeof result.data === 'string' ? result.data : JSON.stringify(result.data)}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          
          {Object.keys(testResults).length > 0 && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Test Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="font-medium">Total Tests</div>
                  <div className="text-2xl font-bold">{endpoints.length}</div>
                </div>
                <div>
                  <div className="font-medium text-green-600">Passed</div>
                  <div className="text-2xl font-bold text-green-600">
                    {Object.values(testResults).filter(r => r.status === 'success').length}
                  </div>
                </div>
                <div>
                  <div className="font-medium text-red-600">Failed</div>
                  <div className="text-2xl font-bold text-red-600">
                    {Object.values(testResults).filter(r => r.status === 'error').length}
                  </div>
                </div>
                <div>
                  <div className="font-medium">Avg Response</div>
                  <div className="text-2xl font-bold">
                    {Object.values(testResults).length > 0 ? 
                      Math.round(Object.values(testResults).reduce((sum, r) => sum + (r.responseTime || 0), 0) / Object.values(testResults).length) 
                      : 0}ms
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalyticsAPITest;
