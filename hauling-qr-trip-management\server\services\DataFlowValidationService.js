/**
 * Data Flow Validation Service
 * 
 * Validates the complete data flow chain:
 * Shift Management → Assignment Management → Trip Monitoring
 * 
 * This service ensures that:
 * 1. Active shift data correctly flows to Assignment Management
 * 2. Assignment Management accurately determines driver assignments based on active shifts
 * 3. Trip Monitoring receives complete driver information from the assignment system
 * 4. End-to-end data flow works: Driver QR check-in → Active shift → Assignment updated → Trip scan → Complete trip_logs entry
 */

const { getClient } = require('../config/database');
const { logInfo, logError } = require('../utils/logger');

class DataFlowValidationService {
  /**
   * Validate the complete data flow chain
   * @returns {Promise<Object>} Validation results
   */
  static async validateCompleteDataFlow() {
    const client = await getClient();
    const validationResults = {
      overall_status: 'operational',
      issues: [],
      metrics: {},
      validation_steps: {},
      timestamp: new Date().toISOString()
    };

    try {
      await client.query('BEGIN');

      // Step 1: Validate Shift Management → Assignment Management flow
      const shiftToAssignmentValidation = await this.validateShiftToAssignmentFlow(client);
      validationResults.validation_steps.shift_to_assignment = shiftToAssignmentValidation;

      // Step 2: Validate Assignment Management → Trip Monitoring flow
      const assignmentToTripValidation = await this.validateAssignmentToTripFlow(client);
      validationResults.validation_steps.assignment_to_trip = assignmentToTripValidation;

      // Step 3: Validate end-to-end data consistency
      const endToEndValidation = await this.validateEndToEndConsistency(client);
      validationResults.validation_steps.end_to_end = endToEndValidation;

      // Step 4: Test driver capture functionality
      const driverCaptureValidation = await this.validateDriverCaptureAccuracy(client);
      validationResults.validation_steps.driver_capture = driverCaptureValidation;

      // Aggregate all issues
      validationResults.issues = [
        ...shiftToAssignmentValidation.issues,
        ...assignmentToTripValidation.issues,
        ...endToEndValidation.issues,
        ...driverCaptureValidation.issues
      ];

      // Determine overall status
      const criticalIssues = validationResults.issues.filter(issue => issue.severity === 'critical');
      const warningIssues = validationResults.issues.filter(issue => issue.severity === 'warning');

      if (criticalIssues.length > 0) {
        validationResults.overall_status = 'critical';
      } else if (warningIssues.length > 0) {
        validationResults.overall_status = 'warning';
      }

      // Aggregate metrics
      validationResults.metrics = {
        total_issues: validationResults.issues.length,
        critical_issues: criticalIssues.length,
        warning_issues: warningIssues.length,
        info_issues: validationResults.issues.filter(issue => issue.severity === 'info').length,
        active_shifts: shiftToAssignmentValidation.metrics.active_shifts_count,
        assignments_with_drivers: assignmentToTripValidation.metrics.assignments_with_drivers,
        recent_trips_with_drivers: driverCaptureValidation.metrics.trips_with_driver_info
      };

      await client.query('COMMIT');

      logInfo('DATA_FLOW_VALIDATION_COMPLETE', 'Data flow validation completed', {
        overall_status: validationResults.overall_status,
        total_issues: validationResults.issues.length,
        validation_duration: Date.now() - new Date(validationResults.timestamp).getTime()
      });

      return validationResults;

    } catch (error) {
      await client.query('ROLLBACK');
      logError('DATA_FLOW_VALIDATION_ERROR', error, {
        validation_step: 'overall_validation',
        timestamp: validationResults.timestamp
      });

      return {
        overall_status: 'critical',
        issues: [{
          id: 'validation_system_error',
          type: 'system_error',
          severity: 'critical',
          description: `Data flow validation system error: ${error.message}`,
          affected_records: [],
          auto_fixable: false
        }],
        metrics: {},
        validation_steps: {},
        timestamp: new Date().toISOString()
      };
    } finally {
      client.release();
    }
  }

  /**
   * Validate that active shift data correctly flows to Assignment Management
   * @param {Object} client Database client
   * @returns {Promise<Object>} Validation results
   */
  static async validateShiftToAssignmentFlow(client) {
    const validation = {
      status: 'operational',
      issues: [],
      metrics: {},
      details: {}
    };

    try {
      // Query to check shift → assignment data flow
      const shiftAssignmentFlowQuery = `
        WITH active_shifts AS (
          SELECT 
            ds.id as shift_id,
            ds.truck_id,
            ds.driver_id,
            ds.shift_type,
            ds.status,
            ds.start_date,
            ds.end_date,
            ds.auto_created,
            dt.truck_number,
            d.full_name as driver_name,
            d.employee_id
          FROM driver_shifts ds
          JOIN dump_trucks dt ON ds.truck_id = dt.id
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.status = 'active'
            AND ds.start_date <= CURRENT_DATE
            AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
        ),
        assignment_sync_check AS (
          SELECT 
            ash.shift_id,
            ash.truck_id,
            ash.driver_id as shift_driver_id,
            ash.truck_number,
            ash.driver_name as shift_driver_name,
            ash.shift_type,
            ash.auto_created,
            a.id as assignment_id,
            a.driver_id as assignment_driver_id,
            ad.full_name as assignment_driver_name,
            CASE 
              WHEN a.id IS NULL THEN 'no_assignment'
              WHEN a.driver_id IS NULL THEN 'assignment_no_driver'
              WHEN a.driver_id = ash.driver_id THEN 'synchronized'
              ELSE 'driver_mismatch'
            END as sync_status
          FROM active_shifts ash
          LEFT JOIN assignments a ON ash.truck_id = a.truck_id 
            AND a.status IN ('assigned', 'in_progress')
          LEFT JOIN drivers ad ON a.driver_id = ad.id
        )
        SELECT 
          sync_status,
          COUNT(*) as count,
          array_agg(
            jsonb_build_object(
              'shift_id', shift_id,
              'truck_number', truck_number,
              'shift_driver', shift_driver_name,
              'assignment_driver', assignment_driver_name,
              'shift_type', shift_type,
              'auto_created', auto_created
            )
          ) as affected_records
        FROM assignment_sync_check
        GROUP BY sync_status
      `;

      const syncResult = await client.query(shiftAssignmentFlowQuery);
      
      validation.metrics.active_shifts_count = syncResult.rows.reduce((sum, row) => sum + parseInt(row.count), 0);
      validation.details.sync_breakdown = {};

      for (const row of syncResult.rows) {
        validation.details.sync_breakdown[row.sync_status] = {
          count: parseInt(row.count),
          records: row.affected_records
        };

        // Create issues for problematic sync statuses
        if (row.sync_status === 'no_assignment') {
          validation.issues.push({
            id: `shift_no_assignment_${Date.now()}`,
            type: 'data_flow_break',
            severity: 'warning',
            description: `${row.count} active shifts have no corresponding assignments`,
            affected_records: row.affected_records.map(r => r.shift_id),
            auto_fixable: true,
            fix_suggestion: 'Create assignments for active shifts without assignments'
          });
        } else if (row.sync_status === 'assignment_no_driver') {
          validation.issues.push({
            id: `assignment_no_driver_${Date.now()}`,
            type: 'data_flow_incomplete',
            severity: 'warning',
            description: `${row.count} assignments exist but have no driver assigned despite active shifts`,
            affected_records: row.affected_records.map(r => r.shift_id),
            auto_fixable: true,
            fix_suggestion: 'Update assignments to match active shift drivers'
          });
        } else if (row.sync_status === 'driver_mismatch') {
          validation.issues.push({
            id: `driver_mismatch_${Date.now()}`,
            type: 'data_inconsistency',
            severity: 'critical',
            description: `${row.count} assignments have different drivers than their active shifts`,
            affected_records: row.affected_records.map(r => r.shift_id),
            auto_fixable: true,
            fix_suggestion: 'Synchronize assignment drivers with active shift drivers'
          });
        }
      }

      // Check for QR-created shift protection
      const qrShiftProtectionQuery = `
        SELECT 
          COUNT(*) as qr_active_shifts,
          COUNT(*) FILTER (WHERE ds.end_date IS NULL) as qr_active_no_end_date
        FROM driver_shifts ds
        WHERE ds.status = 'active'
          AND ds.auto_created = true
          AND ds.start_date IS NOT NULL
      `;

      const qrProtectionResult = await client.query(qrShiftProtectionQuery);
      validation.metrics.qr_active_shifts = parseInt(qrProtectionResult.rows[0].qr_active_shifts);
      validation.metrics.qr_active_no_end_date = parseInt(qrProtectionResult.rows[0].qr_active_no_end_date);

      if (validation.issues.length === 0) {
        validation.status = 'operational';
      } else if (validation.issues.some(issue => issue.severity === 'critical')) {
        validation.status = 'critical';
      } else {
        validation.status = 'warning';
      }

      return validation;

    } catch (error) {
      logError('SHIFT_ASSIGNMENT_VALIDATION_ERROR', error);
      validation.status = 'critical';
      validation.issues.push({
        id: 'shift_assignment_validation_error',
        type: 'system_error',
        severity: 'critical',
        description: `Error validating shift to assignment flow: ${error.message}`,
        affected_records: [],
        auto_fixable: false
      });
      return validation;
    }
  }

  /**
   * Validate that Assignment Management accurately determines driver assignments for Trip Monitoring
   * @param {Object} client Database client
   * @returns {Promise<Object>} Validation results
   */
  static async validateAssignmentToTripFlow(client) {
    const validation = {
      status: 'operational',
      issues: [],
      metrics: {},
      details: {}
    };

    try {
      // Query to check assignment → trip monitoring data flow
      const assignmentTripFlowQuery = `
        WITH assignment_driver_info AS (
          SELECT 
            a.id as assignment_id,
            a.truck_id,
            a.driver_id,
            a.status as assignment_status,
            dt.truck_number,
            d.full_name as assignment_driver_name,
            d.employee_id,
            -- Check if there's an active shift for this truck
            ds.id as active_shift_id,
            ds.driver_id as shift_driver_id,
            sd.full_name as shift_driver_name,
            ds.shift_type,
            CASE 
              WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
                CONCAT('✅ ', ds.shift_type, ' Shift Active')
              WHEN ds.id IS NOT NULL AND ds.status = 'scheduled' THEN
                CONCAT('📅 ', ds.shift_type, ' Shift Scheduled')
              ELSE '⚠️ No Active Shift'
            END as trip_monitoring_display
          FROM assignments a
          JOIN dump_trucks dt ON a.truck_id = dt.id
          LEFT JOIN drivers d ON a.driver_id = d.id
          LEFT JOIN driver_shifts ds ON (
            ds.truck_id = a.truck_id
            AND ds.status = 'active'
            AND ds.start_date <= CURRENT_DATE
            AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
          )
          LEFT JOIN drivers sd ON ds.driver_id = sd.id
          WHERE a.status IN ('assigned', 'in_progress')
        )
        SELECT 
          trip_monitoring_display,
          COUNT(*) as count,
          array_agg(
            jsonb_build_object(
              'assignment_id', assignment_id,
              'truck_number', truck_number,
              'assignment_driver', assignment_driver_name,
              'shift_driver', shift_driver_name,
              'shift_type', shift_type,
              'assignment_status', assignment_status
            )
          ) as affected_records
        FROM assignment_driver_info
        GROUP BY trip_monitoring_display
      `;

      const tripFlowResult = await client.query(assignmentTripFlowQuery);
      
      validation.metrics.total_assignments = tripFlowResult.rows.reduce((sum, row) => sum + parseInt(row.count), 0);
      validation.details.trip_monitoring_breakdown = {};

      for (const row of tripFlowResult.rows) {
        validation.details.trip_monitoring_breakdown[row.trip_monitoring_display] = {
          count: parseInt(row.count),
          records: row.affected_records
        };

        // Create issues for "No Active Shift" cases
        if (row.trip_monitoring_display === '⚠️ No Active Shift') {
          validation.issues.push({
            id: `no_active_shift_display_${Date.now()}`,
            type: 'trip_monitoring_display_issue',
            severity: 'warning',
            description: `${row.count} assignments show "No Active Shift" in Trip Monitoring`,
            affected_records: row.affected_records.map(r => r.assignment_id),
            auto_fixable: true,
            fix_suggestion: 'Synchronize assignment display with active shift status'
          });
        }
      }

      validation.metrics.assignments_with_drivers = validation.details.trip_monitoring_breakdown['✅ Day Shift Active']?.count || 0;
      validation.metrics.assignments_with_drivers += validation.details.trip_monitoring_breakdown['✅ Night Shift Active']?.count || 0;
      validation.metrics.no_active_shift_count = validation.details.trip_monitoring_breakdown['⚠️ No Active Shift']?.count || 0;

      if (validation.issues.length === 0) {
        validation.status = 'operational';
      } else {
        validation.status = 'warning';
      }

      return validation;

    } catch (error) {
      logError('ASSIGNMENT_TRIP_VALIDATION_ERROR', error);
      validation.status = 'critical';
      validation.issues.push({
        id: 'assignment_trip_validation_error',
        type: 'system_error',
        severity: 'critical',
        description: `Error validating assignment to trip flow: ${error.message}`,
        affected_records: [],
        auto_fixable: false
      });
      return validation;
    }
  }

  /**
   * Validate end-to-end data consistency across all three systems
   * @param {Object} client Database client
   * @returns {Promise<Object>} Validation results
   */
  static async validateEndToEndConsistency(client) {
    const validation = {
      status: 'operational',
      issues: [],
      metrics: {},
      details: {}
    };

    try {
      // Query to check end-to-end consistency
      const endToEndQuery = `
        WITH system_consistency AS (
          SELECT 
            dt.id as truck_id,
            dt.truck_number,
            -- Shift Management data
            ds.id as shift_id,
            ds.driver_id as shift_driver_id,
            ds.status as shift_status,
            ds.shift_type,
            ds.auto_created,
            sd.full_name as shift_driver_name,
            sd.employee_id as shift_employee_id,
            -- Assignment Management data
            a.id as assignment_id,
            a.driver_id as assignment_driver_id,
            a.status as assignment_status,
            ad.full_name as assignment_driver_name,
            -- Trip Monitoring data (recent trips)
            tl.id as recent_trip_id,
            tl.performed_by_driver_id as trip_driver_id,
            tl.performed_by_driver_name as trip_driver_name,
            tl.performed_by_employee_id as trip_employee_id,
            tl.performed_by_shift_id as trip_shift_id,
            tl.performed_by_shift_type as trip_shift_type,
            tl.status as trip_status,
            tl.created_at as trip_created_at
          FROM dump_trucks dt
          LEFT JOIN driver_shifts ds ON (
            ds.truck_id = dt.id 
            AND ds.status = 'active'
            AND ds.start_date <= CURRENT_DATE
            AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
          )
          LEFT JOIN drivers sd ON ds.driver_id = sd.id
          LEFT JOIN assignments a ON (
            a.truck_id = dt.id 
            AND a.status IN ('assigned', 'in_progress')
          )
          LEFT JOIN drivers ad ON a.driver_id = ad.id
          LEFT JOIN LATERAL (
            SELECT *
            FROM trip_logs tl2
            WHERE tl2.assignment_id = a.id
            ORDER BY tl2.created_at DESC
            LIMIT 1
          ) tl ON true
          WHERE dt.status = 'active'
        ),
        consistency_analysis AS (
          SELECT 
            truck_id,
            truck_number,
            shift_driver_name,
            assignment_driver_name,
            trip_driver_name,
            shift_status,
            assignment_status,
            trip_status,
            CASE 
              WHEN shift_driver_id IS NULL THEN 'no_active_shift'
              WHEN assignment_driver_id IS NULL THEN 'no_assignment_driver'
              WHEN shift_driver_id = assignment_driver_id AND assignment_driver_id = trip_driver_id THEN 'fully_consistent'
              WHEN shift_driver_id = assignment_driver_id AND trip_driver_id IS NULL THEN 'no_recent_trips'
              WHEN shift_driver_id = assignment_driver_id THEN 'trip_driver_mismatch'
              WHEN shift_driver_id != assignment_driver_id THEN 'assignment_driver_mismatch'
              ELSE 'complex_inconsistency'
            END as consistency_status
          FROM system_consistency
        )
        SELECT 
          consistency_status,
          COUNT(*) as count,
          array_agg(
            jsonb_build_object(
              'truck_number', truck_number,
              'shift_driver', shift_driver_name,
              'assignment_driver', assignment_driver_name,
              'trip_driver', trip_driver_name,
              'shift_status', shift_status,
              'assignment_status', assignment_status,
              'trip_status', trip_status
            )
          ) as affected_records
        FROM consistency_analysis
        GROUP BY consistency_status
      `;

      const consistencyResult = await client.query(endToEndQuery);
      
      validation.details.consistency_breakdown = {};

      for (const row of consistencyResult.rows) {
        validation.details.consistency_breakdown[row.consistency_status] = {
          count: parseInt(row.count),
          records: row.affected_records
        };

        // Create issues for inconsistent states
        if (row.consistency_status === 'assignment_driver_mismatch') {
          validation.issues.push({
            id: `assignment_driver_mismatch_${Date.now()}`,
            type: 'cross_system_inconsistency',
            severity: 'critical',
            description: `${row.count} trucks have different drivers in Shift Management vs Assignment Management`,
            affected_records: row.affected_records.map(r => r.truck_number),
            auto_fixable: true,
            fix_suggestion: 'Synchronize assignment drivers with active shift drivers'
          });
        } else if (row.consistency_status === 'trip_driver_mismatch') {
          validation.issues.push({
            id: `trip_driver_mismatch_${Date.now()}`,
            type: 'trip_data_inconsistency',
            severity: 'warning',
            description: `${row.count} trucks have different drivers in recent trips vs current assignments`,
            affected_records: row.affected_records.map(r => r.truck_number),
            auto_fixable: false,
            fix_suggestion: 'Review trip logging process and driver capture functionality'
          });
        } else if (row.consistency_status === 'complex_inconsistency') {
          validation.issues.push({
            id: `complex_inconsistency_${Date.now()}`,
            type: 'multi_system_inconsistency',
            severity: 'critical',
            description: `${row.count} trucks have complex inconsistencies across all three systems`,
            affected_records: row.affected_records.map(r => r.truck_number),
            auto_fixable: false,
            fix_suggestion: 'Manual review required for complex inconsistencies'
          });
        }
      }

      validation.metrics.fully_consistent = validation.details.consistency_breakdown['fully_consistent']?.count || 0;
      validation.metrics.total_trucks_checked = consistencyResult.rows.reduce((sum, row) => sum + parseInt(row.count), 0);
      validation.metrics.consistency_percentage = validation.metrics.total_trucks_checked > 0 
        ? Math.round((validation.metrics.fully_consistent / validation.metrics.total_trucks_checked) * 100)
        : 0;

      if (validation.issues.length === 0) {
        validation.status = 'operational';
      } else if (validation.issues.some(issue => issue.severity === 'critical')) {
        validation.status = 'critical';
      } else {
        validation.status = 'warning';
      }

      return validation;

    } catch (error) {
      logError('END_TO_END_VALIDATION_ERROR', error);
      validation.status = 'critical';
      validation.issues.push({
        id: 'end_to_end_validation_error',
        type: 'system_error',
        severity: 'critical',
        description: `Error validating end-to-end consistency: ${error.message}`,
        affected_records: [],
        auto_fixable: false
      });
      return validation;
    }
  }

  /**
   * Validate driver capture accuracy in trip logging
   * @param {Object} client Database client
   * @returns {Promise<Object>} Validation results
   */
  static async validateDriverCaptureAccuracy(client) {
    const validation = {
      status: 'operational',
      issues: [],
      metrics: {},
      details: {}
    };

    try {
      // Query to check driver capture accuracy in recent trips
      const driverCaptureQuery = `
        WITH recent_trips AS (
          SELECT 
            tl.id,
            tl.assignment_id,
            tl.status,
            tl.performed_by_driver_id,
            tl.performed_by_driver_name,
            tl.performed_by_employee_id,
            tl.performed_by_shift_id,
            tl.performed_by_shift_type,
            tl.created_at,
            a.truck_id,
            dt.truck_number,
            -- Check what the active shift was at the time of trip creation
            ds.id as expected_shift_id,
            ds.driver_id as expected_driver_id,
            d.full_name as expected_driver_name,
            d.employee_id as expected_employee_id,
            ds.shift_type as expected_shift_type
          FROM trip_logs tl
          JOIN assignments a ON tl.assignment_id = a.id
          JOIN dump_trucks dt ON a.truck_id = dt.id
          LEFT JOIN driver_shifts ds ON (
            ds.truck_id = a.truck_id
            AND ds.status = 'active'
            AND tl.created_at::date BETWEEN ds.start_date AND COALESCE(ds.end_date, ds.start_date)
          )
          LEFT JOIN drivers d ON ds.driver_id = d.id
          WHERE tl.created_at >= CURRENT_DATE - INTERVAL '7 days'
        ),
        capture_accuracy AS (
          SELECT 
            id,
            truck_number,
            performed_by_driver_name,
            expected_driver_name,
            performed_by_shift_type,
            expected_shift_type,
            created_at,
            CASE 
              WHEN performed_by_driver_id IS NULL THEN 'no_driver_captured'
              WHEN expected_driver_id IS NULL THEN 'no_active_shift_found'
              WHEN performed_by_driver_id = expected_driver_id THEN 'accurate_capture'
              ELSE 'driver_mismatch'
            END as capture_status
          FROM recent_trips
        )
        SELECT 
          capture_status,
          COUNT(*) as count,
          array_agg(
            jsonb_build_object(
              'trip_id', id,
              'truck_number', truck_number,
              'captured_driver', performed_by_driver_name,
              'expected_driver', expected_driver_name,
              'captured_shift_type', performed_by_shift_type,
              'expected_shift_type', expected_shift_type,
              'created_at', created_at
            )
          ) as affected_records
        FROM capture_accuracy
        GROUP BY capture_status
      `;

      const captureResult = await client.query(driverCaptureQuery);
      
      validation.details.capture_breakdown = {};

      for (const row of captureResult.rows) {
        validation.details.capture_breakdown[row.capture_status] = {
          count: parseInt(row.count),
          records: row.affected_records
        };

        // Create issues for inaccurate captures
        if (row.capture_status === 'no_driver_captured') {
          validation.issues.push({
            id: `no_driver_captured_${Date.now()}`,
            type: 'driver_capture_failure',
            severity: 'critical',
            description: `${row.count} recent trips have no driver information captured`,
            affected_records: row.affected_records.map(r => r.trip_id),
            auto_fixable: false,
            fix_suggestion: 'Review captureActiveDriverInfo function and shift query logic'
          });
        } else if (row.capture_status === 'driver_mismatch') {
          validation.issues.push({
            id: `driver_capture_mismatch_${Date.now()}`,
            type: 'driver_capture_inaccuracy',
            severity: 'warning',
            description: `${row.count} recent trips captured wrong driver information`,
            affected_records: row.affected_records.map(r => r.trip_id),
            auto_fixable: false,
            fix_suggestion: 'Review driver capture timing and shift matching logic'
          });
        } else if (row.capture_status === 'no_active_shift_found') {
          validation.issues.push({
            id: `no_active_shift_for_trip_${Date.now()}`,
            type: 'shift_trip_timing_issue',
            severity: 'info',
            description: `${row.count} trips were created when no active shift was found (may be normal for some workflows)`,
            affected_records: row.affected_records.map(r => r.trip_id),
            auto_fixable: false,
            fix_suggestion: 'Review if these trips should have active shifts or if timing is correct'
          });
        }
      }

      validation.metrics.trips_with_driver_info = validation.details.capture_breakdown['accurate_capture']?.count || 0;
      validation.metrics.total_recent_trips = captureResult.rows.reduce((sum, row) => sum + parseInt(row.count), 0);
      validation.metrics.capture_accuracy_percentage = validation.metrics.total_recent_trips > 0 
        ? Math.round((validation.metrics.trips_with_driver_info / validation.metrics.total_recent_trips) * 100)
        : 0;

      if (validation.issues.length === 0) {
        validation.status = 'operational';
      } else if (validation.issues.some(issue => issue.severity === 'critical')) {
        validation.status = 'critical';
      } else {
        validation.status = 'warning';
      }

      return validation;

    } catch (error) {
      logError('DRIVER_CAPTURE_VALIDATION_ERROR', error);
      validation.status = 'critical';
      validation.issues.push({
        id: 'driver_capture_validation_error',
        type: 'system_error',
        severity: 'critical',
        description: `Error validating driver capture accuracy: ${error.message}`,
        affected_records: [],
        auto_fixable: false
      });
      return validation;
    }
  }

  /**
   * Create validation queries to verify data consistency across all systems
   * @returns {Object} SQL queries for manual validation
   */
  static getValidationQueries() {
    return {
      shift_to_assignment_consistency: `
        -- Check if active shifts have corresponding assignments with correct drivers
        SELECT 
          ds.id as shift_id,
          dt.truck_number,
          d.full_name as shift_driver,
          ds.shift_type,
          ds.status as shift_status,
          a.id as assignment_id,
          ad.full_name as assignment_driver,
          a.status as assignment_status,
          CASE 
            WHEN a.id IS NULL THEN 'No Assignment'
            WHEN a.driver_id IS NULL THEN 'Assignment No Driver'
            WHEN a.driver_id = ds.driver_id THEN 'Synchronized'
            ELSE 'Driver Mismatch'
          END as sync_status
        FROM driver_shifts ds
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        JOIN drivers d ON ds.driver_id = d.id
        LEFT JOIN assignments a ON ds.truck_id = a.truck_id AND a.status IN ('assigned', 'in_progress')
        LEFT JOIN drivers ad ON a.driver_id = ad.id
        WHERE ds.status = 'active'
          AND ds.start_date <= CURRENT_DATE
          AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
        ORDER BY dt.truck_number;
      `,
      
      assignment_to_trip_display: `
        -- Check how assignments appear in Trip Monitoring
        SELECT 
          a.id as assignment_id,
          dt.truck_number,
          d.full_name as assignment_driver,
          a.status as assignment_status,
          ds.id as active_shift_id,
          sd.full_name as shift_driver,
          ds.shift_type,
          CASE 
            WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
              CONCAT('✅ ', ds.shift_type, ' Shift Active')
            WHEN ds.id IS NOT NULL AND ds.status = 'scheduled' THEN
              CONCAT('📅 ', ds.shift_type, ' Shift Scheduled')
            ELSE '⚠️ No Active Shift'
          END as trip_monitoring_display
        FROM assignments a
        JOIN dump_trucks dt ON a.truck_id = dt.id
        LEFT JOIN drivers d ON a.driver_id = d.id
        LEFT JOIN driver_shifts ds ON (
          ds.truck_id = a.truck_id
          AND ds.status = 'active'
          AND ds.start_date <= CURRENT_DATE
          AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
        )
        LEFT JOIN drivers sd ON ds.driver_id = sd.id
        WHERE a.status IN ('assigned', 'in_progress')
        ORDER BY dt.truck_number;
      `,
      
      recent_trip_driver_capture: `
        -- Check driver capture accuracy in recent trips
        SELECT 
          tl.id as trip_id,
          dt.truck_number,
          tl.status as trip_status,
          tl.performed_by_driver_name as captured_driver,
          tl.performed_by_shift_type as captured_shift_type,
          tl.created_at,
          ds.id as expected_shift_id,
          d.full_name as expected_driver,
          ds.shift_type as expected_shift_type,
          CASE 
            WHEN tl.performed_by_driver_id IS NULL THEN 'No Driver Captured'
            WHEN ds.id IS NULL THEN 'No Active Shift Found'
            WHEN tl.performed_by_driver_id = ds.driver_id THEN 'Accurate Capture'
            ELSE 'Driver Mismatch'
          END as capture_accuracy
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        LEFT JOIN driver_shifts ds ON (
          ds.truck_id = a.truck_id
          AND ds.status = 'active'
          AND tl.created_at::date BETWEEN ds.start_date AND COALESCE(ds.end_date, ds.start_date)
        )
        LEFT JOIN drivers d ON ds.driver_id = d.id
        WHERE tl.created_at >= CURRENT_DATE - INTERVAL '3 days'
        ORDER BY tl.created_at DESC;
      `,
      
      end_to_end_consistency: `
        -- Check end-to-end consistency across all three systems
        SELECT 
          dt.truck_number,
          -- Shift Management
          ds.id as shift_id,
          sd.full_name as shift_driver,
          ds.shift_type,
          ds.status as shift_status,
          -- Assignment Management
          a.id as assignment_id,
          ad.full_name as assignment_driver,
          a.status as assignment_status,
          -- Trip Monitoring (most recent trip)
          tl.id as recent_trip_id,
          tl.performed_by_driver_name as trip_driver,
          tl.performed_by_shift_type as trip_shift_type,
          tl.status as trip_status,
          tl.created_at as trip_time,
          -- Consistency Check
          CASE 
            WHEN ds.driver_id IS NULL THEN 'No Active Shift'
            WHEN a.driver_id IS NULL THEN 'No Assignment Driver'
            WHEN ds.driver_id = a.driver_id AND a.driver_id = tl.performed_by_driver_id THEN 'Fully Consistent'
            WHEN ds.driver_id = a.driver_id AND tl.performed_by_driver_id IS NULL THEN 'No Recent Trips'
            WHEN ds.driver_id = a.driver_id THEN 'Trip Driver Mismatch'
            WHEN ds.driver_id != a.driver_id THEN 'Assignment Driver Mismatch'
            ELSE 'Complex Inconsistency'
          END as consistency_status
        FROM dump_trucks dt
        LEFT JOIN driver_shifts ds ON (
          ds.truck_id = dt.id 
          AND ds.status = 'active'
          AND ds.start_date <= CURRENT_DATE
          AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
        )
        LEFT JOIN drivers sd ON ds.driver_id = sd.id
        LEFT JOIN assignments a ON (
          a.truck_id = dt.id 
          AND a.status IN ('assigned', 'in_progress')
        )
        LEFT JOIN drivers ad ON a.driver_id = ad.id
        LEFT JOIN LATERAL (
          SELECT *
          FROM trip_logs tl2
          WHERE tl2.assignment_id = a.id
          ORDER BY tl2.created_at DESC
          LIMIT 1
        ) tl ON true
        WHERE dt.status = 'active'
        ORDER BY dt.truck_number;
      `
    };
  }
}

module.exports = DataFlowValidationService;