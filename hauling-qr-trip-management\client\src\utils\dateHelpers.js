/**
 * Date utility functions for shift management
 * Extracted from inline functions to promote reusability
 */

// Asia/Manila timezone-aware date formatter
const phDateFormatter = new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' });

export const getWeekStart = () => {
  // Get current date in Asia/Manila timezone
  const today = new Date();
  const todayStr = phDateFormatter.format(today);
  const todayDate = new Date(todayStr + 'T00:00:00'); // Parse as local date

  const day = todayDate.getDay();
  const diff = todayDate.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(todayDate.setDate(diff));
  return phDateFormatter.format(monday);
};

export const getWeekEnd = () => {
  // Get current date in Asia/Manila timezone
  const today = new Date();
  const todayStr = phDateFormatter.format(today);
  const todayDate = new Date(todayStr + 'T00:00:00'); // Parse as local date

  const day = todayDate.getDay();
  const diff = todayDate.getDate() - day + (day === 0 ? 0 : 7); // Adjust when day is Sunday
  const sunday = new Date(todayDate.setDate(diff));
  return phDateFormatter.format(sunday);
};

export const getMonthStart = () => {
  // Get current date in Asia/Manila timezone
  const today = new Date();
  const todayStr = phDateFormatter.format(today);
  const todayDate = new Date(todayStr + 'T00:00:00'); // Parse as local date

  return phDateFormatter.format(new Date(todayDate.getFullYear(), todayDate.getMonth(), 1));
};

export const getMonthEnd = () => {
  // Get current date in Asia/Manila timezone
  const today = new Date();
  const todayStr = phDateFormatter.format(today);
  const todayDate = new Date(todayStr + 'T00:00:00'); // Parse as local date

  return phDateFormatter.format(new Date(todayDate.getFullYear(), todayDate.getMonth() + 1, 0));
};

export const getToday = () => {
  return phDateFormatter.format(new Date());
};

export const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString();
};

export const formatDateTime = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString();
};

export const getDaysInRange = (dateFrom, dateTo) => {
  if (!dateFrom || !dateTo) return 0;
  const start = new Date(dateFrom);
  const end = new Date(dateTo);
  const diffTime = Math.abs(end - start);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
};

/**
 * Calculate duration between shift start and end times
 * Handles both same-day and overnight shifts
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} startTime - Start time (HH:MM:SS or HH:MM)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @param {string} endTime - End time (HH:MM:SS or HH:MM)
 * @returns {string} - Formatted duration string (e.g., "8h 30m") or "Invalid Date" if error
 */
export const calculateDuration = (startDate, startTime, endDate, endTime) => {
  try {
    // Validate inputs
    if (!startDate || !startTime || !endDate || !endTime) {
      return 'N/A';
    }

    // Normalize date format (handle both YYYY-MM-DD and ISO timestamp formats)
    const normalizeDate = (dateInput) => {
      if (!dateInput) return null;
      
      // If it's already in YYYY-MM-DD format, return as is
      if (typeof dateInput === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
        return dateInput;
      }
      
      // If it's an ISO timestamp, extract the date part
      if (typeof dateInput === 'string' && dateInput.includes('T')) {
        return dateInput.split('T')[0];
      }
      
      // Try to parse as Date and extract date part using Asia/Manila timezone
      try {
        const date = new Date(dateInput);
        if (!isNaN(date.getTime())) {
          return phDateFormatter.format(date);
        }
      } catch (e) {
        // Fall through to return null
      }
      
      return null;
    };

    // Normalize time format (ensure HH:MM:SS)
    const normalizeTime = (time) => {
      if (!time) return null;
      const parts = time.split(':');
      if (parts.length === 2) {
        return `${parts[0]}:${parts[1]}:00`;
      }
      return time;
    };

    const normalizedStartDate = normalizeDate(startDate);
    const normalizedEndDate = normalizeDate(endDate);
    const normalizedStartTime = normalizeTime(startTime);
    const normalizedEndTime = normalizeTime(endTime);

    if (!normalizedStartDate || !normalizedEndDate || !normalizedStartTime || !normalizedEndTime) {
      return 'Invalid Time';
    }

    // Create full datetime strings
    const startDateTime = `${normalizedStartDate}T${normalizedStartTime}`;
    const endDateTime = `${normalizedEndDate}T${normalizedEndTime}`;

    // Create Date objects
    const startDateObj = new Date(startDateTime);
    const endDateObj = new Date(endDateTime);

    // Validate Date objects
    if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
      return 'Invalid Date';
    }

    // Handle overnight shifts (end time is next day)
    let adjustedEndDate = endDateObj;
    if (normalizedStartDate === normalizedEndDate && normalizedEndTime < normalizedStartTime) {
      // Same date but end time is earlier than start time = overnight shift
      adjustedEndDate = new Date(endDateObj.getTime() + 24 * 60 * 60 * 1000);
    }

    // Calculate duration in milliseconds
    const durationMs = adjustedEndDate.getTime() - startDateObj.getTime();

    // Handle negative duration (shouldn't happen with proper data)
    if (durationMs < 0) {
      return 'Invalid Range';
    }

    // Convert to hours and minutes (ceil to next minute to match HH:MM display semantics)
    const totalMinutes = Math.ceil(durationMs / (1000 * 60));
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    // Format duration string
    if (hours === 0 && minutes === 0) {
      return '0m';
    } else if (hours === 0) {
      return `${minutes}m`;
    } else if (minutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${minutes}m`;
    }

  } catch (error) {
    console.error('Error calculating duration:', error, {
      startDate,
      startTime,
      endDate,
      endTime
    });
    return 'Error';
  }
};