/**
 * Contact Settings API Service
 * Handles API calls for contact information settings
 */

import axios from 'axios';
import { getApiBaseUrl } from '../utils/network-utils';

class ContactSettingsAPI {
  constructor() {
    this.cache = null;
    this.cacheTimestamp = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache
  }

  /**
   * Get all contact settings with caching
   */
  async getContactSettings() {
    try {
      // Check cache first
      if (this.cache && this.cacheTimestamp && 
          (Date.now() - this.cacheTimestamp) < this.cacheTimeout) {
        return this.cache;
      }

      const token = localStorage.getItem('hauling_token');
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const config = {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      const apiBaseUrl = getApiBaseUrl();
      const response = await axios.get(`${apiBaseUrl}/contact-settings`, config);

      if (response.data.success) {
        // Update cache
        this.cache = response.data.data;
        this.cacheTimestamp = Date.now();
        return this.cache;
      } else {
        throw new Error(response.data.message || 'Failed to fetch contact settings');
      }
    } catch (error) {
      console.error('Error fetching contact settings:', error);
      
      // Return fallback data if API fails
      return this.getFallbackContactSettings();
    }
  }

  /**
   * Get contact information for a specific category and status
   */
  async getContactInfo(category, statusType) {
    try {
      const settings = await this.getContactSettings();
      return settings[category]?.[statusType] || this.getFallbackContactInfo(category, statusType);
    } catch (error) {
      console.error('Error getting contact info:', error);
      return this.getFallbackContactInfo(category, statusType);
    }
  }

  /**
   * Save or update contact setting
   */
  async saveContactSetting(contactData) {
    try {
      const token = localStorage.getItem('hauling_token');
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const config = {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      const apiBaseUrl = getApiBaseUrl();
      const response = await axios.post(`${apiBaseUrl}/contact-settings`, contactData, config);

      if (response.data.success) {
        // Clear cache to force refresh
        this.clearCache();
        return response.data;
      } else {
        throw new Error(response.data.message || 'Failed to save contact setting');
      }
    } catch (error) {
      console.error('Error saving contact setting:', error);
      throw error;
    }
  }

  /**
   * Delete contact setting
   */
  async deleteContactSetting(id) {
    try {
      const token = localStorage.getItem('hauling_token');
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const config = {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      const apiBaseUrl = getApiBaseUrl();
      const response = await axios.delete(`${apiBaseUrl}/contact-settings/${id}`, config);

      if (response.data.success) {
        // Clear cache to force refresh
        this.clearCache();
        return response.data;
      } else {
        throw new Error(response.data.message || 'Failed to delete contact setting');
      }
    } catch (error) {
      console.error('Error deleting contact setting:', error);
      throw error;
    }
  }

  /**
   * Clear the cache
   */
  clearCache() {
    this.cache = null;
    this.cacheTimestamp = null;
  }

  /**
   * Get fallback contact settings when API is unavailable
   */
  getFallbackContactSettings() {
    return {
      driver: {
        inactive: {
          primary: 'Supervisor',
          phone: '(*************',
          email: '<EMAIL>'
        },
        suspended: {
          primary: 'HR Department',
          phone: '(*************',
          email: '<EMAIL>'
        },
        on_leave: {
          primary: 'Supervisor',
          phone: '(*************',
          email: '<EMAIL>'
        },
        terminated: {
          primary: 'HR Department',
          phone: '(*************',
          email: '<EMAIL>'
        }
      },
      truck: {
        inactive: {
          primary: 'Fleet Manager',
          phone: '(*************',
          email: '<EMAIL>'
        },
        maintenance: {
          primary: 'Maintenance Team',
          phone: '(*************',
          email: '<EMAIL>'
        },
        retired: {
          primary: 'Fleet Manager',
          phone: '(*************',
          email: '<EMAIL>'
        }
      }
    };
  }

  /**
   * Get fallback contact info for specific category and status
   */
  getFallbackContactInfo(category, statusType) {
    const fallbackSettings = this.getFallbackContactSettings();
    return fallbackSettings[category]?.[statusType] || {
      primary: 'System Administrator',
      phone: '(555) 123-HELP',
      email: '<EMAIL>'
    };
  }
}

// Create and export singleton instance
const contactSettingsAPI = new ContactSettingsAPI();
export default contactSettingsAPI;
