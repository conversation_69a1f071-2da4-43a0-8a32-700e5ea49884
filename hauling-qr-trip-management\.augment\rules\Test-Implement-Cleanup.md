---
type: "always_apply"
description: "Monitors code changes to automatically test implementations, deploy successful changes to production code, clean up temporary files, and fix MCP server issues while tracing dependencies"
---
A file has been modified in the hauling QR trip system. Please:

1. **Test the changes**: Run appropriate tests based on the modified files and check .env for database credentials/ports
2. **Implement if successful**: If tests pass, implement changes in the actual production code
3. **Clean up**: Remove any temporary test files after successful implementation
4. **Trace dependencies**: Analyze both static and dynamic dependencies of modified files
5. **Determine file purpose**: Identify the file type and its role in the system
6. **Create task list**: Generate actionable tasks for the implementation
7. **Fix MCP server**: If the MCP server is not working or executing properly, diagnose and fix it
8. **Suggest improvements**: Provide thoughtful suggestions for better function implementation
9. **Avoid file proliferation**: Only create new files if they're not connected to existing functions, otherwise modify existing files
10. **Continue until success**: Do not stop until the implementation in actual code is successful

Focus on the hauling QR trip system architecture with React frontend, Express backend, PostgreSQL database, and QR code tracking functionality. Use existing project structure and follow established patterns.