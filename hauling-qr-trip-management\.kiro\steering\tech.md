# Technology Stack & Build System

## Architecture
Full-stack web application with separate client and server components, unified configuration system, and PostgreSQL database.

## Tech Stack

### Frontend (Client)
- **Framework**: React 18.2.0 with React Router DOM for SPA routing
- **Styling**: Tailwind CSS 3.3.6 with custom design system (primary/secondary/success/warning/danger color palettes)
- **QR Code**: Multiple libraries for scanning and generation (@yudiel/react-qr-scanner, html5-qrcode, qrcode.react)
- **Charts**: Chart.js with react-chartjs-2 for analytics dashboards
- **Forms**: React Hook Form for form management
- **HTTP Client**: Axios for API communication
- **Notifications**: React Hot Toast for user feedback
- **File Handling**: JSZip, file-saver, pdf-lib for document generation
- **Build Tool**: Create React App (CRA) with custom configurations

### Backend (Server)
- **Runtime**: Node.js with Express.js framework
- **Database**: PostgreSQL with pg driver and connection pooling
- **Authentication**: JWT with bcryptjs for password hashing
- **Security**: Helmet, CORS, express-rate-limit
- **WebSocket**: ws library for real-time communication
- **QR Processing**: qrcode generation, jsqr for reading
- **Image Processing**: Sharp for image manipulation
- **Validation**: Joi for request validation
- **Logging**: Winston for structured logging
- **File Upload**: Multer for handling file uploads

### Database
- **Primary**: PostgreSQL with advanced features (JSONB, GIN indexes, materialized views)
- **Migration System**: Custom Node.js migration runner
- **Performance**: Connection pooling, optimized indexes, materialized views for analytics

## Build Commands

### Development
```bash
# Start full system in development mode
npm run dev

# Start individual components
cd client && npm start          # Frontend dev server (port 3000)
cd server && npm run dev        # Backend with nodemon (port 5000)

# Database operations
npm run db:migrate              # Run database migrations
```

### Production
```bash
# Build frontend for production
cd client && npm run build

# Start production server
npm start                       # Starts server.js in production mode

# Production with HTTPS
node scripts/start-prod-https.js
```

### Testing
```bash
# Run all tests
npm test

# Server-specific tests
cd server && npm test
cd server && npm run test:coverage    # With coverage report
cd server && npm run test:exceptions  # Exception workflow tests

# Client tests
cd client && npm test
```

## Configuration System

### Unified Environment Configuration
- Single `.env` file for both client and server
- Automatic development/production mode switching
- Auto-detection of network IP addresses
- Simplified CORS and SSL configuration

### Key Environment Variables
- `NODE_ENV`: development/production mode
- `AUTO_DETECT_IP`: Automatic IP detection for local network
- `ENABLE_HTTPS`: Toggle HTTPS for both client and server
- `DB_*`: Database connection settings
- `JWT_SECRET`: Authentication secret
- `REACT_APP_*`: Client-side environment variables

## Development Workflow

### Port Configuration
- Frontend: Always port 3000 (HTTP/HTTPS)
- Backend: Always port 5000 (HTTP/HTTPS)
- Database: PostgreSQL on port 5432

### SSL/HTTPS Support
- Development: Self-signed certificates in `./ssl/dev/`
- Production: CA-signed certificates in `./ssl/production/`
- Automatic certificate selection based on NODE_ENV

### Code Style & Linting
- ESLint configuration with React app defaults
- Relaxed rules: no-console off, no-unused-vars as warnings
- Tailwind CSS with custom design system and animations
- PostCSS with Autoprefixer for browser compatibility