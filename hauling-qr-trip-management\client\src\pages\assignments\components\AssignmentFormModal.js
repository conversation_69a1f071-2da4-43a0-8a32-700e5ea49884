import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { trucksAPI, locationsAPI } from '../../../services/api';

const AssignmentFormModal = ({ assignment, onClose, onSubmit }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  
  // Dropdown data
  const [trucks, setTrucks] = useState([]);
  const [locations, setLocations] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm({
    defaultValues: {
      truck_id: '',
      loading_location_id: '',
      unloading_location_id: '',
      assigned_date: new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(new Date()),
      start_time: '',
      end_time: '',
      status: 'assigned',
      expected_loads_per_day: 1,
      notes: ''
    }
  });

  // Watch for start_time to validate end_time
  const startTime = watch('start_time');

  // Load dropdown data - Driver selection removed (managed by shift system)
  useEffect(() => {
    const loadDropdownData = async () => {
      setLoading(true);
      try {
        const [trucksResponse, locationsResponse] = await Promise.all([
          trucksAPI.getAll({ params: { limit: 100, status: 'active' } }),
          locationsAPI.getAll({ params: { limit: 100, status: 'active' } })
        ]);

        setTrucks(trucksResponse.data.data || []);
        setLocations(locationsResponse.data.data || []);
      } catch (error) {
        console.error('Error loading dropdown data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDropdownData();
  }, []);

  // Reset form when assignment changes
  useEffect(() => {
    if (assignment) {
      reset({
        truck_id: assignment.truck_id || '',
        loading_location_id: assignment.loading_location_id || '',
        unloading_location_id: assignment.unloading_location_id || '',
        assigned_date: assignment.assigned_date ? assignment.assigned_date.split('T')[0] : new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(new Date()),
        start_time: assignment.start_time ? new Date(assignment.start_time).toISOString().slice(0, 16) : '',
        end_time: assignment.end_time ? new Date(assignment.end_time).toISOString().slice(0, 16) : '',
        status: assignment.status || 'assigned',
        expected_loads_per_day: assignment.expected_loads_per_day || 1,
        notes: assignment.notes || ''
      });
    } else {
      reset({
        truck_id: '',
        loading_location_id: '',
        unloading_location_id: '',
        assigned_date: new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(new Date()),
        start_time: '',
        end_time: '',
        status: 'assigned',
        expected_loads_per_day: 1,
        notes: ''
      });
    }
  }, [assignment, reset]);

  const onFormSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // Convert form data to match API expectations
      const formData = {
        ...data,
        truck_id: parseInt(data.truck_id),
        // driver_id removed - will be determined by shift management
        loading_location_id: parseInt(data.loading_location_id),
        unloading_location_id: parseInt(data.unloading_location_id),
        expected_loads_per_day: parseInt(data.expected_loads_per_day),
        start_time: data.start_time ? new Date(data.start_time).toISOString() : null,
        end_time: data.end_time ? new Date(data.end_time).toISOString() : null,
        assigned_date: new Intl.DateTimeFormat('en-CA', { timeZone: 'Asia/Manila' }).format(new Date(data.assigned_date))
      };

      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Filter locations by type
  const loadingLocations = locations.filter(loc => loc.type === 'loading');
  const unloadingLocations = locations.filter(loc => loc.type === 'unloading');

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-secondary-200 rounded w-1/3"></div>
            <div className="space-y-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-12 bg-secondary-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-200">
          <h2 className="text-xl font-semibold text-secondary-900">
            {assignment ? 'Edit Assignment' : 'Create New Assignment'}
          </h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-secondary-400 hover:text-secondary-600 transition-colors disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onFormSubmit)} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Truck Selection */}
            <div>
              <label htmlFor="truck_id" className="block text-sm font-medium text-secondary-700 mb-1">
                Truck *
              </label>
              <select
                id="truck_id"
                {...register('truck_id', {
                  required: 'Truck selection is required'
                })}
                className={`input ${errors.truck_id ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                disabled={isSubmitting}
              >
                <option value="">Select a truck</option>
                {trucks.map((truck) => (
                  <option key={truck.id} value={truck.id}>
                    {truck.truck_number} - {truck.make} {truck.model} ({truck.license_plate})
                  </option>
                ))}
              </select>
              {errors.truck_id && (
                <p className="text-danger-600 text-sm mt-1">{errors.truck_id.message}</p>
              )}
            </div>

            {/* Driver Assignment Note */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <span className="text-blue-600 text-lg">👥</span>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-blue-800">Driver Assignment</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Drivers are now managed through <strong>Shift Management</strong>.
                    Create shifts to assign drivers to trucks with specific time schedules.
                  </p>
                  <p className="text-xs text-blue-600 mt-2">
                    The current driver will be automatically determined based on active shifts.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Loading Location */}
            <div>
              <label htmlFor="loading_location_id" className="block text-sm font-medium text-secondary-700 mb-1">
                Loading Location *
              </label>
              <select
                id="loading_location_id"
                {...register('loading_location_id', {
                  required: 'Loading location is required'
                })}
                className={`input ${errors.loading_location_id ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                disabled={isSubmitting}
              >
                <option value="">Select loading location</option>
                {loadingLocations.map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name} ({location.location_code})
                  </option>
                ))}
              </select>
              {errors.loading_location_id && (
                <p className="text-danger-600 text-sm mt-1">{errors.loading_location_id.message}</p>
              )}
            </div>

            {/* Unloading Location */}
            <div>
              <label htmlFor="unloading_location_id" className="block text-sm font-medium text-secondary-700 mb-1">
                Unloading Location *
              </label>
              <select
                id="unloading_location_id"
                {...register('unloading_location_id', {
                  required: 'Unloading location is required'
                })}
                className={`input ${errors.unloading_location_id ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                disabled={isSubmitting}
              >
                <option value="">Select unloading location</option>
                {unloadingLocations.map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name} ({location.location_code})
                  </option>
                ))}
              </select>
              {errors.unloading_location_id && (
                <p className="text-danger-600 text-sm mt-1">{errors.unloading_location_id.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Assigned Date */}
            <div>
              <label htmlFor="assigned_date" className="block text-sm font-medium text-secondary-700 mb-1">
                Assigned Date *
              </label>
              <input
                type="date"
                id="assigned_date"
                {...register('assigned_date', {
                  required: 'Assigned date is required'
                })}
                className={`input ${errors.assigned_date ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                disabled={isSubmitting}
              />
              {errors.assigned_date && (
                <p className="text-danger-600 text-sm mt-1">{errors.assigned_date.message}</p>
              )}
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-secondary-700 mb-1">
                Status
              </label>
              <select
                id="status"
                {...register('status')}
                className="input"
                disabled={isSubmitting}
              >
                <option value="assigned">Assigned</option>
                <option value="pending_approval">Pending Approval</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            {/* Expected Loads */}
            <div>
              <label htmlFor="expected_loads_per_day" className="block text-sm font-medium text-secondary-700 mb-1">
                Expected Loads/Day
              </label>
              <input
                type="number"
                id="expected_loads_per_day"
                min="1"
                max="50"
                {...register('expected_loads_per_day', {
                  valueAsNumber: true,
                  min: {
                    value: 1,
                    message: 'Expected loads must be at least 1'
                  },
                  max: {
                    value: 50,
                    message: 'Expected loads cannot exceed 50'
                  }
                })}
                className={`input ${errors.expected_loads_per_day ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                disabled={isSubmitting}
              />
              {errors.expected_loads_per_day && (
                <p className="text-danger-600 text-sm mt-1">{errors.expected_loads_per_day.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Start Time */}
            <div>
              <label htmlFor="start_time" className="block text-sm font-medium text-secondary-700 mb-1">
                Start Time (Optional)
              </label>
              <input
                type="datetime-local"
                id="start_time"
                {...register('start_time')}
                className="input"
                disabled={isSubmitting}
              />
            </div>

            {/* End Time */}
            <div>
              <label htmlFor="end_time" className="block text-sm font-medium text-secondary-700 mb-1">
                End Time (Optional)
              </label>
              <input
                type="datetime-local"
                id="end_time"
                {...register('end_time', {
                  validate: (value) => {
                    if (value && startTime && new Date(value) <= new Date(startTime)) {
                      return 'End time must be after start time';
                    }
                    return true;
                  }
                })}
                className={`input ${errors.end_time ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                disabled={isSubmitting}
              />
              {errors.end_time && (
                <p className="text-danger-600 text-sm mt-1">{errors.end_time.message}</p>
              )}
            </div>
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-secondary-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              rows={3}
              {...register('notes', {
                maxLength: {
                  value: 1000,
                  message: 'Notes must not exceed 1000 characters'
                }
              })}
              className={`input ${errors.notes ? 'border-danger-500 focus:ring-danger-500' : ''}`}
              placeholder="Additional notes about this assignment..."
              disabled={isSubmitting}
            />
            {errors.notes && (
              <p className="text-danger-600 text-sm mt-1">{errors.notes.message}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-secondary-200">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  {assignment ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                assignment ? 'Update Assignment' : 'Create Assignment'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssignmentFormModal;