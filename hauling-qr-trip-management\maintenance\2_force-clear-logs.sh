#!/bin/bash

# =============================================================================
# Consolidated Log Clearing Script - Hauling QR Trip System
# =============================================================================
# This script consolidates functionality from clear-all-logs.sh and 
# force-clear-logs.sh to provide comprehensive log clearing capabilities.
#
# Features:
# - Comprehensive log discovery and clearing
# - Multiple clearing methods with fallbacks
# - Optional service stopping for aggressive clearing
# - Support for both root and non-root execution
# - Handles compressed logs and rotated files
# - Detailed reporting and verification
#
# Usage: 
#   ./force-clear-logs.sh           # Standard clearing (preserves services)
#   ./force-clear-logs.sh --force   # Aggressive clearing (stops services)
#   sudo ./force-clear-logs.sh      # Full system access
# =============================================================================

# set -e  # Temporarily disabled to debug

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/hauling-qr-system"
DEPLOYMENT_LOG_DIR="/var/log/hauling-deployment"
FORCE_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force|-f)
            FORCE_MODE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --force, -f    Stop services before clearing (requires root)"
            echo "  --help, -h     Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0              # Standard log clearing"
            echo "  $0 --force      # Aggressive clearing with service restart"
            echo "  sudo $0 --force # Full system access with service restart"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Display header based on mode
if [[ "$FORCE_MODE" == "true" ]]; then
    echo -e "${RED}🚨 FORCE CLEARING ALL LOGS - HAULING QR SYSTEM${NC}"
    echo -e "${RED}===============================================${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  This will temporarily stop services to clear logs!${NC}"
else
    echo -e "${BLUE}🧹 Clearing All Hauling QR System Logs${NC}"
    echo -e "${BLUE}=====================================${NC}"
fi

echo ""

# Check permissions and warn appropriately
if [[ $EUID -ne 0 ]]; then
    if [[ "$FORCE_MODE" == "true" ]]; then
        echo -e "${RED}❌ Force mode requires root privileges (use sudo)${NC}"
        exit 1
    else
        echo -e "${YELLOW}⚠️  Running without root - some system logs may be inaccessible${NC}"
        echo "   For full system access, run: sudo $0"
        echo ""
        echo -e "${CYAN}Proceeding with current user permissions...${NC}"
    fi
else
    echo -e "${GREEN}✅ Running with root privileges - full system access available${NC}"
fi

echo ""

# Confirmation for force mode
if [[ "$FORCE_MODE" == "true" ]]; then
    read -p "Are you sure you want to force clear all logs? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${CYAN}Operation cancelled${NC}"
        exit 0
    fi
    echo ""
fi

# Function to safely clear log file with multiple methods
clear_log_file() {
    local file="$1"
    local description="$2"
    
    if [[ -f "$file" ]]; then
        local size_before=$(du -h "$file" 2>/dev/null | cut -f1 || echo "0")
        
        # Try multiple methods to clear the file
        if truncate -s 0 "$file" 2>/dev/null; then
            echo -e "${GREEN}✅ Cleared: $description ($size_before) [truncate]${NC}"
        elif > "$file" 2>/dev/null; then
            echo -e "${GREEN}✅ Cleared: $description ($size_before) [redirect]${NC}"
        elif echo -n > "$file" 2>/dev/null; then
            echo -e "${GREEN}✅ Cleared: $description ($size_before) [echo]${NC}"
        elif [[ -w "$file" ]]; then
            cat /dev/null > "$file" 2>/dev/null && echo -e "${GREEN}✅ Cleared: $description ($size_before) [cat]${NC}" || echo -e "${RED}❌ Failed to clear: $description${NC}"
        elif [[ "$FORCE_MODE" == "true" && $EUID -eq 0 ]]; then
            # Last resort in force mode - delete and recreate
            local owner=$(stat -c '%U:%G' "$file" 2>/dev/null || echo "ubuntu:ubuntu")
            local perms=$(stat -c '%a' "$file" 2>/dev/null || echo "644")
            rm -f "$file" 2>/dev/null || true
            touch "$file" 2>/dev/null || true
            chown "$owner" "$file" 2>/dev/null || true
            chmod "$perms" "$file" 2>/dev/null || true
            echo -e "${YELLOW}⚠️  Recreated: $description ($size_before)${NC}"
        else
            echo -e "${YELLOW}⚠️  No write permission: $description (owner: $(stat -c '%U:%G' "$file" 2>/dev/null || echo "unknown"))${NC}"
        fi
    else
        echo -e "${CYAN}ℹ️  Not found: $description${NC}"
    fi
}

# Function to clear directory logs (simplified version)
clear_log_directory() {
    local dir="$1"
    local description="$2"
    local pattern="$3"
    local delete_compressed="${4:-false}"
    
    if [[ -d "$dir" ]]; then
        # Use a simpler approach - get list of files first
        local files=($(find "$dir" -name "$pattern" -type f 2>/dev/null))
        local count=${#files[@]}
        
        if [[ $count -gt 0 ]]; then
            local total_size=$(find "$dir" -name "$pattern" -type f -exec du -ch {} + 2>/dev/null | tail -1 | cut -f1 2>/dev/null || echo "0")
            
            # Clear each file individually
            local cleared=0
            for file in "${files[@]}"; do
                if [[ -f "$file" ]]; then
                    if truncate -s 0 "$file" 2>/dev/null; then
                        ((cleared++))
                    elif > "$file" 2>/dev/null; then
                        ((cleared++))
                    else
                        echo -e "${YELLOW}⚠️  Could not clear: $(basename "$file")${NC}"
                    fi
                fi
            done
            
            echo -e "${GREEN}✅ Cleared: $description ($cleared/$count files, $total_size)${NC}"
        else
            echo -e "${CYAN}ℹ️  No log files found: $description${NC}"
        fi
        
        # Handle compressed logs if in force mode
        if [[ "$delete_compressed" == "true" && "$FORCE_MODE" == "true" ]]; then
            local compressed_files=($(find "$dir" -name "*.gz" -type f 2>/dev/null))
            local compressed_count=${#compressed_files[@]}
            if [[ $compressed_count -gt 0 ]]; then
                local compressed_size=$(find "$dir" -name "*.gz" -type f -exec du -ch {} + 2>/dev/null | tail -1 | cut -f1 2>/dev/null || echo "0")
                find "$dir" -name "*.gz" -type f -delete 2>/dev/null || true
                echo -e "${GREEN}✅ Removed: $compressed_count compressed logs from $description ($compressed_size)${NC}"
            fi
        fi
    else
        echo -e "${CYAN}ℹ️  Directory not found: $description${NC}"
    fi
}

# Function to stop services (force mode only)
stop_services() {
    if [[ "$FORCE_MODE" != "true" ]]; then
        return 0
    fi
    
    echo -e "${YELLOW}🛑 Step 1: Stopping Services${NC}"
    
    # Stop services that might be writing to logs
    echo -e "${CYAN}Stopping PM2...${NC}"
    pm2 stop all 2>/dev/null || true
    pm2 kill 2>/dev/null || true
    
    echo -e "${CYAN}Stopping Nginx...${NC}"
    systemctl stop nginx 2>/dev/null || true
    
    echo -e "${CYAN}Stopping PostgreSQL...${NC}"
    systemctl stop postgresql 2>/dev/null || true
    
    sleep 3
    echo ""
}

# Function to start services (force mode only)
start_services() {
    if [[ "$FORCE_MODE" != "true" ]]; then
        return 0
    fi
    
    echo -e "${YELLOW}🚀 Step: Restarting Services${NC}"
    
    # Restart services
    echo -e "${CYAN}Starting PostgreSQL...${NC}"
    systemctl start postgresql
    sleep 2
    
    echo -e "${CYAN}Starting Nginx...${NC}"
    systemctl start nginx
    sleep 2
    
    echo -e "${CYAN}Starting PM2...${NC}"
    cd "$APP_DIR" 2>/dev/null || cd /var/www/hauling-qr-system
    pm2 start ecosystem.config.js 2>/dev/null || pm2 start server/server.js --name hauling-qr-server
    sleep 3
    echo ""
}

# Stop services if in force mode
stop_services

# Log discovery and size reporting
if [[ "$FORCE_MODE" == "true" ]]; then
    echo -e "${YELLOW}🔍 Step 2: Log Discovery${NC}"
    
    # Show what logs we found before clearing
    echo -e "${CYAN}Discovering all log files...${NC}"
    
    echo -e "${BLUE}PM2 Logs:${NC}"
    [[ -d "/root/.pm2/logs" ]] && du -sh /root/.pm2/logs 2>/dev/null && ls -la /root/.pm2/logs/ | head -5
    [[ -d "/home/<USER>/.pm2/logs" ]] && du -sh /home/<USER>/.pm2/logs 2>/dev/null && ls -la /home/<USER>/.pm2/logs/ | head -5
    
    echo -e "${BLUE}Nginx Logs:${NC}"
    [[ -d "/var/log/nginx" ]] && du -sh /var/log/nginx 2>/dev/null && ls -la /var/log/nginx/ | head -10
    
    echo -e "${BLUE}PostgreSQL Logs:${NC}"
    [[ -d "/var/log/postgresql" ]] && du -sh /var/log/postgresql 2>/dev/null && ls -la /var/log/postgresql/ | head -5
    
    echo -e "${BLUE}NPM Logs:${NC}"
    [[ -d "/home/<USER>/.npm/_logs" ]] && du -sh /home/<USER>/.npm/_logs 2>/dev/null
    [[ -d "/root/.npm/_logs" ]] && du -sh /root/.npm/_logs 2>/dev/null
    
    echo ""
fi

echo -e "${YELLOW}📋 Step: PM2 Logs${NC}"

# PM2 logs (user-specific)
if command -v pm2 >/dev/null 2>&1; then
    # Get current user's PM2 directory
    PM2_HOME="${PM2_HOME:-$HOME/.pm2}"
    
    echo -e "${CYAN}PM2 Home: $PM2_HOME${NC}"
    
    # Clear PM2 logs using PM2 command
    if pm2 list >/dev/null 2>&1; then
        echo -e "${CYAN}Flushing PM2 logs...${NC}"
        pm2 flush 2>/dev/null || true
        echo -e "${GREEN}✅ PM2 logs flushed${NC}"
    fi
    
    # Clear PM2 log files directly
    clear_log_directory "$PM2_HOME/logs" "PM2 log files" "*.log"
    
    # Clear specific hauling-qr-server logs
    clear_log_file "$PM2_HOME/logs/hauling-qr-server-out.log" "PM2 hauling-qr-server output log"
    clear_log_file "$PM2_HOME/logs/hauling-qr-server-error.log" "PM2 hauling-qr-server error log"
    clear_log_file "$PM2_HOME/logs/hauling-qr-server.log" "PM2 hauling-qr-server combined log"
    
    # Check for root PM2 logs if running as root
    if [[ $EUID -eq 0 ]]; then
        clear_log_directory "/root/.pm2/logs" "Root PM2 log files" "*.log"
    fi
    
    # Also check ubuntu user PM2 logs (the actual active ones)
    if [[ -d "/home/<USER>/.pm2/logs" ]]; then
        echo -e "${CYAN}Clearing ubuntu user PM2 logs...${NC}"
        
        if [[ "$FORCE_MODE" == "true" ]]; then
            echo -e "${CYAN}Found ubuntu user PM2 logs (active logs with CORS spam):${NC}"
            ls -lh /home/<USER>/.pm2/logs/ 2>/dev/null
            
            # Show sizes before clearing
            echo -e "${YELLOW}Sizes before clearing:${NC}"
            du -h /home/<USER>/.pm2/logs/*.log 2>/dev/null | head -5
        fi
        
        clear_log_directory "/home/<USER>/.pm2/logs" "Ubuntu user PM2 log files" "*.log"
        
        # Use PM2 flush for ubuntu user
        if sudo -u ubuntu pm2 flush 2>/dev/null; then
            echo -e "${GREEN}✅ Ubuntu user PM2 logs flushed${NC}"
        else
            echo -e "${YELLOW}⚠️  Could not flush ubuntu user PM2 logs${NC}"
        fi
    fi
else
    echo -e "${YELLOW}⚠️  PM2 not found${NC}"
fi

echo ""
echo -e "${YELLOW}📋 Step: Application Logs${NC}"

# Application-specific logs (from ecosystem.config.js)
clear_log_file "$APP_DIR/server/logs/err.log" "Application error log"
clear_log_file "$APP_DIR/server/logs/out.log" "Application output log"  
clear_log_file "$APP_DIR/server/logs/combined.log" "Application combined log"

# Clear entire server logs directory
clear_log_directory "$APP_DIR/server/logs" "Server log directory" "*.log"

# Application logs that might be in other locations
clear_log_file "$APP_DIR/logs/app.log" "Application log"
clear_log_file "$APP_DIR/server/app.log" "Server application log"
clear_log_file "/home/<USER>/logs/hauling-qr-app.log" "Ubuntu user app log"

echo ""
echo -e "${YELLOW}📋 Step: Deployment Logs${NC}"

# Deployment logs
clear_log_directory "$DEPLOYMENT_LOG_DIR" "Deployment logs" "auto-deploy-*.log"
clear_log_directory "$DEPLOYMENT_LOG_DIR" "All deployment logs" "*.log"

echo ""
echo -e "${YELLOW}📋 Step: System Service Logs${NC}"

# Nginx logs (if accessible)
if [[ $EUID -eq 0 ]]; then
    # In force mode, stop nginx temporarily to clear logs
    if [[ "$FORCE_MODE" == "true" ]]; then
        echo -e "${CYAN}Nginx temporarily stopped for log clearing${NC}"
    else
        echo -e "${CYAN}Stopping Nginx temporarily to clear logs...${NC}"
        systemctl stop nginx 2>/dev/null || true
        sleep 1
    fi
    
    # Show what we're about to clear in force mode
    if [[ "$FORCE_MODE" == "true" && -d "/var/log/nginx" ]]; then
        echo -e "${CYAN}Found Nginx logs:${NC}"
        ls -lh /var/log/nginx/ | grep -E "\.(log|gz)$" | head -10
    fi
    
    clear_log_file "/var/log/nginx/access.log" "Nginx access log"
    clear_log_file "/var/log/nginx/error.log" "Nginx error log"
    
    # Clear rotated logs in force mode
    if [[ "$FORCE_MODE" == "true" ]]; then
        echo -e "${CYAN}Clearing Nginx rotated logs...${NC}"
        find /var/log/nginx -name "access.log.*" -type f ! -name "*.gz" -exec truncate -s 0 {} \; 2>/dev/null || true
        find /var/log/nginx -name "error.log.*" -type f ! -name "*.gz" -exec truncate -s 0 {} \; 2>/dev/null || true
        
        # Remove compressed rotated logs (they're old and safe to delete)
        echo -e "${CYAN}Removing compressed Nginx logs...${NC}"
        local compressed_count=$(find /var/log/nginx -name "*.gz" -type f 2>/dev/null | wc -l)
        if [[ $compressed_count -gt 0 ]]; then
            local compressed_size=$(find /var/log/nginx -name "*.gz" -type f -exec du -ch {} + 2>/dev/null | tail -1 | cut -f1 || echo "0")
            find /var/log/nginx -name "*.gz" -type f -delete 2>/dev/null || true
            echo -e "${GREEN}✅ Removed $compressed_count compressed Nginx logs ($compressed_size)${NC}"
        else
            echo -e "${CYAN}ℹ️  No compressed Nginx logs found${NC}"
        fi
    fi
    
    # Restart nginx if not in force mode (force mode handles this later)
    if [[ "$FORCE_MODE" != "true" ]]; then
        systemctl start nginx 2>/dev/null || true
        echo -e "${GREEN}✅ Nginx restarted${NC}"
    fi
elif [[ -w "/var/log/nginx/access.log" ]]; then
    clear_log_file "/var/log/nginx/access.log" "Nginx access log"
    clear_log_file "/var/log/nginx/error.log" "Nginx error log"
else
    echo -e "${YELLOW}⚠️  Nginx logs require root access${NC}"
fi

# PostgreSQL logs (if accessible)
if [[ $EUID -eq 0 ]]; then
    # Find PostgreSQL log files including rotated ones
    PG_LOG_DIR="/var/log/postgresql"
    if [[ -d "$PG_LOG_DIR" ]]; then
        if [[ "$FORCE_MODE" == "true" ]]; then
            echo -e "${CYAN}PostgreSQL temporarily stopped for log clearing${NC}"
            echo -e "${CYAN}Found PostgreSQL logs:${NC}"
            ls -lh /var/log/postgresql/ | head -10
        else
            echo -e "${CYAN}Restarting PostgreSQL to clear logs...${NC}"
            systemctl restart postgresql 2>/dev/null || true
            sleep 2
        fi
        
        clear_log_directory "$PG_LOG_DIR" "PostgreSQL logs" "postgresql-*.log*" "true"
        
        if [[ "$FORCE_MODE" == "true" ]]; then
            echo -e "${CYAN}Clearing rotated PostgreSQL logs...${NC}"
            clear_log_directory "$PG_LOG_DIR" "PostgreSQL rotated logs" "*.log.*"
            
            # Remove compressed PostgreSQL logs if any
            local pg_compressed_count=$(find /var/log/postgresql -name "*.gz" -type f 2>/dev/null | wc -l)
            if [[ $pg_compressed_count -gt 0 ]]; then
                local pg_compressed_size=$(find /var/log/postgresql -name "*.gz" -type f -exec du -ch {} + 2>/dev/null | tail -1 | cut -f1 || echo "0")
                find /var/log/postgresql -name "*.gz" -type f -delete 2>/dev/null || true
                echo -e "${GREEN}✅ Removed $pg_compressed_count compressed PostgreSQL logs ($pg_compressed_size)${NC}"
            fi
        fi
    fi
else
    echo -e "${YELLOW}⚠️  PostgreSQL logs require root access${NC}"
fi

echo ""
echo -e "${YELLOW}📋 Step: Node.js and NPM Logs${NC}"

# NPM logs
clear_log_directory "$HOME/.npm/_logs" "NPM logs" "*.log"
clear_log_file "$HOME/.npm/_logs/debug.log" "NPM debug log"

# Ubuntu user NPM logs
if [[ -d "/home/<USER>/.npm/_logs" ]]; then
    clear_log_directory "/home/<USER>/.npm/_logs" "Ubuntu NPM logs" "*.log"
fi

# Root NPM logs
if [[ $EUID -eq 0 && -d "/root/.npm/_logs" ]]; then
    clear_log_directory "/root/.npm/_logs" "Root NPM logs" "*.log"
fi

# Node.js logs in application directory
clear_log_directory "$APP_DIR" "Node.js logs in app directory" "npm-debug.log*"
clear_log_directory "$APP_DIR/client" "Client npm logs" "npm-debug.log*"
clear_log_directory "$APP_DIR/server" "Server npm logs" "npm-debug.log*"

echo ""
echo -e "${YELLOW}📋 Step: System Journal Logs (if root)${NC}"

if [[ $EUID -eq 0 ]]; then
    # Clear systemd journal logs for our services
    echo -e "${CYAN}Clearing systemd journal logs for hauling services...${NC}"
    journalctl --vacuum-time=1s --quiet 2>/dev/null || true
    echo -e "${GREEN}✅ Systemd journal logs cleared${NC}"
else
    echo -e "${YELLOW}⚠️  Systemd journal clearing requires root access${NC}"
fi

echo ""
echo -e "${YELLOW}📋 Step: Additional Log Locations${NC}"

# Check for logs in common locations
ADDITIONAL_LOCATIONS=(
    "/tmp/hauling-*.log"
    "/tmp/npm-*.log"
    "/var/tmp/hauling-*.log"
    "$APP_DIR/*.log"
    "$APP_DIR/client/*.log"
    "$APP_DIR/server/*.log"
)

for location in "${ADDITIONAL_LOCATIONS[@]}"; do
    if ls $location >/dev/null 2>&1; then
        for file in $location; do
            if [[ -f "$file" ]]; then
                clear_log_file "$file" "Additional log: $(basename $file)"
            fi
        done
    fi
done

# Restart services if in force mode
start_services

# Verification step
echo -e "${YELLOW}📋 Step: Verification & Summary${NC}"

# Check services if in force mode
if [[ "$FORCE_MODE" == "true" && $EUID -eq 0 ]]; then
    echo -e "${CYAN}Checking service status...${NC}"
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}✅ Nginx: Running${NC}"
    else
        echo -e "${RED}❌ Nginx: Not running${NC}"
    fi
    
    if systemctl is-active --quiet postgresql; then
        echo -e "${GREEN}✅ PostgreSQL: Running${NC}"
    else
        echo -e "${RED}❌ PostgreSQL: Not running${NC}"
    fi
    
    if pm2 list | grep -q "hauling-qr-server.*online"; then
        echo -e "${GREEN}✅ PM2 Application: Running${NC}"
    else
        echo -e "${RED}❌ PM2 Application: Not running${NC}"
    fi
    echo ""
fi

# Show remaining log sizes
echo -e "${CYAN}Checking remaining log file sizes...${NC}"

LOG_DIRS=(
    "$PM2_HOME/logs"
    "$APP_DIR/server/logs"
    "$DEPLOYMENT_LOG_DIR"
    "/var/log/nginx"
    "/var/log/postgresql"
)

TOTAL_SIZE=0
for dir in "${LOG_DIRS[@]}"; do
    if [[ -d "$dir" ]]; then
        SIZE=$(du -sh "$dir" 2>/dev/null | cut -f1 || echo "0")
        if [[ "$SIZE" != "0" ]]; then
            echo -e "${CYAN}   $dir: $SIZE${NC}"
        fi
    fi
done

# Show specific log sizes if in force mode
if [[ "$FORCE_MODE" == "true" ]]; then
    echo ""
    echo -e "${CYAN}Current log sizes after clearing:${NC}"
    du -sh /root/.pm2/logs 2>/dev/null && echo -n " (root PM2)" || echo "Root PM2 logs: Not accessible"
    du -sh /home/<USER>/.pm2/logs 2>/dev/null && echo -n " (ubuntu PM2)" || echo "Ubuntu PM2 logs: Not accessible"
    du -sh /var/www/hauling-qr-system/server/logs 2>/dev/null && echo -n " (app logs)" || echo "App logs: Not accessible"  
    du -sh /var/log/nginx 2>/dev/null && echo -n " (nginx)" || echo "Nginx logs: Not accessible"
    du -sh /var/log/postgresql 2>/dev/null && echo -n " (postgresql)" || echo "PostgreSQL logs: Not accessible"
    du -sh /home/<USER>/.npm/_logs 2>/dev/null && echo -n " (ubuntu npm)" || echo "Ubuntu NPM logs: Not accessible"
fi

echo ""
if [[ "$FORCE_MODE" == "true" ]]; then
    echo -e "${GREEN}🎉 Force log clearing completed!${NC}"
else
    echo -e "${GREEN}🎉 Log Clearing Complete!${NC}"
fi

echo ""
echo -e "${BLUE}📋 What was cleared:${NC}"
echo -e "   • PM2 process logs (hauling-qr-server)"
echo -e "   • Application logs (server/logs/)"
echo -e "   • Deployment logs (/var/log/hauling-deployment/)"
echo -e "   • System service logs (Nginx, PostgreSQL)"
echo -e "   • NPM and Node.js debug logs"
echo -e "   • Temporary log files"

if [[ $EUID -eq 0 ]]; then
    echo -e "   • System journal logs (systemd)"
fi

if [[ "$FORCE_MODE" == "true" ]]; then
    echo ""
    echo -e "${BLUE}🔧 What was done:${NC}"
    echo -e "   • ${GREEN}Cleared content${NC}: Active logs (preserves rotation & permissions)"
    echo -e "   • ${YELLOW}Deleted files${NC}: Compressed logs (.gz files - safe to remove)"
    echo -e "   • ${CYAN}Flushed${NC}: PM2 logs via command"
    echo -e "   • ${CYAN}Truncated${NC}: System service logs"
    echo -e "   • ${BLUE}Restarted${NC}: Services (PostgreSQL, Nginx, PM2)"
fi

echo ""
echo -e "${BLUE}📝 Useful commands after clearing:${NC}"
echo -e "   • Monitor new logs: ${CYAN}pm2 logs hauling-qr-server -f${NC}"
echo -e "   • Check PM2 status: ${CYAN}pm2 status${NC}"
echo -e "   • Check log sizes: ${CYAN}du -sh ~/.pm2/logs/ /var/log/hauling-deployment/${NC}"
echo -e "   • View Nginx logs: ${CYAN}tail -f /var/log/nginx/access.log${NC}"

if [[ "$FORCE_MODE" == "true" ]]; then
    echo -e "   • Check sizes: ${CYAN}./check-log-sizes.sh${NC}"
    echo -e "   • Test application: ${CYAN}curl http://localhost:5000/health${NC}"
fi

echo ""
echo -e "${YELLOW}💡 Note: Log files were cleared but not deleted to preserve file permissions${NC}"
echo -e "${YELLOW}   New logs will start accumulating immediately as the system runs${NC}"

if [[ "$FORCE_MODE" != "true" ]]; then
    echo ""
    echo -e "${CYAN}💡 For more aggressive clearing (stops services, removes compressed logs):${NC}"
    echo -e "   Run: ${CYAN}sudo $0 --force${NC}"
fi