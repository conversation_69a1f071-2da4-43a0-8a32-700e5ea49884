// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState } from "react";
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import PWAInstallButtons from '../../components/pwa/PWAInstallButtons';

const LoginPage = () => {  const [username, setUsername] = useState("admin");
  const [password, setPassword] = useState("admin123");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [rateLimited, setRateLimited] = useState(false);
  const [retryAfter, setRetryAfter] = useState(null);
  const { login } = useAuth();
  const navigate = useNavigate();

  // Detect mobile device
  const isMobileDevice = () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setRateLimited(false);
    setRetryAfter(null);

    if (!username || !password) {
      setError("Please enter both username and password");
      setIsLoading(false);
      return;
    }

    // Show mobile-specific loading message
    if (isMobileDevice()) {
      console.log('📱 Mobile login attempt starting...');
    }

    try {
      const result = await login({ username, password });

      if (result.success) {
        navigate('/dashboard');
      } else {
        if (result.error.includes('Rate limit exceeded')) {
          setRateLimited(true);
          setRetryAfter('15 minutes');
        }

        // Mobile-specific error messages
        let errorMessage = result.error || 'Login failed';
        if (isMobileDevice()) {
          if (errorMessage.includes('Network')) {
            errorMessage = 'Network error on mobile. Please check your connection and try again.';
          } else if (errorMessage.includes('timeout')) {
            errorMessage = 'Request timed out. Mobile connections can be slower - please try again.';
          } else if (errorMessage === 'Login failed') {
            errorMessage = 'Login failed. If using mobile, ensure you accepted the security certificate.';
          }
        }

        setError(errorMessage);
      }
    } catch (error) {
      console.error('Login error:', error);

      let errorMessage = 'An unexpected error occurred';

      if (error.response?.status === 429) {
        setRateLimited(true);
        setRetryAfter(error.response.headers['retry-after'] || '15 minutes');
        errorMessage = 'Too many login attempts. Please try again later.';
      } else if (isMobileDevice()) {
        // Mobile-specific error handling
        if (error.message.includes('Network')) {
          errorMessage = 'Mobile network error. Please check your connection and ensure you accepted the security certificate.';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Mobile request timed out. Please try again with a stable connection.';
        } else {
          errorMessage = 'Mobile login error. Please ensure you accepted the security certificate and try again.';
        }
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Add styles to head
  React.useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
    @keyframes slow-pan {
      0% { transform: scale(1.1) translate(0, 0); }
      50% { transform: scale(1.1) translate(-1%, -1%); }
      100% { transform: scale(1.1) translate(0, 0); }
    }
    @keyframes gradient {
      0% { opacity: 0.7; }
      50% { opacity: 0.8; }
      100% { opacity: 0.7; }
    }
    @keyframes card-appear {
      from { transform: translateY(20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    @keyframes input-appear {
      from { transform: translateX(-20px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    .animate-slow-pan {
      animation: slow-pan 20s ease-in-out infinite;
    }
    .animate-gradient {
      animation: gradient 8s ease-in-out infinite;
    }
    .animate-card-appear {
      animation: card-appear 0.8s ease-out forwards;
    }
    .animate-input-appear {
      animation: input-appear 0.6s ease-out forwards;
    }
  `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 relative overflow-hidden">      <div className="absolute inset-0">
        <img
          src="/images/hauling_truck_bg.jpg"
          className="w-full h-full object-cover animate-slow-pan"
          alt="Background"
        />
      </div>
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/70 via-blue-600/60 to-blue-400/50 backdrop-blur-[8px] animate-gradient"></div>
      <div className="w-full max-w-md relative z-10">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-3 tracking-tight">
            🚛 Hauling QR System
          </h1>
          <p className="text-blue-100 text-xl">Trip Management System</p>
        </div>
        <div className="backdrop-blur-2xl bg-white/15 rounded-2xl shadow-2xl p-10 border border-white/30 animate-card-appear hover:shadow-blue-500/20 hover:border-white/40 transition-all duration-500">
          <div className="text-center">
            <h2 className="text-3xl font-semibold text-white mb-8">
              Admin Login
            </h2>            {error && (
              <div className={`border rounded-xl p-6 mb-8 ${
                rateLimited 
                  ? 'bg-yellow-900/30 border-yellow-300/20' 
                  : 'bg-red-900/30 border-red-300/20'
              }`}>
                <p className={`text-base ${
                  rateLimited ? 'text-yellow-100' : 'text-red-100'
                }`}>
                  <strong>{rateLimited ? '⏰ Rate Limited:' : '⚠️ Error:'}</strong><br />
                  {error}
                  {rateLimited && retryAfter && (
                    <>
                      <br />
                      <span className="text-sm">Please wait {retryAfter} before trying again.</span>
                    </>
                  )}
                  {isMobileDevice() && error.includes('certificate') && (
                    <>
                      <br /><br />
                      <strong className="text-blue-200">📱 Mobile Setup Required:</strong>
                      <ol className="list-decimal list-inside mt-2 text-sm text-blue-100">
                        <li>Go to: https://99.99.15.168:5444/health</li>
                        <li>Tap "Advanced" → "Proceed to site"</li>
                        <li>Return here and try login again</li>
                      </ol>
                    </>
                  )}
                </p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label
                  htmlFor="username"
                  className="block text-lg font-medium text-white mb-3 text-left"
                >
                  Username
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="fas fa-user text-gray-400"></i>
                  </div>                  <input
                    id="username"
                    name="username"
                    type="text"
                    autoComplete="username"
                    placeholder="Enter your username"
                    className="w-full pl-10 pr-3 py-3 bg-white/10 border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 text-white text-lg backdrop-blur-md placeholder-blue-200 transition-all duration-300 hover:bg-white/15 animate-input-appear"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    aria-describedby="username-error"
                  />
                </div>
              </div>
              <div>
                <label
                  htmlFor="password"
                  className="block text-lg font-medium text-white mb-3 text-left"
                >
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="fas fa-lock text-gray-400"></i>
                  </div>                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    placeholder="••••••••"
                    className="w-full pl-10 pr-3 py-3 bg-white/10 border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 text-white text-lg backdrop-blur-md placeholder-blue-200 transition-all duration-300 hover:bg-white/15 animate-input-appear delay-100"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    aria-describedby="password-error"
                  />
                </div>
              </div>              <button
                type="submit"
                disabled={isLoading || rateLimited || !username.trim() || !password.trim()}
                className={`w-full font-medium py-3 px-6 rounded-xl transition duration-300 ease-in-out mt-8 whitespace-nowrap !rounded-button text-lg backdrop-blur-sm border border-white/10 ${
                  isLoading || rateLimited || !username.trim() || !password.trim()
                    ? 'bg-gray-500/20 text-gray-300 cursor-not-allowed'
                    : 'bg-blue-500/30 hover:bg-blue-500/40 text-white'
                }`}
                aria-describedby={error ? "login-error" : undefined}
              >
                {isLoading ? (
                  <>
                    <div className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Signing In...
                  </>
                ) : rateLimited ? (
                  <>
                    <i className="fas fa-clock mr-2"></i>
                    Rate Limited - Try Again Later
                  </>
                ) : (
                  <>
                    <i className="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                  </>
                )}
              </button>
              <div className="mt-6 text-center">
                <div className="text-lg text-blue-200 text-center">
                  <p><strong>Default Credentials:</strong></p>
                  <p className="text-sm">Username: admin | Password: admin12345</p>
                </div>
              </div>
            </form>
          </div>
        </div>

        {/* PWA Installation Section */}
        <div className="mt-8">
          <PWAInstallButtons />
        </div>

        <div className="text-center text-blue-100 text-base mt-8">
          <p>© 2025 Hauling QR Trip Management System. All rights reserved.</p>
          <p className="mt-2 text-blue-200">Today: June 18, 2025</p>
        </div>
      </div>
    </div>
    
  );
};
export default LoginPage;
