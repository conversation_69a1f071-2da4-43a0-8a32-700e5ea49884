import React from 'react';
import { usePermissions } from '../../hooks/usePermissions';

const ProtectedRoute = ({ children, requiredPermission, fallback = null }) => {
  const { hasPermission, loading } = usePermissions();

  // Show loading state while permissions are being fetched
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-secondary-600">Loading...</span>
      </div>
    );
  }

  // Check if user has required permission
  if (!hasPermission(requiredPermission)) {
    // Show custom fallback or default access denied message
    if (fallback) {
      return fallback;
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-secondary-900 mb-2">Access Denied</h1>
          <p className="text-secondary-600 mb-4">
            You don't have permission to access this page.
          </p>
          <p className="text-sm text-secondary-500 mb-6">
            Required permission: <code className="bg-secondary-100 px-2 py-1 rounded">{requiredPermission}</code>
          </p>
          <div className="space-x-4">
            <button
              onClick={() => window.history.back()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Go Back
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="px-4 py-2 bg-secondary-600 text-white rounded-md hover:bg-secondary-700 transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User has permission, render the protected content
  return children;
};

export default ProtectedRoute;