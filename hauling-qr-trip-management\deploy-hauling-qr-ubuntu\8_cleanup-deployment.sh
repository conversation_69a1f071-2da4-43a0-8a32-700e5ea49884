#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - MODULAR CLEANUP DEPLOYMENT SCRIPT
# =============================================================================
# Version: 1.1.0 - Enhanced with tmp directory cleanup
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# Description: Self-contained cleanup operations for production deployment optimization
#              Including cleanup of temporary deployment directories
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================
readonly SCRIPT_NAME="cleanup-deployment.sh"
readonly LOG_DIR="/var/log/hauling-qr-deployment"
readonly LOG_FILE="${LOG_DIR}/cleanup-$(date +%Y%m%d-%H%M%S).log"

# Application Configuration
readonly APP_DIR="/var/www/hauling-qr-system"
readonly TMP_APP_DIR="/tmp/hauling-qr-trip-management"

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Create log directory with proper permissions
sudo mkdir -p "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"
sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true

ts() { date '+%Y-%m-%d %H:%M:%S'; }
log() { echo "[$(ts)] $*" | tee -a "$LOG_FILE"; }

log_info() {
  echo -e "${BLUE}[$(ts)] INFO  | $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
  echo -e "${GREEN}[$(ts)] OK    | $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
  echo -e "${YELLOW}[$(ts)] WARN  | $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
  echo -e "${RED}[$(ts)] ERROR | $1${NC}" | tee -a "$LOG_FILE"
}

log_debug() {
  echo -e "${CYAN}[$(ts)] DEBUG | $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# ERROR HANDLING
# =============================================================================
cleanup_on_error() {
  local exit_code=$?
  if [[ $exit_code -ne 0 ]]; then
    log_error "Cleanup deployment failed with exit code $exit_code"
    log_error "Check log file: $LOG_FILE"
  fi
  exit $exit_code
}

trap cleanup_on_error ERR

# =============================================================================
# CLEANUP FUNCTIONS
# =============================================================================
remove_development_folders() {
  log_info "🗑️ Removing development and deployment folders..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory for cleanup"
    return 1
  }

  # List of development and deployment directories/files to remove
  local cleanup_targets=(
    ".augment"
    ".kiro"
    ".vscode"
    "docs"
    "server/docs"
    "deploy-hauling-qr-ubuntu"
    "pull_requests"
    ".git"
    ".github"
    "README.md"
    "CHANGELOG.md"
    "CONTRIBUTING.md"
    "LICENSE"
    "*.md"
    ".gitignore"
    ".gitattributes"
    "package-lock.json"
    "yarn.lock"
    "pnpm-lock.yaml"
    ".env.example"
  )

  local cleanup_count=0
  local total_size_before=$(du -sh . 2>/dev/null | cut -f1 || echo "unknown")

  log_info "📊 Application directory size before cleanup: $total_size_before"

  for target in "${cleanup_targets[@]}"; do
    if [[ -e "$target" ]]; then
      local target_size=$(du -sh "$target" 2>/dev/null | cut -f1 || echo "unknown")
      log_info "🗑️  Removing: $target ($target_size)"

      if rm -rf "$target" 2>/dev/null; then
        cleanup_count=$((cleanup_count + 1))
        log_success "✅ Removed: $target"
      else
        log_warning "⚠️ Failed to remove: $target"
      fi
    fi
  done

  log_success "✅ Development folders cleanup completed ($cleanup_count items removed)"
  return 0
}

remove_markdown_files() {
  log_info "📝 Removing remaining markdown files..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory"
    return 1
  }

  # Remove any remaining .md files in subdirectories
  local md_count=$(find . -name "*.md" -type f | wc -l)
  if [[ $md_count -gt 0 ]]; then
    log_info "Found $md_count markdown files to remove"
    find . -name "*.md" -type f -delete 2>/dev/null || true
    log_success "✅ Removed $md_count markdown files"
  else
    log_info "No additional markdown files found"
  fi

  return 0
}

remove_test_directories() {
  log_info "🧪 Removing test directories and files..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory"
    return 1
  }

  # Remove test directories
  local test_dirs=("__tests__" "test" "tests")
  local test_count=0

  for test_dir in "${test_dirs[@]}"; do
    local found_dirs=$(find . -name "$test_dir" -type d | wc -l)
    if [[ $found_dirs -gt 0 ]]; then
      log_info "Removing $found_dirs '$test_dir' directories"
      find . -name "$test_dir" -type d -exec rm -rf {} + 2>/dev/null || true
      test_count=$((test_count + found_dirs))
    fi
  done

  if [[ $test_count -gt 0 ]]; then
    log_success "✅ Removed $test_count test directories"
  else
    log_info "No test directories found"
  fi

  return 0
}

remove_development_config_files() {
  log_info "⚙️ Removing development configuration files..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory"
    return 1
  }

  # Development configuration file patterns
  local config_patterns=(
    ".eslintrc*"
    ".prettierrc*"
    "jest.config.*"
    "*.test.js"
    "*.spec.js"
  )

  local config_count=0

  for pattern in "${config_patterns[@]}"; do
    local found_files=$(find . -name "$pattern" -type f | wc -l)
    if [[ $found_files -gt 0 ]]; then
      log_info "Removing $found_files files matching '$pattern'"
      find . -name "$pattern" -type f -delete 2>/dev/null || true
      config_count=$((config_count + found_files))
    fi
  done

  if [[ $config_count -gt 0 ]]; then
    log_success "✅ Removed $config_count development configuration files"
  else
    log_info "No development configuration files found"
  fi

  return 0
}

cleanup_node_modules() {
  log_info "📦 Cleaning up unnecessary node_modules directories..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory"
    return 1
  }

  # Clean up node_modules in subdirectories (keep main ones)
  local node_modules_count=$(find . -path "./node_modules" -prune -o -path "./server/node_modules" -prune -o -path "./client/node_modules" -prune -o -name "node_modules" -type d -print | wc -l)
  
  if [[ $node_modules_count -gt 0 ]]; then
    log_info "Removing $node_modules_count unnecessary node_modules directories"
    find . -path "./node_modules" -prune -o -path "./server/node_modules" -prune -o -path "./client/node_modules" -prune -o -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
    log_success "✅ Removed $node_modules_count unnecessary node_modules directories"
  else
    log_info "No unnecessary node_modules directories found"
  fi

  return 0
  }

remove_cloned_repository() {
  log_info "🗑️ Checking if cloned repository can be safely removed..."

  local cloned_repo_path="/home/<USER>/hauling-qr-trip-management"

  # CRITICAL: Check if we're running from the cloned repository
  local current_script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

  if [[ "$current_script_dir" == *"$cloned_repo_path"* ]]; then
    log_warning "⚠️ PRESERVING cloned repository - deployment is running from this location"
    log_info "Current script directory: $current_script_dir"
    log_info "Repository path: $cloned_repo_path"
    log_info "Reason: Cannot remove repository while deployment scripts are running from it"
    log_info "The repository will be cleaned up after deployment completion"

    # Create a marker file to indicate cleanup was deferred
    echo "$(date): Cleanup deferred - deployment running from repository" > "/tmp/hauling-qr-cleanup-deferred.marker"

    return 0
  fi

  # If we're not running from the cloned repository, it's safe to remove
  if [[ -d "$cloned_repo_path" ]]; then
    local repo_size=$(du -sh "$cloned_repo_path" 2>/dev/null | cut -f1 || echo "unknown")
    log_info "Found cloned repository: $cloned_repo_path ($repo_size)"
    log_info "Safe to remove - deployment is not running from this location"

    if rm -rf "$cloned_repo_path" 2>/dev/null; then
      log_success "✅ Successfully removed cloned repository: $cloned_repo_path"
    else
      log_warning "⚠️ Failed to remove cloned repository: $cloned_repo_path"
      # Try with sudo if regular removal fails
      if sudo rm -rf "$cloned_repo_path" 2>/dev/null; then
        log_success "✅ Successfully removed cloned repository with sudo: $cloned_repo_path"
      else
        log_error "❌ Failed to remove cloned repository even with sudo: $cloned_repo_path"
        return 1
      fi
    fi
  else
    log_info "No cloned repository found at: $cloned_repo_path"
  fi

  return 0


}

remove_build_artifacts() {
  log_info "🏗️ Removing build artifacts and temporary files..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory"
    return 1
  }

  # Build artifacts to remove
  local build_artifacts=(
    "*.log"
    "*.tmp"
    ".DS_Store"
    "Thumbs.db"
    "*.swp"
    "*.swo"
    "*~"
    ".cache"
    ".temp"
    ".tmp"
    "coverage"
    ".nyc_output"
  
  )

  local artifact_count=0

  for artifact in "${build_artifacts[@]}"; do
    local found_files=$(find . -name "$artifact" 2>/dev/null | wc -l)
    if [[ $found_files -gt 0 ]]; then
      log_info "Removing $found_files items matching '$artifact'"
      find . -name "$artifact" -exec rm -rf {} + 2>/dev/null || true
      artifact_count=$((artifact_count + found_files))
    fi
  done

  if [[ $artifact_count -gt 0 ]]; then
    log_success "✅ Removed $artifact_count build artifacts"
  else
    log_info "No build artifacts found"
  fi

  return 0
}

remove_additional_deployment_directories() {
  log_info "🗂️ Removing additional deployment directories..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory"
    return 1
  }

  # Additional directories to remove from production deployment
  local additional_dirs=(
    "testsprite_tests"
  )

  local cleanup_count=0
  local total_size_removed=0

  for dir in "${additional_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
      local dir_size=$(du -sh "$dir" 2>/dev/null | cut -f1 || echo "unknown")
      local dir_size_kb=$(du -sk "$dir" 2>/dev/null | cut -f1 || echo "0")

      log_info "🗑️  Removing: $dir ($dir_size)"

      if rm -rf "$dir" 2>/dev/null; then
        cleanup_count=$((cleanup_count + 1))
        total_size_removed=$((total_size_removed + dir_size_kb))
        log_success "✅ Removed: $dir"
      else
        log_warning "⚠️ Failed to remove: $dir"
      fi
    else
      log_info "Directory not found: $dir (skipping)"
    fi
  done

  if [[ $cleanup_count -gt 0 ]]; then
    local total_size_mb=$((total_size_removed / 1024))
    log_success "✅ Additional deployment directories cleanup completed ($cleanup_count directories removed, ~${total_size_mb}MB freed)"
  else
    log_info "No additional deployment directories found to remove"
  fi

  return 0
}

cleanup_temporary_directory() {
  log_info "🧹 Cleaning up temporary deployment directory..."

  # Clean up the main temporary deployment directory
  local tmp_deployment_dir="/tmp/hauling-qr-deployment"

  if [[ -d "$tmp_deployment_dir" ]]; then
    local tmp_size=$(du -sh "$tmp_deployment_dir" 2>/dev/null | cut -f1 || echo "unknown")
    log_info "Found temporary deployment directory: $tmp_deployment_dir ($tmp_size)"

    if rm -rf "$tmp_deployment_dir" 2>/dev/null; then
      log_success "✅ Successfully removed temporary deployment directory: $tmp_deployment_dir"
    else
      log_warning "⚠️ Failed to remove temporary deployment directory: $tmp_deployment_dir"
      # Try with sudo if regular removal fails
      if sudo rm -rf "$tmp_deployment_dir" 2>/dev/null; then
        log_success "✅ Successfully removed temporary deployment directory with sudo: $tmp_deployment_dir"
      else
        log_error "❌ Failed to remove temporary deployment directory even with sudo: $tmp_deployment_dir"
        return 1
      fi
    fi
  else
    log_info "No temporary deployment directory found at: $tmp_deployment_dir"
  fi

  # Also clean up the legacy temporary directory if it exists
  if [[ -d "$TMP_APP_DIR" ]]; then
    local tmp_size=$(du -sh "$TMP_APP_DIR" 2>/dev/null | cut -f1 || echo "unknown")
    log_info "Found legacy temporary directory: $TMP_APP_DIR ($tmp_size)"

    if rm -rf "$TMP_APP_DIR" 2>/dev/null; then
      log_success "✅ Successfully removed legacy temporary directory: $TMP_APP_DIR"
    else
      log_warning "⚠️ Failed to remove legacy temporary directory: $TMP_APP_DIR"
      # Try with sudo if regular removal fails
      if sudo rm -rf "$TMP_APP_DIR" 2>/dev/null; then
        log_success "✅ Successfully removed legacy temporary directory with sudo: $TMP_APP_DIR"
      else
        log_error "❌ Failed to remove legacy temporary directory even with sudo: $TMP_APP_DIR"
        return 1
      fi
    fi
  else
    log_info "No legacy temporary directory found at: $TMP_APP_DIR"
  fi

  return 0
}

calculate_cleanup_savings() {
  log_info "📊 Calculating cleanup savings..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory"
    return 1
  }

  local total_size_after=$(du -sh . 2>/dev/null | cut -f1 || echo "unknown")
  
  log_success "✅ Production cleanup completed successfully"
  log_info "📊 Application directory size after cleanup: $total_size_after"
  log_info "🎯 Production deployment is now optimized and clean"

  return 0
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
  log_info "🚀 Starting deployment cleanup for Hauling QR Trip System..."
  log_info "Script: $SCRIPT_NAME"
  log_info "Log file: $LOG_FILE"

  # Verify application directory exists
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    exit 1
  fi
  
  # Step 9: Remove cloned repository
  if ! remove_cloned_repository; then
    log_error "❌ Cloned repository removal failed"
    exit 1
  fi

  # Step 1: Remove development folders
  if ! remove_development_folders; then
    log_error "❌ Development folders cleanup failed"
    exit 1
  fi

  # Step 2: Remove markdown files
  if ! remove_markdown_files; then
    log_error "❌ Markdown files cleanup failed"
    exit 1
  fi

  # Step 3: Remove test directories
  if ! remove_test_directories; then
    log_error "❌ Test directories cleanup failed"
    exit 1
  fi

  # Step 4: Remove development configuration files
  if ! remove_development_config_files; then
    log_error "❌ Development configuration cleanup failed"
    exit 1
  fi

  # Step 5: Cleanup node_modules
  if ! cleanup_node_modules; then
    log_error "❌ Node modules cleanup failed"
    exit 1
  fi

  # Step 6: Remove build artifacts
  if ! remove_build_artifacts; then
    log_error "❌ Build artifacts cleanup failed"
    exit 1
  fi

  # Step 7: Remove additional deployment directories
  if ! remove_additional_deployment_directories; then
    log_error "❌ Additional deployment directories cleanup failed"
    exit 1
  fi

  # Step 8: Clean up temporary directory
  if ! cleanup_temporary_directory; then
    log_error "❌ Temporary directory cleanup failed"
    exit 1
  fi

  # Step 9: Calculate cleanup savings
  if ! calculate_cleanup_savings; then
    log_error "❌ Cleanup savings calculation failed"
    exit 1
  fi

  log_success "✅ Deployment cleanup completed successfully"
  log_info "📋 Cleanup Summary:"
  log_info "   - Development folders removed (.augment, .kiro, .vscode, docs, etc.)"
  log_info "   - Markdown files cleaned up"
  log_info "   - Test directories removed"
  log_info "   - Development configuration files removed"
  log_info "   - Unnecessary node_modules cleaned up"
  log_info "   - Build artifacts and temporary files removed"
  log_info "   - Temporary deployment directory cleaned (/tmp/hauling-qr-deployment)"
  log_info "   - Cloned repository preserved for Phase 8.5 (/home/<USER>/hauling-qr-trip-management)"
  log_info "   - Production deployment optimized"
  
  return 0
}

# Execute main function
main "$@"
