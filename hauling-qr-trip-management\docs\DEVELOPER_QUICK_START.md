# Developer Quick Start Guide

## 🚀 Get Running in 5 Minutes

### Prerequisites Check
```bash
# Check Node.js version (need v16+, v20 LTS recommended)
node --version

# Check PostgreSQL (need v12+, v17.4 recommended)
psql --version

# Check npm
npm --version
```

### 1. Clone & Setup
```bash
# Clone the repository
git clone <repository-url>
cd hauling-qr-trip-system

# The .env file is already configured with sensible defaults
# Just verify your database credentials if needed
cat .env | grep DB_
```

### 2. Database Setup
```bash
# Create database (adjust for your PostgreSQL setup)
sudo -u postgres createdb hauling_qr_system

# Run migrations
npm run db:migrate

# Or use the consolidated schema (faster for new setups)
sudo -u postgres psql hauling_qr_system < database/init.sql
```

### 3. Start the System
```bash
# 🎯 RECOMMENDED: Unified startup with auto-configuration
node start-system.js

# Alternative: Traditional development mode
npm run dev
```

### 4. Access the Application
The unified startup will display your access URLs:
```
📱 Frontend: http://[YOUR-IP]:3000
🖥️  Backend: http://[YOUR-IP]:5000
🔌 WebSocket: ws://[YOUR-IP]:5000
```

## 🔧 Development Workflow

### Daily Development
```bash
# Start everything with one command
node start-system.js

# The system will:
# ✅ Detect your network IP automatically
# ✅ Install missing dependencies
# ✅ Start backend server (port 5000)
# ✅ Start frontend dev server (port 3000)
# ✅ Configure CORS for network access
# ✅ Handle graceful shutdown with Ctrl+C
```

### Testing
```bash
# Run all tests
npm test

# Mobile-specific tests
npm run test:mobile

# PWA offline functionality
npm run test:pwa-all

# Browser compatibility
npm run test:browser-compatibility

# Server tests with coverage
cd server && npm run test:coverage
```

### Individual Component Development
```bash
# Backend only (port 5000)
cd server && npm run dev

# Frontend only (port 3000)
cd client && npm start

# Database migrations
npm run db:migrate
```

## 📱 Mobile Testing

### Network Access (Automatic)
The system automatically configures network access:
- **HOST=0.0.0.0**: Frontend accessible from any device on your network
- **AUTO_DETECT_IP=true**: Automatic IP detection and CORS configuration
- **Mobile-friendly URLs**: Access from phones/tablets using your computer's IP

### Manual Network Testing
```bash
# Find your IP address
# Windows
ipconfig | findstr IPv4

# Linux/Mac
ip addr show | grep inet

# Access from mobile device
http://[YOUR-IP]:3000
```

### VS Code Dev Tunnels
```bash
# 1. Start the system
node start-system.js

# 2. In VS Code, forward port 3000
# 3. Set port visibility to "Public"
# 4. Access the tunnel URL from anywhere
```

## 🗄️ Database Quick Reference

### Connection Details
```bash
# Default development settings
Host: localhost
Port: 5432
Database: hauling_qr_system
User: postgres
Password: PostgreSQLPassword
```

### Common Database Tasks
```bash
# Connect to database
psql -h localhost -U postgres -d hauling_qr_system

# Check migration status
npm run db:migrate

# Reset database (careful!)
sudo -u postgres dropdb hauling_qr_system
sudo -u postgres createdb hauling_qr_system
sudo -u postgres psql hauling_qr_system < database/init.sql
```

### Current Schema Highlights
- **29 active migrations** (017-029 are recent)
- **Role-based access control** (migration 017)
- **Driver QR system** (migration 018)
- **Security audit logging** (migration 019)
- **Shift management fixes** (migrations 020-029)

## 🔐 Authentication & Testing

### Default Admin User
```bash
# Default credentials (change in production)
Username: admin
Password: admin123

# API testing with curl
TOKEN=$(curl -s -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  | jq -r '.data.token')

curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/trips
```

### JWT Token Testing
```javascript
// Frontend: Get token from localStorage
const token = localStorage.getItem('token');

// API calls with axios
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
```

## 🎯 Key Features to Test

### QR Code System
1. **Trip QR Codes**: Generate and scan truck/location QR codes
2. **Driver QR System**: Check-in/check-out with driver ID cards
3. **Mobile Scanning**: Test QR scanning on mobile devices

### Real-time Features
1. **WebSocket Updates**: Live dashboard updates
2. **Trip Monitoring**: Real-time trip status changes
3. **Driver Status**: Live driver check-in/check-out updates

### Core Workflows
1. **Trip Creation**: Create → Assign → Start → Complete → Verify
2. **Driver Shifts**: Check-in → Work → Check-out
3. **Multi-location**: A→B→C extensions and C→B→C cycles

## 🚨 Common Issues & Solutions

### Port Already in Use
```bash
# Kill processes on ports 3000/5000
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:3000 | xargs kill -9
lsof -ti:5000 | xargs kill -9
```

### Database Connection Issues
```bash
# Check PostgreSQL status
# Windows
sc query postgresql-x64-14

# Linux
sudo systemctl status postgresql

# Start PostgreSQL if needed
# Windows
net start postgresql-x64-14

# Linux
sudo systemctl start postgresql
```

### Node Modules Issues
```bash
# Clean install
rm -rf node_modules package-lock.json
rm -rf client/node_modules client/package-lock.json
rm -rf server/node_modules server/package-lock.json

npm install
cd client && npm install
cd ../server && npm install
```

### Environment Configuration Issues
```bash
# Reset to defaults
git checkout .env

# Or manually verify key settings
grep -E "(NODE_ENV|AUTO_DETECT_IP|DB_)" .env
```

## 📊 Performance Tips

### Development Performance
- **Use unified startup**: `node start-system.js` is optimized
- **Enable source maps**: Set `GENERATE_SOURCEMAP=true` for debugging
- **Disable unnecessary logs**: Check console output controls in .env

### Database Performance
- **Connection pooling**: Already configured (max 25, min 5)
- **Indexes**: Performance indexes are in place
- **Query optimization**: Use EXPLAIN ANALYZE for slow queries

### Frontend Performance
- **Dev tunnels**: Optimized configuration for remote access
- **Hot reload**: Fast refresh is configured
- **Bundle analysis**: Use `npm run build` to check bundle size

## 🔗 Useful Links

### Development URLs
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **WebSocket**: ws://localhost:5000
- **Health Check**: http://localhost:5000/api/health

### Documentation
- **API Docs**: [docs/API_ENDPOINTS_CURRENT.md](API_ENDPOINTS_CURRENT.md)
- **System Status**: [docs/CURRENT_SYSTEM_STATUS.md](CURRENT_SYSTEM_STATUS.md)
- **Main README**: [../README.md](../README.md)

### Tools & Testing
- **Database Admin**: Use pgAdmin, DBeaver, or psql command line
- **API Testing**: Postman, Insomnia, or curl
- **WebSocket Testing**: Browser dev tools or wscat
- **Mobile Testing**: Chrome DevTools device emulation or real devices

## 🎉 You're Ready!

With this setup, you should have:
- ✅ Full development environment running
- ✅ Database with sample data
- ✅ Network access for mobile testing
- ✅ Real-time WebSocket updates
- ✅ QR code scanning capabilities
- ✅ Comprehensive testing suite

**Next Steps:**
1. Explore the dashboard at http://localhost:3000
2. Test QR code scanning with your mobile device
3. Check out the API endpoints in [API_ENDPOINTS_CURRENT.md](API_ENDPOINTS_CURRENT.md)
4. Review the system status in [CURRENT_SYSTEM_STATUS.md](CURRENT_SYSTEM_STATUS.md)

Happy coding! 🚀