# Design Document

## Overview

This design document outlines the approach for modifying the existing auto-deployment script for the Hauling QR Trip Management System to work specifically with the domain `truckhaul.top` and Cloudflare SSL/TLS integration. The deployment script will automate the installation and configuration of all necessary components on Ubuntu 24.04 VPS servers.

## Architecture

The deployment architecture follows a standard web application stack with the following components:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cloudflare    │────│   Nginx Proxy    │────│   Node.js App   │
│   (SSL/CDN)     │    │   (Port 80/443)  │    │   (Port 5000)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                       ┌──────────────────┐
                       │   PostgreSQL     │
                       │   (Port 5432)    │
                       └──────────────────┘
```

### Key Components:

1. **Frontend**: React.js application served by Nginx
2. **Backend**: Node.js/Express API running on port 5000
3. **Database**: PostgreSQL for data storage
4. **Web Server**: Nginx as reverse proxy and static file server
5. **Process Manager**: PM2 for Node.js application management
6. **SSL/TLS**: Cloudflare for SSL termination and CDN services
7. **Security**: UFW firewall and Fail2Ban for intrusion prevention

## Components and Interfaces

### 1. Deployment Script Modifications

The existing `deploy-hauling-qr-ubuntu.sh` script will be modified to:

- Pre-configure the domain name `truckhaul.top`
- Set up Cloudflare SSL/TLS integration with the appropriate mode
- Streamline the deployment process for this specific configuration
- Improve error handling and recovery mechanisms
- Add detailed logging for troubleshooting

### 2. Cloudflare SSL/TLS Integration

We will implement Cloudflare SSL/TLS using the "Full" mode, which provides:

- Encrypted connections between clients and Cloudflare
- Encrypted connections between Cloudflare and the origin server
- Self-signed certificates on the origin server (no need for public CA certificates)

This approach provides a good balance of security and ease of setup, as it doesn't require managing Let's Encrypt certificates but still ensures encrypted connections throughout the entire path.

### 3. Nginx Configuration

The Nginx configuration will be updated to:

- Serve the React frontend from the build directory
- Proxy API requests to the Node.js backend
- Handle WebSocket connections for real-time features
- Implement security headers and best practices
- Configure for Cloudflare integration (real IP detection, etc.)

### 4. Database Setup

The PostgreSQL database setup will:

- Create the required database and user
- Initialize the schema with the application tables
- Run any necessary migrations
- Set up automated backups

### 5. Application Deployment

The application deployment process will:

- Clone the repository or use uploaded files
- Install dependencies for both frontend and backend
- Build the React frontend
- Configure environment variables
- Set up PM2 for process management

### 6. Security Configuration

Security measures will include:

- Firewall configuration with UFW
- Fail2Ban for brute force protection
- Secure file permissions
- Non-root application user
- Security headers in Nginx
- Rate limiting for sensitive endpoints

## Data Models

### Configuration Data Model

The deployment script will use the following configuration parameters:

```
DOMAIN_NAME="truckhaul.top"
CLOUDFLARE_SSL_MODE="full"  # Options: flexible, full, full_strict
DB_PASSWORD="[generated or provided]"
JWT_SECRET="[generated or provided]"
ADMIN_EMAIL="[provided by user]"
ADMIN_PASSWORD="[provided by user]"
GIT_REPO_URL="[repository URL]"
ENABLE_MONITORING="true"
ENABLE_BACKUPS="true"
ENVIRONMENT="production"
```

### Environment Configuration Model

The application's environment configuration will be generated as:

```
# Environment
NODE_ENV=production

# Server Configuration
PORT=5000
HOST=0.0.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_user
DB_PASSWORD=[secure password]

# Security Configuration
JWT_SECRET=[secure token]
JWT_EXPIRY=24h

# CORS Configuration
CORS_ORIGIN=https://truckhaul.top
ALLOWED_ORIGINS=https://truckhaul.top,https://www.truckhaul.top

# Client Configuration
REACT_APP_API_URL=https://truckhaul.top/api
REACT_APP_WS_URL=wss://truckhaul.top
REACT_APP_USE_HTTPS=true
```

## Error Handling

The deployment script will implement robust error handling:

1. **Context-Aware Error Messages**: Provide specific error messages based on the current deployment step
2. **Graceful Failure**: Ensure the system doesn't remain in an inconsistent state if an error occurs
3. **Error Logs**: Generate detailed error logs for troubleshooting
4. **Recovery Suggestions**: Provide actionable recovery steps for common errors
5. **Validation**: Pre-validate configuration before making changes

## Testing Strategy

The deployment script will be tested using the following approach:

1. **Dry Run Mode**: Test configuration validation without making changes
2. **Fresh Installation**: Test on a clean Ubuntu 24.04 VPS
3. **Idempotency Testing**: Run the script multiple times to ensure it handles existing installations
4. **Component Testing**: Verify each component (Nginx, PostgreSQL, Node.js) works correctly
5. **Integration Testing**: Verify all components work together
6. **Cloudflare Integration**: Test the SSL/TLS configuration with Cloudflare

## Cloudflare SSL/TLS Configuration Details

For the domain `truckhaul.top`, we will implement Cloudflare SSL/TLS using the "Full" mode:

1. **Server Configuration**:
   - Generate self-signed SSL certificate on the server
   - Configure Nginx to use this certificate
   - Enable HTTPS on port 443

2. **Cloudflare Configuration**:
   - Set SSL/TLS encryption mode to "Full"
   - Enable "Always Use HTTPS" option
   - Configure appropriate page rules for caching

3. **DNS Configuration**:
   - Set up A records for `truckhaul.top` and `www.truckhaul.top`
   - Enable Cloudflare proxy (orange cloud) for these records

This approach provides end-to-end encryption while simplifying certificate management, as Cloudflare doesn't validate the self-signed certificate on the origin server but still maintains encrypted connections.