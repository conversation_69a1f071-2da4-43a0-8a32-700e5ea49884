/**
 * Frontend Network Utilities for Dynamic Backend Detection
 * Automatically detects backend server URLs for API and WebSocket connections
 * Uses environment variables from root .env file for configuration
 */

/**
 * Get the current page's hostname
 */
function getCurrentHostname() {
  return window.location.hostname;
}

/**
 * Get the current page's protocol
 */
function getCurrentProtocol() {
  return window.location.protocol;
}

/**
 * Check if the current page is served over HTTPS
 */
function isHTTPS() {
  return getCurrentProtocol() === 'https:';
}

/**
 * Detect if we're running on localhost
 */
function isLocalhost() {
  const hostname = getCurrentHostname();
  return hostname === 'localhost' || hostname === '127.0.0.1';
}

/**
 * Detect if we're running on a local network IP
 */
function isLocalNetwork() {
  const hostname = getCurrentHostname();
  
  // Check for private IP ranges
  if (hostname.startsWith('192.168.') || 
      hostname.startsWith('10.') || 
      (hostname.startsWith('172.') && 
       parseInt(hostname.split('.')[1]) >= 16 && 
       parseInt(hostname.split('.')[1]) <= 31)) {
    return true;
  }
  
  return false;
}

/**
 * Get the appropriate backend port based on protocol and environment
 * ENHANCED: Support for dev tunnels and port forwarding scenarios
 * Uses centralized port configuration from environment variables
 */
function getBackendPort() {
  const hostname = getCurrentHostname();
  const isHttps = isHTTPS();

  // For dev tunnels, check if we're on a tunneled domain
  if (hostname.includes('devtunnels.ms') ||
      hostname.includes('ngrok.io') ||
      hostname.includes('localtunnel.me') ||
      hostname.includes('github.dev') ||
      hostname.includes('gitpod.io')) {
    // Dev tunnels typically forward the same port as the frontend
    // So if frontend is on 3000, backend should also be accessible on 3000
    return window.location.port || '3000';
  }

  // Use environment-specific ports (set by config-loader.js)
  // Default to new Cloudflare-compatible ports if env vars not available
  if (isHttps) {
    return '8443'; // HTTPS port (Cloudflare compatible)
  } else {
    return '8080'; // HTTP port (Cloudflare compatible)
  }
}

/**
 * Get the appropriate WebSocket protocol
 */
function getWebSocketProtocol() {
  return isHTTPS() ? 'wss:' : 'ws:';
}

/**
 * Generate backend API URL with automatic detection and dev tunnel support
 */
function getApiBaseUrl() {
  // Check for explicit environment variable first
  if (process.env.REACT_APP_API_URL) {
    console.log('🔧 Using explicit API URL:', process.env.REACT_APP_API_URL);
    return process.env.REACT_APP_API_URL;
  }

  // Auto-detect based on current page
  const protocol = getCurrentProtocol();
  const hostname = getCurrentHostname();
  const port = getBackendPort();

  // For dev tunnels, use proxy approach
  if (hostname.includes('devtunnels.ms') ||
      hostname.includes('ngrok.io') ||
      hostname.includes('localtunnel.me') ||
      hostname.includes('github.dev') ||
      hostname.includes('gitpod.io') ||
      hostname.includes('35cp9j1x-3000.asse.devtunnels.ms')) {

    // For dev tunnels, use the proxy configured in setupProxy.js
    // This routes API calls through the frontend server to the local backend
    // Use the same domain and port as the current page
    const apiUrl = `${protocol}//${hostname}${window.location.port ? ':' + window.location.port : ''}/api`;
    console.log('🔧 Dev tunnel detected!');
    console.log('🔧 Current URL:', window.location.href);
    console.log('🔧 Dev tunnel API URL (via proxy):', apiUrl);
    return apiUrl;
  }

  // For local development
  const apiUrl = `${protocol}//${hostname}:${port}/api`;
  console.log('🔧 Auto-detected API URL:', apiUrl);
  return apiUrl;
}

/**
 * Get API URL with fallback support for production VPS IP access
 */
function getApiUrlWithFallback() {
  // Primary URL (domain or auto-detected)
  const primaryUrl = process.env.REACT_APP_API_URL || getApiBaseUrl();
  
  // Fallback URL (VPS IP)
  const fallbackUrl = process.env.REACT_APP_API_URL_FALLBACK;
  
  console.log('🔧 Primary API URL:', primaryUrl);
  if (fallbackUrl) {
    console.log('🔧 Fallback API URL:', fallbackUrl);
  }
  
  return {
    primary: primaryUrl,
    fallback: fallbackUrl,
    vpsIP: process.env.REACT_APP_VPS_IP
  };
}

/**
 * Generate WebSocket URL with automatic detection and dev tunnel support
 */
function getWebSocketUrl() {
  // Check for explicit environment variable first
  if (process.env.REACT_APP_WS_URL) {
    console.log('🔧 Using explicit WebSocket URL:', process.env.REACT_APP_WS_URL);
    return process.env.REACT_APP_WS_URL;
  }

  // Auto-detect based on current page
  const protocol = getWebSocketProtocol();
  const hostname = getCurrentHostname();
  const port = getBackendPort();

  // For dev tunnels, use proxy approach for WebSocket
  if (hostname.includes('devtunnels.ms') ||
      hostname.includes('ngrok.io') ||
      hostname.includes('localtunnel.me') ||
      hostname.includes('github.dev') ||
      hostname.includes('gitpod.io')) {

    // For dev tunnels, DON'T include port - the tunnel handles routing
    const wsUrl = `${protocol}//${hostname}/ws`;
    console.log('🔧 Dev tunnel WebSocket URL (via proxy):', wsUrl);
    return wsUrl;
  }

  // For local development
  const wsUrl = `${protocol}//${hostname}:${port}`;
  console.log('🔧 Auto-detected WebSocket URL:', wsUrl);
  return wsUrl;
}

/**
 * Get WebSocket URL with fallback support for production VPS IP access
 */
function getWebSocketUrlWithFallback() {
  // Primary URL (domain or auto-detected)
  const primaryUrl = process.env.REACT_APP_WS_URL || getWebSocketUrl();
  
  // Fallback URL (VPS IP)
  const fallbackUrl = process.env.REACT_APP_WS_URL_FALLBACK;
  
  console.log('🔧 Primary WebSocket URL:', primaryUrl);
  if (fallbackUrl) {
    console.log('🔧 Fallback WebSocket URL:', fallbackUrl);
  }
  
  return {
    primary: primaryUrl,
    fallback: fallbackUrl,
    vpsIP: process.env.REACT_APP_VPS_IP
  };
}

/**
 * Generate all possible backend URLs for testing
 * Uses centralized port configuration
 */
function getAllPossibleBackendUrls() {
  const hostname = getCurrentHostname();
  const protocols = ['http:', 'https:'];
  // Use new Cloudflare-compatible ports + legacy ports for backward compatibility
  const ports = ['8080', '8443', '5000', '5444'];
  const urls = [];
  
  // Current hostname with different protocols/ports
  for (const protocol of protocols) {
    for (const port of ports) {
      urls.push(`${protocol}//${hostname}:${port}/api`);
    }
  }
  
  // Localhost variants
  if (!isLocalhost()) {
    for (const protocol of protocols) {
      for (const port of ports) {
        urls.push(`${protocol}//localhost:${port}/api`);
      }
    }
  }
  
  return urls;
}

/**
 * Test backend connectivity with fallback support
 */
async function testBackendConnectivity(url) {
  try {
    const response = await fetch(`${url.replace('/api', '')}/health`, {
      method: 'GET',
      mode: 'cors',
      credentials: 'include'
    });
    
    return {
      url,
      status: response.status,
      ok: response.ok,
      available: response.ok
    };
  } catch (error) {
    return {
      url,
      status: 0,
      ok: false,
      available: false,
      error: error.message
    };
  }
}

/**
 * Smart API call with automatic fallback to VPS IP
 */
async function makeApiCallWithFallback(endpoint, options = {}) {
  const urls = getApiUrlWithFallback();
  
  // Try primary URL first
  try {
    const primaryUrl = `${urls.primary}${endpoint}`;
    console.log('🔧 Trying primary API URL:', primaryUrl);
    
    const response = await fetch(primaryUrl, {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    if (response.ok) {
      console.log('✅ Primary API URL successful');
      return response;
    }
    
    throw new Error(`Primary URL failed with status: ${response.status}`);
  } catch (primaryError) {
    console.warn('⚠️ Primary API URL failed:', primaryError.message);
    
    // Try fallback URL if available
    if (urls.fallback) {
      try {
        const fallbackUrl = `${urls.fallback}${endpoint}`;
        console.log('🔧 Trying fallback API URL:', fallbackUrl);
        
        const response = await fetch(fallbackUrl, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            ...options.headers
          },
          ...options
        });
        
        if (response.ok) {
          console.log('✅ Fallback API URL successful');
          return response;
        }
        
        throw new Error(`Fallback URL failed with status: ${response.status}`);
      } catch (fallbackError) {
        console.error('❌ Both primary and fallback URLs failed');
        throw new Error(`Both API URLs failed. Primary: ${primaryError.message}, Fallback: ${fallbackError.message}`);
      }
    } else {
      console.error('❌ No fallback URL configured');
      throw primaryError;
    }
  }
}

/**
 * Find the best available backend URL
 */
async function findBestBackendUrl() {
  const primaryUrl = getApiBaseUrl();
  
  // Test primary URL first
  const primaryTest = await testBackendConnectivity(primaryUrl);
  if (primaryTest.available) {
    return {
      url: primaryUrl,
      test: primaryTest,
      method: 'primary'
    };
  }
  
  // Test all possible URLs
  const allUrls = getAllPossibleBackendUrls();
  const tests = await Promise.all(
    allUrls.map(url => testBackendConnectivity(url))
  );
  
  // Find first available URL
  const availableTest = tests.find(test => test.available);
  if (availableTest) {
    return {
      url: availableTest.url,
      test: availableTest,
      method: 'discovery'
    };
  }
  
  // No backend found, return primary URL anyway
  return {
    url: primaryUrl,
    test: primaryTest,
    method: 'fallback'
  };
}

/**
 * Display network configuration information
 */
function displayNetworkConfig() {
  const config = {
    hostname: getCurrentHostname(),
    protocol: getCurrentProtocol(),
    isHTTPS: isHTTPS(),
    isLocalhost: isLocalhost(),
    isLocalNetwork: isLocalNetwork(),
    apiUrl: getApiBaseUrl(),
    wsUrl: getWebSocketUrl(),
    backendPort: getBackendPort()
  };
  
  console.log('🌐 Frontend Network Configuration:', config);
  return config;
}

/**
 * Get mobile-friendly URLs for QR codes or sharing
 */
function getMobileUrls() {
  const hostname = getCurrentHostname();
  const httpsPort = '3000';
  
  return {
    current: window.location.href,
    https: `https://${hostname}:${httpsPort}`,
    http: `http://${hostname}:${httpsPort}`,
    qrCode: `https://${hostname}:${httpsPort}/scanner`
  };
}

/**
 * Check if the current environment supports camera access
 */
function supportsCameraAccess() {
  const isSecureContext = window.isSecureContext;
  const hasMediaDevices = navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
  
  return {
    isSecureContext,
    hasMediaDevices,
    supported: isSecureContext && hasMediaDevices,
    reason: !isSecureContext ? 'Not a secure context (HTTPS required)' :
            !hasMediaDevices ? 'MediaDevices API not available' :
            'Supported'
  };
}

/**
 * Get environment-specific configuration
 */
function getEnvironmentConfig() {
  return {
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
    debugMode: process.env.REACT_APP_DEBUG_MODE === 'true',
    useHttps: process.env.REACT_APP_USE_HTTPS === 'true',
    localNetworkIp: process.env.REACT_APP_LOCAL_NETWORK_IP,
    apiUrl: process.env.REACT_APP_API_URL,
    wsUrl: process.env.REACT_APP_WS_URL
  };
}

export {
  getCurrentHostname,
  getCurrentProtocol,
  isHTTPS,
  isLocalhost,
  isLocalNetwork,
  getBackendPort,
  getWebSocketProtocol,
  getApiBaseUrl,
  getApiUrlWithFallback,
  getWebSocketUrl,
  getWebSocketUrlWithFallback,
  getAllPossibleBackendUrls,
  testBackendConnectivity,
  makeApiCallWithFallback,
  findBestBackendUrl,
  displayNetworkConfig,
  getMobileUrls,
  supportsCameraAccess,
  getEnvironmentConfig
};
