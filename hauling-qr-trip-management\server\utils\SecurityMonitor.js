const { logError, logInfo } = require('./logger');
const { query } = require('../config/database');

/**
 * Security Monitor Utility
 * Tracks and analyzes security patterns and potential abuse
 */
class SecurityMonitor {
  
  /**
   * Track suspicious activity patterns
   * @param {string} activityType - Type of activity
   * @param {Object} context - Activity context
   */
  static async trackSuspiciousActivity(activityType, context) {
    try {
      const { ip_address, user_agent, endpoint, details } = context;
      
      // Log the suspicious activity
      logError('SUSPICIOUS_ACTIVITY_DETECTED', `${activityType} detected`, {
        activity_type: activityType,
        ip_address,
        user_agent,
        endpoint,
        timestamp: new Date().toISOString(),
        details
      });

      // Store in database for pattern analysis (if security_logs table exists)
      try {
        await query(
          `INSERT INTO security_logs (activity_type, ip_address, user_agent, endpoint, details, created_at)
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [activityType, ip_address, user_agent, endpoint, JSON.stringify(details), new Date()]
        );
      } catch (dbError) {
        // Don't fail if security_logs table doesn't exist
        logError('SECURITY_LOG_DB_ERROR', dbError, { activity_type: activityType });
      }

    } catch (error) {
      logError('SECURITY_MONITOR_ERROR', error, { activity_type: activityType });
    }
  }



  /**
   * Monitor failed authentication attempts
   * @param {string} ip_address - Client IP address
   * @param {string} employee_id - Employee ID attempted
   * @param {string} failure_reason - Reason for failure
   */
  static async monitorFailedAuth(ip_address, employee_id, failure_reason) {
    try {
      // Track failed attempts (in production, use Redis for rate limiting)
      const context = {
        ip_address,
        employee_id,
        failure_reason,
        endpoint: '/api/driver/connect',
        details: {
          failure_type: 'AUTHENTICATION_FAILED',
          attempted_employee_id: employee_id,
          failure_reason
        }
      };

      await this.trackSuspiciousActivity('FAILED_AUTHENTICATION', context);

      // Check if this IP has too many failures (simplified check)
      // In production, implement proper rate limiting with Redis
      logInfo('AUTH_FAILURE_MONITORED', `Failed auth attempt for ${employee_id}`, {
        ip_address,
        employee_id,
        failure_reason,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logError('FAILED_AUTH_MONITOR_ERROR', error, { ip_address, employee_id });
    }
  }

  /**
   * Validate request origin and headers
   * @param {Object} req - Express request object
   * @returns {Object} Validation result
   */
  static validateRequestOrigin(req) {
    const suspiciousHeaders = [];
    
    // Check for missing or suspicious User-Agent
    const userAgent = req.get('User-Agent');
    if (!userAgent || userAgent.length < 10) {
      suspiciousHeaders.push('MISSING_OR_SHORT_USER_AGENT');
    }
    
    // Check for suspicious User-Agent patterns
    if (userAgent && (userAgent.includes('bot') || userAgent.includes('crawler') || userAgent.includes('spider'))) {
      suspiciousHeaders.push('BOT_USER_AGENT');
    }
    
    // Check for missing Accept header
    if (!req.get('Accept')) {
      suspiciousHeaders.push('MISSING_ACCEPT_HEADER');
    }
    
    // Check for suspicious X-Forwarded-For chains
    const forwardedFor = req.get('X-Forwarded-For');
    if (forwardedFor && forwardedFor.split(',').length > 5) {
      suspiciousHeaders.push('EXCESSIVE_PROXY_CHAIN');
    }

    return {
      suspicious: suspiciousHeaders.length > 0,
      indicators: suspiciousHeaders,
      risk_level: SecurityMonitor._calculateRiskLevel(suspiciousHeaders)
    };
  }

  /**
   * Calculate risk level based on suspicious indicators
   * @param {Array} suspiciousHeaders - Array of suspicious header indicators
   * @returns {string} Risk level: LOW, MEDIUM, HIGH, CRITICAL
   */
  static _calculateRiskLevel(suspiciousHeaders) {
    if (!suspiciousHeaders || suspiciousHeaders.length === 0) {
      return 'LOW';
    }

    const criticalIndicators = ['BOT_USER_AGENT', 'EXCESSIVE_PROXY_CHAIN'];
    const highRiskIndicators = ['MISSING_OR_SHORT_USER_AGENT'];
    
    // Check for critical indicators
    if (suspiciousHeaders.some(indicator => criticalIndicators.includes(indicator))) {
      return 'CRITICAL';
    }
    
    // Check for high risk indicators
    if (suspiciousHeaders.some(indicator => highRiskIndicators.includes(indicator))) {
      return 'HIGH';
    }
    
    // Multiple medium risk indicators
    if (suspiciousHeaders.length >= 2) {
      return 'HIGH';
    }
    
    // Single medium risk indicator
    return 'MEDIUM';
  }




}

module.exports = SecurityMonitor;