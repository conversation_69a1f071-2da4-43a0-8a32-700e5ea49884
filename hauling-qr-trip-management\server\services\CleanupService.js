const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const glob = require('glob');

class CleanupService {
    constructor() {
        this.rootDir = path.join(__dirname, '../..');
        this.serverDir = path.join(this.rootDir, 'server');
        this.scriptsDir = path.join(this.rootDir, 'scripts');
        this.backupDir = path.join(this.serverDir, 'backups/cleanup');

        this.criticalPatterns = [
            // Route handlers
            /\.get\s*\(/,
            /\.post\s*\(/,
            /\.put\s*\(/,
            /\.delete\s*\(/,
            /\.patch\s*\(/,
            /router\./,
            /app\./,

            // Middleware
            /middleware/i,
            /authenticate/i,
            /authorize/i,
            /validate/i,

            // Database operations
            /pool\.query/,
            /db\./,
            /connection\./,
            /transaction/i,

            // Express setup
            /express\(/,
            /listen\(/,
            /server\./,

            // Module exports
            /module\.exports/,
            /exports\./,

            // Main entry points
            /server\.js/,
            /app\.js/,
            /index\.js/
        ];
    }

    async analyzeUnusedFunctions() {
        try {
            const serverFiles = await this.getJavaScriptFiles('server');
            const scriptFiles = await this.getJavaScriptFiles('scripts');
            const allFiles = [...serverFiles, ...scriptFiles];

            const analysis = {
                totalFiles: allFiles.length,
                analyzedFunctions: [],
                unusedFunctions: [],
                criticalFunctions: [],
                safeToRemove: [],
                warnings: []
            };

            for (const filePath of allFiles) {
                const fileAnalysis = await this.analyzeFile(filePath);
                analysis.analyzedFunctions.push(...fileAnalysis.functions);
                analysis.criticalFunctions.push(...fileAnalysis.critical);
                analysis.unusedFunctions.push(...fileAnalysis.unused);
            }

            // Cross-reference usage across files
            const usageMap = await this.buildUsageMap(allFiles);
            analysis.safeToRemove = this.identifySafeToRemove(analysis.unusedFunctions, usageMap);

            return analysis;
        } catch (error) {
            throw new Error(`Cleanup analysis failed: ${error.message}`);
        }
    }

    async getJavaScriptFiles(directory) {
        try {
            // Use glob to find all JavaScript files
            const basePath = directory === 'server' ? this.serverDir : this.scriptsDir;
            
            // Check if directory exists first
            try {
                await fs.access(basePath);
            } catch (error) {
                console.warn(`Warning: Directory ${basePath} does not exist: ${error.message}`);
                return [];
            }
            
            // Use glob.sync to find all JavaScript files, excluding node_modules
            const pattern = path.join(basePath, '**', '*.js');
            const files = glob.sync(pattern, {
                ignore: [
                    path.join(basePath, 'node_modules', '**'),
                    path.join(basePath, '.git', '**')
                ],
                absolute: true
            });
            
            // Return relative paths from the root directory
            return files.map(file => path.relative(this.rootDir, file));
        } catch (error) {
            console.warn(`Warning: Could not scan directory ${directory}: ${error.message}`);
            return [];
        }
    }

    async analyzeFile(filePath) {
        try {
            // Get the full path to the file
            const fullPath = path.join(this.rootDir, filePath);
            
            // Check if file exists
            try {
                await fs.access(fullPath);
            } catch (error) {
                throw new Error(`File does not exist: ${fullPath}`);
            }
            
            const content = await fs.readFile(fullPath, 'utf8');
            const functions = this.extractFunctions(content);

            const analysis = {
                file: filePath,
                functions: [],
                critical: [],
                unused: []
            };

            for (const func of functions) {
                const functionInfo = {
                    name: func.name,
                    file: filePath,
                    line: func.line,
                    type: func.type,
                    isCritical: this.isCriticalFunction(content, func)
                };

                analysis.functions.push(functionInfo);

                if (functionInfo.isCritical) {
                    analysis.critical.push(functionInfo);
                } else {
                    const isUsed = await this.isFunctionUsed(func.name, filePath);
                    if (!isUsed) {
                        analysis.unused.push(functionInfo);
                    }
                }
            }

            return analysis;
        } catch (error) {
            throw new Error(`Failed to analyze file ${filePath}: ${error.message}`);
        }
    }

    extractFunctions(content) {
        const functions = [];
        const lines = content.split('\n');

        // Function declarations
        const functionRegex = /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g;
        // Arrow functions assigned to variables
        const arrowFunctionRegex = /(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*(?:\([^)]*\)|[a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>/g;

        let match;

        // Extract function declarations
        while ((match = functionRegex.exec(content)) !== null) {
            const lineNumber = content.substring(0, match.index).split('\n').length;
            functions.push({
                name: match[1],
                type: 'function',
                line: lineNumber
            });
        }

        // Extract arrow functions
        while ((match = arrowFunctionRegex.exec(content)) !== null) {
            const lineNumber = content.substring(0, match.index).split('\n').length;
            functions.push({
                name: match[1],
                type: 'arrow',
                line: lineNumber
            });
        }

        // Extract methods (be more careful to avoid false positives)
        const methodLines = lines.filter((line, index) => {
            const trimmed = line.trim();
            return trimmed.match(/^[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{/) &&
                !trimmed.startsWith('//') &&
                !trimmed.includes('if') &&
                !trimmed.includes('for') &&
                !trimmed.includes('while');
        });

        methodLines.forEach((line, index) => {
            const match = line.match(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/);
            if (match) {
                const lineNumber = lines.indexOf(line) + 1;
                functions.push({
                    name: match[1],
                    type: 'method',
                    line: lineNumber
                });
            }
        });

        return functions;
    }

    isCriticalFunction(content, func) {
        // Check if function matches critical patterns
        for (const pattern of this.criticalPatterns) {
            if (pattern.test(content)) {
                return true;
            }
        }

        // Check if function name suggests it's critical
        const criticalNames = [
            'main', 'init', 'setup', 'start', 'listen', 'connect',
            'authenticate', 'authorize', 'validate', 'middleware',
            'route', 'handler', 'controller', 'service'
        ];

        return criticalNames.some(name =>
            func.name.toLowerCase().includes(name.toLowerCase())
        );
    }

    async isFunctionUsed(functionName, filePath) {
        try {
            // Search for function usage across all JavaScript files
            const searchPattern = `\\b${functionName}\\b`;
            const searchCommand = process.platform === 'win32'
                ? `findstr /r /s "${searchPattern}" server\\*.js scripts\\*.js`
                : `grep -r "${searchPattern}" server/ scripts/ || true`;

            const result = execSync(searchCommand, {
                cwd: path.join(__dirname, '..'),
                encoding: 'utf8',
                stdio: 'pipe'
            });

            // If function is found in multiple files or used in same file, it's used
            const matches = result.split('\n').filter(line => line.trim());
            const usageFiles = new Set(matches.map(match => match.split(':')[0]));

            // Function is used if found in other files or called in same file
            return usageFiles.size > 1 ||
                matches.some(match => match.includes(`${functionName}(`));
        } catch (error) {
            // If search fails, assume function is used (safer approach)
            return true;
        }
    }

    async buildUsageMap(files) {
        const usageMap = new Map();

        for (const filePath of files) {
            try {
                // Get the full path to the file
                const fullPath = path.join(this.rootDir, filePath);
                
                // Check if file exists
                try {
                    await fs.access(fullPath);
                } catch (error) {
                    console.warn(`Warning: File does not exist: ${fullPath}`);
                    continue;
                }
                
                const content = await fs.readFile(fullPath, 'utf8');
                const imports = this.extractImports(content);
                const calls = this.extractFunctionCalls(content);

                usageMap.set(filePath, {
                    imports,
                    calls
                });
            } catch (error) {
                console.warn(`Warning: Could not build usage map for ${filePath}: ${error.message}`);
            }
        }

        return usageMap;
    }

    extractImports(content) {
        const imports = [];
        const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
        const importRegex = /import\s+.*\s+from\s+['"`]([^'"`]+)['"`]/g;

        let match;
        while ((match = requireRegex.exec(content)) !== null) {
            imports.push(match[1]);
        }

        while ((match = importRegex.exec(content)) !== null) {
            imports.push(match[1]);
        }

        return imports;
    }

    extractFunctionCalls(content) {
        const calls = [];
        const callRegex = /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g;

        let match;
        while ((match = callRegex.exec(content)) !== null) {
            calls.push(match[1]);
        }

        return calls;
    }

    identifySafeToRemove(unusedFunctions, usageMap) {
        return unusedFunctions.filter(func => {
            // Additional safety checks using usage map
            for (const [filePath, usage] of usageMap) {
                if (usage.calls.includes(func.name)) {
                    return false;
                }
            }
            return true;
        });
    }

    async createBackup() {
        try {
            // Create a shorter backup path to avoid ENAMETOOLONG errors
            await fs.mkdir(this.backupDir, { recursive: true });
            const timestamp = new Date().toISOString().slice(0, 16).replace(/[:.]/g, '-');
            const backupPath = path.join(this.backupDir, `backup-${timestamp}`);

            // Create backup directories
            await fs.mkdir(path.join(backupPath, 'server'), { recursive: true });
            await fs.mkdir(path.join(backupPath, 'scripts'), { recursive: true });

            // Copy only JavaScript files for backup (to avoid recursive path issues)
            const serverFiles = await this.getJavaScriptFiles('server');
            const scriptFiles = await this.getJavaScriptFiles('scripts');

            // Copy server files
            for (const file of serverFiles) {
                try {
                    const sourcePath = path.join(this.rootDir, file);
                    const targetPath = path.join(backupPath, file);
                    
                    // Ensure target directory exists
                    await fs.mkdir(path.dirname(targetPath), { recursive: true });
                    
                    // Copy the file
                    await fs.copyFile(sourcePath, targetPath);
                } catch (err) {
                    console.warn(`Warning: Could not backup file ${file}: ${err.message}`);
                }
            }

            // Copy script files
            for (const file of scriptFiles) {
                try {
                    const sourcePath = path.join(this.rootDir, file);
                    const targetPath = path.join(backupPath, file);
                    
                    // Ensure target directory exists
                    await fs.mkdir(path.dirname(targetPath), { recursive: true });
                    
                    // Copy the file
                    await fs.copyFile(sourcePath, targetPath);
                } catch (err) {
                    console.warn(`Warning: Could not backup file ${file}: ${err.message}`);
                }
            }

            return backupPath;
        } catch (error) {
            throw new Error(`Backup creation failed: ${error.message}`);
        }
    }

    async copyDirectory(src, dest) {
        await fs.mkdir(dest, { recursive: true });
        const entries = await fs.readdir(src, { withFileTypes: true });

        for (const entry of entries) {
            const srcPath = path.join(src, entry.name);
            const destPath = path.join(dest, entry.name);

            if (entry.isDirectory() && entry.name !== 'node_modules') {
                await this.copyDirectory(srcPath, destPath);
            } else if (entry.isFile()) {
                await fs.copyFile(srcPath, destPath);
            }
        }
    }

    async executeCleanup(safeToRemove) {
        try {
            const backupPath = await this.createBackup();
            const results = {
                backupPath,
                removedFunctions: [],
                modifiedFiles: [],
                errors: []
            };

            // Group functions by file for efficient processing
            const fileGroups = new Map();
            for (const func of safeToRemove) {
                if (!fileGroups.has(func.file)) {
                    fileGroups.set(func.file, []);
                }
                fileGroups.get(func.file).push(func);
            }

            // Process each file
            for (const [filePath, functions] of fileGroups) {
                try {
                    const modified = await this.removeFunctionsFromFile(filePath, functions);
                    if (modified) {
                        results.modifiedFiles.push(filePath);
                        results.removedFunctions.push(...functions);
                    }
                } catch (error) {
                    results.errors.push({
                        file: filePath,
                        error: error.message
                    });
                }
            }

            return results;
        } catch (error) {
            throw new Error(`Cleanup execution failed: ${error.message}`);
        }
    }

    async removeFunctionsFromFile(filePath, functions) {
        try {
            const fullPath = path.join(__dirname, '..', filePath);
            const content = await fs.readFile(fullPath, 'utf8');
            const lines = content.split('\n');

            // Sort functions by line number in descending order to avoid index issues
            const sortedFunctions = functions.sort((a, b) => b.line - a.line);

            let modified = false;
            for (const func of sortedFunctions) {
                const removed = this.removeFunctionFromLines(lines, func);
                if (removed) {
                    modified = true;
                }
            }

            if (modified) {
                await fs.writeFile(fullPath, lines.join('\n'));
            }

            return modified;
        } catch (error) {
            throw new Error(`Failed to remove functions from ${filePath}: ${error.message}`);
        }
    }

    removeFunctionFromLines(lines, func) {
        const startLine = func.line - 1; // Convert to 0-based index

        if (startLine >= lines.length) {
            return false;
        }

        // Find the end of the function by counting braces
        let braceCount = 0;
        let endLine = startLine;
        let foundOpenBrace = false;

        for (let i = startLine; i < lines.length; i++) {
            const line = lines[i];

            for (const char of line) {
                if (char === '{') {
                    braceCount++;
                    foundOpenBrace = true;
                } else if (char === '}') {
                    braceCount--;
                    if (foundOpenBrace && braceCount === 0) {
                        endLine = i;
                        break;
                    }
                }
            }

            if (foundOpenBrace && braceCount === 0) {
                break;
            }
        }

        // Remove the function lines
        if (endLine >= startLine) {
            lines.splice(startLine, endLine - startLine + 1);
            return true;
        }

        return false;
    }

    async rollbackCleanup(backupPath) {
        try {
            // Restore from backup
            await this.copyDirectory(path.join(backupPath, 'server'), path.join(__dirname, '../server'));
            await this.copyDirectory(path.join(backupPath, 'scripts'), path.join(__dirname, '../scripts'));

            return { success: true, message: 'Cleanup successfully rolled back' };
        } catch (error) {
            throw new Error(`Rollback failed: ${error.message}`);
        }
    }

    async analyzeCodebase(options = {}) {
        try {
            const timestamp = new Date().toISOString();

            // Set default options
            const config = {
                includeServer: options.includeServer !== false,
                includeScripts: options.includeScripts !== false,
                preserveCritical: options.preserveCritical !== false,
                maxFilesToAnalyze: options.maxFilesToAnalyze || 100,
                verbose: options.verbose || false
            };

            const analysis = await this.analyzeUnusedFunctions();

            return {
                success: true,
                timestamp,
                stats: {
                    totalFiles: analysis.totalFiles,
                    totalFunctions: analysis.analyzedFunctions.length,
                    criticalFunctions: analysis.criticalFunctions.length,
                    unusedFunctions: analysis.unusedFunctions.length,
                    safeToRemove: analysis.safeToRemove.length
                },
                unusedFunctions: analysis.safeToRemove.map(func => ({
                    name: func.name,
                    file: func.file,
                    type: func.type,
                    line: func.line,
                    exported: this.isFunctionExported(func)
                }))
            };
        } catch (error) {
            return {
                success: false,
                timestamp: new Date().toISOString(),
                error: error.message
            };
        }
    }

    async executeCleanupFromAnalysis(analysisResults, options = {}) {
        try {
            const timestamp = new Date().toISOString();

            // Set default options
            const config = {
                dryRun: options.dryRun !== false,
                createBackup: options.createBackup !== false,
                maxFilesToModify: options.maxFilesToModify || 50,
                verbose: options.verbose || false
            };

            let backupPath = null;
            if (config.createBackup && !config.dryRun) {
                backupPath = await this.createBackup();
            }

            const results = {
                modifiedFiles: [],
                removedFunctions: [],
                errors: []
            };

            if (!config.dryRun) {
                const cleanupResults = await this.executeCleanup(analysisResults.unusedFunctions || []);
                results.modifiedFiles = cleanupResults.modifiedFiles;
                results.removedFunctions = cleanupResults.removedFunctions;
                results.errors = cleanupResults.errors;
            } else {
                // Simulate cleanup for dry run
                results.modifiedFiles = [...new Set((analysisResults.unusedFunctions || []).map(f => f.file))];
                results.removedFunctions = analysisResults.unusedFunctions || [];
            }

            return {
                success: true,
                timestamp,
                dryRun: config.dryRun,
                backupPath,
                stats: {
                    filesModified: results.modifiedFiles.length,
                    functionsRemoved: results.removedFunctions.length,
                    errors: results.errors.length
                },
                results
            };
        } catch (error) {
            return {
                success: false,
                timestamp: new Date().toISOString(),
                error: error.message
            };
        }
    }

    async rollbackFromBackup(backupPath) {
        try {
            const timestamp = new Date().toISOString();

            // Verify backup exists
            const backupExists = await fs.access(backupPath).then(() => true).catch(() => false);
            if (!backupExists) {
                throw new Error(`Backup not found: ${backupPath}`);
            }

            // Restore from backup
            await this.copyDirectory(path.join(backupPath, 'server'), path.join(__dirname, '../server'));
            await this.copyDirectory(path.join(backupPath, 'scripts'), path.join(__dirname, '../scripts'));

            return {
                success: true,
                timestamp,
                stats: {
                    restoredDirectories: ['server', 'scripts']
                }
            };
        } catch (error) {
            return {
                success: false,
                timestamp: new Date().toISOString(),
                error: error.message
            };
        }
    }

    async verifySystemIntegrity() {
        try {
            const timestamp = new Date().toISOString();

            // Check critical files exist
            const criticalFiles = [
                'server/server.js',
                'server/routes/system-health-routes.js',
                'server/services/SystemMonitoringService.js',
                'server/config/database.js'
            ];

            const missingFiles = [];
            let serverRequireError = null;
            let dbConnectError = null;
            const errors = [];

            for (const file of criticalFiles) {
                try {
                    const fullPath = path.join(this.rootDir, file);
                    await fs.access(fullPath);
                } catch (error) {
                    missingFiles.push(file);
                    errors.push({
                        file: file,
                        error: `File not found: ${error.message}`
                    });
                }
            }

            // Test server require
            try {
                const serverPath = path.join(this.serverDir, 'server.js');
                require.resolve(serverPath);
            } catch (error) {
                serverRequireError = error.message;
                errors.push({
                    file: 'server/server.js',
                    error: `Cannot require server module: ${error.message}`
                });
            }

            // Test database connection - only if database.js exists
            try {
                const dbConfigPath = path.join(this.serverDir, 'config/database.js');
                if (await fs.access(dbConfigPath).then(() => true).catch(() => false)) {
                    try {
                        const dbConfig = require(dbConfigPath);
                        if (dbConfig && typeof dbConfig.getClient === 'function') {
                            const client = await dbConfig.getClient();
                            await client.query('SELECT 1');
                            client.release();
                        }
                    } catch (error) {
                        dbConnectError = error.message;
                        errors.push({
                            file: 'server/config/database.js',
                            error: `Database connection failed: ${error.message}`
                        });
                    }
                }
            } catch (error) {
                // Ignore database errors if config file doesn't exist
            }

            return {
                passed: missingFiles.length === 0 && !serverRequireError && !dbConnectError,
                timestamp,
                missingFiles,
                serverRequireError,
                dbConnectError,
                errors
            };
        } catch (error) {
            return {
                passed: false,
                timestamp: new Date().toISOString(),
                error: error.message,
                errors: [{
                    file: 'system',
                    error: error.message
                }]
            };
        }
    }

    isFunctionExported(func) {
        // Simple check for exported functions
        return func.name.includes('export') ||
            func.file.includes('module.exports') ||
            func.type === 'method';
    }
}

module.exports = CleanupService;