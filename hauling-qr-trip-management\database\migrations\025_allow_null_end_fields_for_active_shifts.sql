-- Migration: Allow NULL end_date and end_time for active shifts
-- This enables proper handling of active QR-created shifts where drivers haven't checked out yet

-- Drop the existing constraint that requires both start_date and end_date to be NOT NULL
ALTER TABLE driver_shifts DROP CONSTRAINT IF EXISTS valid_unified_date_range;

-- Allow end_time to be NULL for active shifts
ALTER TABLE driver_shifts ALTER COLUMN end_time DROP NOT NULL;

-- Add a new constraint that allows NULL end_date for active shifts
-- but still validates the date range when both dates are provided
ALTER TABLE driver_shifts ADD CONSTRAINT valid_unified_date_range_flexible 
CHECK (
  (start_date IS NULL) OR 
  (start_date IS NOT NULL AND (end_date IS NULL OR (end_date IS NOT NULL AND start_date <= end_date)))
);

-- Add a constraint to ensure data consistency for active vs completed shifts
ALTER TABLE driver_shifts ADD CONSTRAINT active_shift_null_end_fields
CHECK (
  (status = 'active' AND auto_created = true) OR 
  (status != 'active' OR auto_created = false OR (end_date IS NOT NULL AND end_time IS NOT NULL))
);

-- Update existing active QR-created shifts to have NULL end_date and end_time
UPDATE driver_shifts 
SET 
  end_date = NULL,
  end_time = NULL,
  updated_at = NOW()
WHERE 
  status = 'active' 
  AND auto_created = true 
  AND (end_time = '23:59:59' OR end_date = start_date);

-- Add comment explaining the new pattern
COMMENT ON COLUMN driver_shifts.end_date IS 'NULL for active QR-created shifts, populated when driver checks out';
COMMENT ON COLUMN driver_shifts.end_time IS 'NULL for active QR-created shifts, populated when driver checks out';