/**
 * Exception Request Parser
 * 
 * This module provides intelligent parsing of exception request descriptions
 * to determine the exact assignment creation requirements based on the
 * specific deviation type and location information contained in the request.
 * 
 * Key Features:
 * - Intelligent parsing of exception descriptions
 * - Location extraction from exception text
 * - Assignment creation strategy determination
 * - Support for complex deviation scenarios
 */

const { logExceptionFlow, logExceptionError } = require('./exception-flow-manager');

/**
 * Exception request patterns for different deviation types
 */
const EXCEPTION_PATTERNS = {
  LOADING_DEVIATION: {
    patterns: [
      /Loading at (.+?) instead of/i,
      /loading at (.+?) instead of/i,
      /Truck .+ loading at (.+?) instead/i,
      /loading at (.+)/i
    ],
    type: 'loading',
    strategy: 'preserve_unloading'
  },
  UNLOADING_DEVIATION: {
    patterns: [
      /Unloading at (.+?) instead of/i,
      /unloading at (.+?) instead of/i,
      /Truck .+ unloading at (.+?) instead/i,
      /unloading at (.+)/i
    ],
    type: 'unloading',
    strategy: 'preserve_loading'
  },
  ROUTE_CHANGE: {
    patterns: [
      /Route deviation: (.+?) → (.+)/i,
      /Changed route from (.+?) to (.+)/i,
      /New route: (.+?) → (.+)/i
    ],
    type: 'route',
    strategy: 'full_route_change'
  }
};

/**
 * Assignment creation strategies based on exception type
 */
const ASSIGNMENT_STRATEGIES = {
  PRESERVE_LOADING: 'preserve_loading',    // Keep original loading, change unloading
  PRESERVE_UNLOADING: 'preserve_unloading', // Keep original unloading, change loading
  FULL_ROUTE_CHANGE: 'full_route_change',   // Change both loading and unloading
  AUTO_DETERMINE: 'auto_determine'          // Intelligently determine based on context
};

class ExceptionRequestParser {
  constructor() {
    this.logger = {
      log: (message, data = {}) => logExceptionFlow('EXCEPTION_PARSER', message, data),
      error: (message, error, data = {}) => logExceptionError('EXCEPTION_PARSER', error, { message, ...data })
    };
  }

  /**
   * Parse exception request to determine assignment creation requirements
   * @param {Object} params - Parsing parameters
   * @param {string} params.exceptionDescription - Exception description text
   * @param {Object} params.tripDetails - Original trip details
   * @param {Object} params.originalAssignment - Original assignment information
   * @returns {Promise<Object>} Parsed assignment requirements
   */
  async parseExceptionRequest(params) {
    const { exceptionDescription, tripDetails, originalAssignment } = params;

    this.logger.log('Parsing exception request for assignment creation', {
      exception_description: exceptionDescription,
      trip_id: tripDetails?.id,
      original_assignment_id: originalAssignment?.id
    });

    try {
      // Step 1: Identify exception type and extract location information
      const deviationType = this._identifyDeviationType(exceptionDescription);
      
      if (!deviationType) {
        this.logger.log('No recognized deviation pattern found, using fallback analysis');
        return this._fallbackAnalysis(exceptionDescription, tripDetails, originalAssignment);
      }

      // Step 2: Extract location information from exception description
      const locationInfo = this._extractLocationInfo(exceptionDescription, deviationType);

      // Step 3: Determine assignment creation strategy
      const assignmentStrategy = this._determineAssignmentStrategy(deviationType, locationInfo, originalAssignment);

      // Step 4: Build assignment creation requirements
      const assignmentRequirements = await this._buildAssignmentRequirements(
        assignmentStrategy,
        locationInfo,
        tripDetails,
        originalAssignment
      );

      this.logger.log('Exception request parsed successfully', {
        deviation_type: deviationType.type,
        strategy: assignmentStrategy,
        assignment_requirements: assignmentRequirements
      });

      return {
        success: true,
        deviationType: deviationType.type,
        strategy: assignmentStrategy,
        locationInfo,
        assignmentRequirements,
        confidence: this._calculateConfidence(deviationType, locationInfo)
      };

    } catch (error) {
      this.logger.error('Failed to parse exception request', error, {
        exception_description: exceptionDescription
      });
      
      // Return fallback analysis on error
      return this._fallbackAnalysis(exceptionDescription, tripDetails, originalAssignment);
    }
  }

  /**
   * Identify the type of deviation from exception description
   * @param {string} description - Exception description
   * @returns {Object|null} Deviation type information
   */
  _identifyDeviationType(description) {
    // Check unloading patterns first to avoid conflicts with loading patterns
    const patternOrder = ['UNLOADING_DEVIATION', 'LOADING_DEVIATION', 'ROUTE_CHANGE'];

    for (const key of patternOrder) {
      const config = EXCEPTION_PATTERNS[key];
      if (!config) continue;

      for (const pattern of config.patterns) {
        if (pattern.test(description)) {
          return {
            key,
            type: config.type,
            strategy: config.strategy,
            pattern: pattern.source
          };
        }
      }
    }
    return null;
  }

  /**
   * Extract location information from exception description
   * @param {string} description - Exception description
   * @param {Object} deviationType - Identified deviation type
   * @returns {Object} Extracted location information
   */
  _extractLocationInfo(description, deviationType) {
    const locationInfo = {
      newLocation: null,
      originalLocation: null,
      locationType: deviationType.type
    };

    // Find the appropriate pattern and extract location
    for (const pattern of EXCEPTION_PATTERNS[deviationType.key].patterns) {
      const match = description.match(pattern);
      if (match) {
        if (deviationType.type === 'route' && match.length >= 3) {
          // Route change pattern: from → to
          locationInfo.originalLocation = match[1].trim();
          locationInfo.newLocation = match[2].trim();
        } else if (match.length >= 2) {
          // Single location pattern
          locationInfo.newLocation = match[1].trim();
        }
        break;
      }
    }

    return locationInfo;
  }

  /**
   * Determine the assignment creation strategy
   * @param {Object} deviationType - Deviation type information
   * @param {Object} locationInfo - Extracted location information
   * @param {Object} originalAssignment - Original assignment
   * @returns {string} Assignment strategy
   */
  _determineAssignmentStrategy(deviationType, locationInfo, originalAssignment) {
    // Use the strategy from the deviation type configuration
    switch (deviationType.strategy) {
      case 'preserve_unloading':
        return ASSIGNMENT_STRATEGIES.PRESERVE_UNLOADING;
      case 'preserve_loading':
        return ASSIGNMENT_STRATEGIES.PRESERVE_LOADING;
      case 'full_route_change':
        return ASSIGNMENT_STRATEGIES.FULL_ROUTE_CHANGE;
      default:
        return ASSIGNMENT_STRATEGIES.AUTO_DETERMINE;
    }
  }

  /**
   * Build assignment creation requirements based on strategy
   * @param {string} strategy - Assignment creation strategy
   * @param {Object} locationInfo - Location information
   * @param {Object} tripDetails - Trip details
   * @param {Object} originalAssignment - Original assignment
   * @returns {Promise<Object>} Assignment requirements
   */
  async _buildAssignmentRequirements(strategy, locationInfo, tripDetails, originalAssignment) {
    const requirements = {
      loadingLocationId: null,
      loadingLocationName: null,
      unloadingLocationId: null,
      unloadingLocationName: null,
      preserveOriginalRoute: null,
      assignmentType: strategy
    };

    switch (strategy) {
      case ASSIGNMENT_STRATEGIES.PRESERVE_LOADING:
        // Keep original loading location, change unloading location
        requirements.loadingLocationId = originalAssignment?.loading_location_id || tripDetails?.loading_location_id;
        requirements.loadingLocationName = originalAssignment?.loading_location || tripDetails?.original_loading_location;
        requirements.unloadingLocationId = tripDetails?.actual_unloading_location_id;
        requirements.unloadingLocationName = locationInfo.newLocation;
        requirements.preserveOriginalRoute = {
          loadingLocationId: requirements.loadingLocationId,
          loadingLocationName: requirements.loadingLocationName
        };
        break;

      case ASSIGNMENT_STRATEGIES.PRESERVE_UNLOADING:
        // Keep original unloading location, change loading location
        requirements.loadingLocationId = tripDetails?.actual_loading_location_id;
        requirements.loadingLocationName = locationInfo.newLocation;
        requirements.unloadingLocationId = originalAssignment?.unloading_location_id || tripDetails?.unloading_location_id;
        requirements.unloadingLocationName = originalAssignment?.unloading_location || tripDetails?.original_unloading_location;
        break;

      case ASSIGNMENT_STRATEGIES.FULL_ROUTE_CHANGE:
        // Change both loading and unloading locations
        requirements.loadingLocationId = tripDetails?.actual_loading_location_id;
        requirements.loadingLocationName = locationInfo.originalLocation;
        requirements.unloadingLocationId = tripDetails?.actual_unloading_location_id;
        requirements.unloadingLocationName = locationInfo.newLocation;
        break;

      default:
        // Auto-determine based on available information
        if (tripDetails?.actual_unloading_location_id && locationInfo.locationType === 'unloading') {
          // Unloading deviation - preserve loading
          requirements.loadingLocationId = originalAssignment?.loading_location_id;
          requirements.loadingLocationName = originalAssignment?.loading_location;
          requirements.unloadingLocationId = tripDetails.actual_unloading_location_id;
          requirements.unloadingLocationName = locationInfo.newLocation;
          requirements.preserveOriginalRoute = {
            loadingLocationId: requirements.loadingLocationId,
            loadingLocationName: requirements.loadingLocationName
          };
        } else if (tripDetails?.actual_loading_location_id && locationInfo.locationType === 'loading') {
          // Loading deviation - preserve unloading
          requirements.loadingLocationId = tripDetails.actual_loading_location_id;
          requirements.loadingLocationName = locationInfo.newLocation;
          requirements.unloadingLocationId = originalAssignment?.unloading_location_id;
          requirements.unloadingLocationName = originalAssignment?.unloading_location;
        }
        break;
    }

    return requirements;
  }

  /**
   * Calculate confidence level for the parsing result
   * @param {Object} deviationType - Deviation type
   * @param {Object} locationInfo - Location information
   * @returns {number} Confidence score (0-1)
   */
  _calculateConfidence(deviationType, locationInfo) {
    let confidence = 0.5; // Base confidence

    // Increase confidence if we found a specific pattern
    if (deviationType && deviationType.pattern) {
      confidence += 0.3;
    }

    // Increase confidence if we extracted location information
    if (locationInfo.newLocation) {
      confidence += 0.2;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Fallback analysis when pattern matching fails
   * @param {string} description - Exception description
   * @param {Object} tripDetails - Trip details
   * @param {Object} originalAssignment - Original assignment
   * @returns {Object} Fallback analysis result
   */
  _fallbackAnalysis(description, tripDetails, originalAssignment) {
    this.logger.log('Using fallback analysis for exception parsing');

    // Simple fallback based on current system logic
    let strategy = ASSIGNMENT_STRATEGIES.AUTO_DETERMINE;
    let locationType = 'unknown';

    if (description.toLowerCase().includes('unloading')) {
      strategy = ASSIGNMENT_STRATEGIES.PRESERVE_LOADING;
      locationType = 'unloading';
    } else if (description.toLowerCase().includes('loading')) {
      strategy = ASSIGNMENT_STRATEGIES.PRESERVE_UNLOADING;
      locationType = 'loading';
    }

    return {
      success: true,
      deviationType: locationType,
      strategy,
      locationInfo: {
        newLocation: 'Extracted from trip details',
        locationType
      },
      assignmentRequirements: {
        assignmentType: strategy,
        preserveOriginalRoute: strategy === ASSIGNMENT_STRATEGIES.PRESERVE_LOADING ? {
          loadingLocationId: originalAssignment?.loading_location_id,
          loadingLocationName: originalAssignment?.loading_location
        } : null
      },
      confidence: 0.3,
      fallback: true
    };
  }
}

module.exports = {
  ExceptionRequestParser,
  EXCEPTION_PATTERNS,
  ASSIGNMENT_STRATEGIES
};
