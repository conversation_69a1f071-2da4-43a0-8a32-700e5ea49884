# System Health Monitoring Integration - Design Document

## Overview

The System Health Monitoring Integration transforms the existing Settings page into a comprehensive control center for maintaining system health across three critical modules: Shift Management, Assignment Management, and Trip Monitoring. The design leverages existing monitoring scripts and database functions while providing a unified interface for automated issue detection and resolution.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        SP[Settings Page]
        SHM[System Health Monitor]
        TMS[Task Management System]
    end
    
    subgraph "API Layer"
        SHE[System Health Endpoints]
        MSE[Monitoring Service Endpoints]
        TME[Task Management Endpoints]
    end
    
    subgraph "Service Layer"
        SMS[System Monitoring Service]
        AFS[Automated Fix Service]
        TMS_SVC[Task Management Service]
        CUS[Cleanup Service]
    end
    
    subgraph "Integration Layer"
        MSS[Monitor Shift Status Script]
        SCJ[Setup Cron Jobs Script]
        ESS[Enhanced Shift Status Service]
    end
    
    subgraph "Database Layer"
        PG[(PostgreSQL)]
        SF[System Functions]
        AL[Audit Logs]
    end
    
    SP --> SHM
    SP --> TMS
    SHM --> SHE
    TMS --> TME
    SHE --> SMS
    SHE --> AFS
    TME --> TMS_SVC
    SMS --> MSS
    SMS --> ESS
    AFS --> SF
    CUS --> MSS
    CUS --> SCJ
    SMS --> PG
    AFS --> AL
```

### Component Architecture

```mermaid
graph LR
    subgraph "System Health Monitor Component"
        SHD[System Health Dashboard]
        SMM[Shift Management Monitor]
        AMM[Assignment Management Monitor]
        TMM[Trip Monitoring Monitor]
        QFB[Quick Fix Buttons]
    end
    
    subgraph "Task Management Component"
        TL[Task List]
        TS[Task Scheduler]
        TR[Task Recommendations]
        TH[Task History]
    end
    
    subgraph "Cleanup Management Component"
        CA[Cleanup Analyzer]
        CR[Cleanup Runner]
        CRP[Cleanup Reports]
    end
```

## Components and Interfaces

### 1. Frontend Components - Settings Page Integration

#### Settings.js Enhancement
**Purpose:** Add new "System Health Monitor" menu item to existing Settings page
**Location:** `client/src/pages/settings/Settings.js`

The existing Settings page will be enhanced by adding a new menu item to the `settingsMenus` array:

```javascript
// Addition to existing settingsMenus array in Settings.js
{
  id: 'system-health-monitor',
  title: 'System Health Monitor',
  icon: '🏥',
  description: 'Comprehensive monitoring and automated fixing for Shift Management, Assignment Management, and Trip Monitoring modules',
  component: SystemHealthMonitor
}
```

#### SystemHealthMonitor.jsx
**Purpose:** Main dashboard component for comprehensive system health monitoring
**Location:** `client/src/pages/settings/components/SystemHealthMonitor.jsx`

This component will be structured similar to the existing `ShiftSynchronizationMonitor.js` but with expanded functionality for all three modules:

```javascript
// Component Structure
const SystemHealthMonitor = () => {
  const [healthStatus, setHealthStatus] = useState({
    shifts: { status: 'loading', issues: [], lastCheck: null },
    assignments: { status: 'loading', issues: [], lastCheck: null },
    trips: { status: 'loading', issues: [], lastCheck: null }
  });
  
  const [isFixing, setIsFixing] = useState({
    shifts: false,
    assignments: false,
    trips: false
  });

  // Real-time status updates
  useEffect(() => {
    const fetchHealthStatus = async () => {
      const response = await api.get('/api/system-health/status');
      setHealthStatus(response.data);
    };
    
    fetchHealthStatus();
    const interval = setInterval(fetchHealthStatus, 30000); // 30 second updates
    return () => clearInterval(interval);
  }, []);

  // Fix handlers for each module
  const handleFixShifts = async () => { /* Implementation */ };
  const handleFixAssignments = async () => { /* Implementation */ };
  const handleFixTrips = async () => { /* Implementation */ };
};
```

**Props Interface:**
```typescript
interface SystemHealthMonitorProps {
  onStatusChange?: (module: string, status: HealthStatus) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface HealthStatus {
  status: 'operational' | 'warning' | 'critical' | 'loading';
  issues: Issue[];
  lastCheck: Date;
  metrics?: HealthMetrics;
}
```

#### TaskManagementPanel.jsx
**Purpose:** Comprehensive task management interface
**Location:** `client/src/pages/settings/components/TaskManagementPanel.jsx`

```javascript
const TaskManagementPanel = () => {
  const [tasks, setTasks] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [scheduledTasks, setScheduledTasks] = useState([]);
  
  // Task management functionality
  const createTask = async (taskData) => { /* Implementation */ };
  const completeTask = async (taskId) => { /* Implementation */ };
  const scheduleTask = async (taskData, schedule) => { /* Implementation */ };
};
```

#### CleanupManagementPanel.jsx
**Purpose:** Automated cleanup management interface
**Location:** `client/src/pages/settings/components/CleanupManagementPanel.jsx`

```javascript
const CleanupManagementPanel = () => {
  const [cleanupStatus, setCleanupStatus] = useState('idle');
  const [analysisResults, setAnalysisResults] = useState(null);
  const [cleanupReports, setCleanupReports] = useState([]);
  
  // Cleanup functionality
  const analyzeUnusedFunctions = async () => { /* Implementation */ };
  const executeCleanup = async (analysisId) => { /* Implementation */ };
  const rollbackCleanup = async (cleanupId) => { /* Implementation */ };
};
```

### 2. Backend API Endpoints

#### System Health Endpoints
**Base Path:** `/api/system-health`

```javascript
// GET /api/system-health/status
// Returns current health status for all modules
{
  shifts: {
    status: 'operational' | 'warning' | 'critical',
    issues: [
      {
        id: 'shift-001',
        type: 'status_mismatch',
        severity: 'medium',
        description: 'Night shift not activated for DT-100',
        affectedRecords: ['shift_id_123'],
        autoFixable: true
      }
    ],
    lastCheck: '2025-07-15T20:30:00Z',
    metrics: {
      totalShifts: 4,
      activeShifts: 2,
      scheduledShifts: 2,
      completedShifts: 0,
      statusMismatches: 0
    }
  },
  assignments: { /* Similar structure */ },
  trips: { /* Similar structure */ }
}

// POST /api/system-health/fix-shifts
// Executes automated shift fixes
{
  success: true,
  fixesApplied: [
    {
      type: 'status_update',
      description: 'Updated shift status from scheduled to active',
      affectedRecords: ['shift_id_123'],
      timestamp: '2025-07-15T20:31:00Z'
    }
  ],
  summary: {
    totalFixes: 1,
    successfulFixes: 1,
    failedFixes: 0
  }
}

// POST /api/system-health/fix-assignments
// POST /api/system-health/fix-trips
// Similar structure to fix-shifts
```

#### Task Management Endpoints
**Base Path:** `/api/tasks`

```javascript
// GET /api/tasks
// Returns all tasks with filtering options
{
  tasks: [
    {
      id: 'task-001',
      type: 'maintenance',
      priority: 'high',
      status: 'pending',
      title: 'Fix shift status inconsistencies',
      description: 'Multiple shifts showing incorrect status',
      createdAt: '2025-07-15T20:00:00Z',
      scheduledFor: '2025-07-15T21:00:00Z',
      estimatedDuration: 300, // seconds
      autoExecutable: true
    }
  ],
  recommendations: [
    {
      id: 'rec-001',
      type: 'optimization',
      priority: 'medium',
      title: 'Schedule regular cleanup',
      description: 'Consider scheduling weekly cleanup tasks',
      actionable: true
    }
  ]
}

// POST /api/tasks
// Creates new task
// PUT /api/tasks/:id
// Updates task status
// DELETE /api/tasks/:id
// Removes completed task
```

#### Cleanup Management Endpoints
**Base Path:** `/api/cleanup`

```javascript
// POST /api/cleanup/analyze
// Analyzes codebase for unused functions
{
  analysisId: 'analysis-001',
  status: 'completed',
  results: {
    serverFiles: {
      totalFiles: 45,
      totalFunctions: 234,
      unusedFunctions: 12,
      criticalFunctions: 222,
      details: [
        {
          file: 'server/utils/legacy-helper.js',
          unusedFunctions: ['oldHelperFunction', 'deprecatedUtil'],
          safeToRemove: true
        }
      ]
    },
    scriptFiles: { /* Similar structure */ }
  },
  recommendations: [
    'Remove 12 unused functions to reduce bundle size',
    'Consider refactoring 3 large functions for better maintainability'
  ]
}

// POST /api/cleanup/execute
// Executes cleanup based on analysis
{
  cleanupId: 'cleanup-001',
  success: true,
  summary: {
    filesModified: 8,
    functionsRemoved: 12,
    linesRemoved: 145,
    backupCreated: true
  },
  rollbackAvailable: true
}
```

### 3. Service Layer Components

#### SystemMonitoringService.js
**Purpose:** Core service for system health monitoring
**Location:** `server/services/SystemMonitoringService.js`

```javascript
class SystemMonitoringService {
  constructor() {
    this.monitoringScripts = {
      shifts: require('../scripts/monitor-shift-status'),
      assignments: require('../scripts/monitor-assignments'),
      trips: require('../scripts/monitor-trips')
    };
  }

  async getSystemHealth() {
    const [shiftsHealth, assignmentsHealth, tripsHealth] = await Promise.all([
      this.checkShiftHealth(),
      this.checkAssignmentHealth(),
      this.checkTripHealth()
    ]);

    return {
      shifts: shiftsHealth,
      assignments: assignmentsHealth,
      trips: tripsHealth,
      overall: this.calculateOverallHealth([shiftsHealth, assignmentsHealth, tripsHealth])
    };
  }

  async checkShiftHealth() {
    // Integrate with existing monitor-shift-status.js
    const results = await this.monitoringScripts.shifts.monitorShiftStatus();
    return this.parseShiftResults(results);
  }

  async fixShiftIssues() {
    // Execute schedule_auto_activation() and related fixes
    const fixes = await this.executeShiftFixes();
    await this.logFixes('shifts', fixes);
    return fixes;
  }
}
```

#### AutomatedFixService.js
**Purpose:** Handles automated fixing of detected issues
**Location:** `server/services/AutomatedFixService.js`

```javascript
class AutomatedFixService {
  async fixShifts() {
    const fixes = [];
    
    // Execute database function
    await this.pool.query('SELECT schedule_auto_activation()');
    fixes.push({
      type: 'database_function',
      description: 'Executed schedule_auto_activation()',
      timestamp: new Date()
    });

    // Verify fixes were applied
    const verification = await this.verifyShiftFixes();
    
    return {
      success: verification.success,
      fixes: fixes,
      verification: verification
    };
  }

  async fixAssignments() {
    // Synchronize assignment displays with active shifts
    const assignments = await this.getAssignmentsWithIssues();
    const fixes = [];

    for (const assignment of assignments) {
      const fix = await this.synchronizeAssignment(assignment);
      fixes.push(fix);
    }

    return { success: true, fixes };
  }

  async fixTrips() {
    // Verify and fix trip workflow issues
    const tripIssues = await this.detectTripIssues();
    const fixes = [];

    for (const issue of tripIssues) {
      const fix = await this.resolveTripIssue(issue);
      fixes.push(fix);
    }

    return { success: true, fixes };
  }
}
```

#### TaskManagementService.js
**Purpose:** Manages maintenance tasks and recommendations
**Location:** `server/services/TaskManagementService.js`

```javascript
class TaskManagementService {
  async createTask(taskData) {
    const task = await this.pool.query(`
      INSERT INTO system_tasks (type, priority, title, description, scheduled_for, auto_executable)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [taskData.type, taskData.priority, taskData.title, taskData.description, taskData.scheduledFor, taskData.autoExecutable]);

    return task.rows[0];
  }

  async executeScheduledTasks() {
    const tasks = await this.getScheduledTasks();
    const results = [];

    for (const task of tasks) {
      if (task.auto_executable) {
        const result = await this.executeTask(task);
        results.push(result);
      }
    }

    return results;
  }

  async generateRecommendations() {
    const systemHealth = await this.systemMonitoringService.getSystemHealth();
    const recommendations = [];

    // Analyze patterns and generate recommendations
    if (systemHealth.shifts.issues.length > 0) {
      recommendations.push({
        type: 'maintenance',
        priority: 'high',
        title: 'Schedule regular shift monitoring',
        description: 'Consider increasing monitoring frequency during shift transitions'
      });
    }

    return recommendations;
  }
}
```

#### CleanupService.js
**Purpose:** Handles automated code cleanup operations
**Location:** `server/services/CleanupService.js`

```javascript
class CleanupService {
  async analyzeUnusedFunctions() {
    const analysis = {
      id: this.generateAnalysisId(),
      serverFiles: await this.analyzeServerFiles(),
      scriptFiles: await this.analyzeScriptFiles(),
      timestamp: new Date()
    };

    await this.saveAnalysis(analysis);
    return analysis;
  }

  async analyzeServerFiles() {
    const files = await this.getJavaScriptFiles('server/**/*.js');
    const results = {
      totalFiles: files.length,
      totalFunctions: 0,
      unusedFunctions: 0,
      criticalFunctions: 0,
      details: []
    };

    for (const file of files) {
      const analysis = await this.analyzeFile(file);
      results.totalFunctions += analysis.totalFunctions;
      results.unusedFunctions += analysis.unusedFunctions.length;
      results.criticalFunctions += analysis.criticalFunctions;
      
      if (analysis.unusedFunctions.length > 0) {
        results.details.push({
          file: file,
          unusedFunctions: analysis.unusedFunctions,
          safeToRemove: analysis.safeToRemove
        });
      }
    }

    return results;
  }

  async executeCleanup(analysisId) {
    const analysis = await this.getAnalysis(analysisId);
    const backup = await this.createBackup();
    
    const cleanup = {
      id: this.generateCleanupId(),
      analysisId: analysisId,
      backupId: backup.id,
      filesModified: 0,
      functionsRemoved: 0,
      linesRemoved: 0,
      timestamp: new Date()
    };

    try {
      // Execute cleanup operations
      for (const detail of analysis.serverFiles.details) {
        if (detail.safeToRemove) {
          const result = await this.removeUnusedFunctions(detail.file, detail.unusedFunctions);
          cleanup.filesModified += result.filesModified;
          cleanup.functionsRemoved += result.functionsRemoved;
          cleanup.linesRemoved += result.linesRemoved;
        }
      }

      cleanup.success = true;
      await this.saveCleanup(cleanup);
      return cleanup;
    } catch (error) {
      await this.rollbackToBackup(backup.id);
      throw error;
    }
  }
}
```

## Data Models

### System Health Models

```typescript
interface SystemHealthStatus {
  shifts: ModuleHealthStatus;
  assignments: ModuleHealthStatus;
  trips: ModuleHealthStatus;
  overall: OverallHealthStatus;
}

interface ModuleHealthStatus {
  status: 'operational' | 'warning' | 'critical' | 'loading';
  issues: Issue[];
  lastCheck: Date;
  metrics: HealthMetrics;
}

interface Issue {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedRecords: string[];
  autoFixable: boolean;
  recommendedAction?: string;
}

interface HealthMetrics {
  [key: string]: number | string | boolean;
}
```

### Task Management Models

```typescript
interface Task {
  id: string;
  type: 'maintenance' | 'cleanup' | 'monitoring' | 'optimization';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  title: string;
  description: string;
  createdAt: Date;
  scheduledFor?: Date;
  completedAt?: Date;
  estimatedDuration?: number;
  autoExecutable: boolean;
  metadata?: Record<string, any>;
}

interface Recommendation {
  id: string;
  type: 'optimization' | 'maintenance' | 'security' | 'performance';
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  actionable: boolean;
  estimatedImpact?: string;
  implementationEffort?: 'low' | 'medium' | 'high';
}
```

### Database Schema Extensions

```sql
-- System Tasks Table
CREATE TABLE system_tasks (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scheduled_for TIMESTAMP,
    completed_at TIMESTAMP,
    estimated_duration INTEGER,
    auto_executable BOOLEAN DEFAULT false,
    metadata JSONB,
    created_by INTEGER REFERENCES users(id)
);

-- System Health Logs Table
CREATE TABLE system_health_logs (
    id SERIAL PRIMARY KEY,
    module VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    issues JSONB,
    metrics JSONB,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Automated Fix Logs Table
CREATE TABLE automated_fix_logs (
    id SERIAL PRIMARY KEY,
    module VARCHAR(50) NOT NULL,
    fix_type VARCHAR(100) NOT NULL,
    description TEXT,
    affected_records JSONB,
    success BOOLEAN,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    executed_by INTEGER REFERENCES users(id),
    rollback_data JSONB
);

-- Cleanup Operations Table
CREATE TABLE cleanup_operations (
    id SERIAL PRIMARY KEY,
    analysis_id VARCHAR(100) NOT NULL,
    backup_id VARCHAR(100),
    files_modified INTEGER DEFAULT 0,
    functions_removed INTEGER DEFAULT 0,
    lines_removed INTEGER DEFAULT 0,
    success BOOLEAN,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    rollback_available BOOLEAN DEFAULT false
);
```

## Error Handling

### Frontend Error Handling

```javascript
// Error boundary for system health components
class SystemHealthErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('System Health Monitor Error:', error, errorInfo);
    // Log to monitoring service
    api.post('/api/system-health/log-error', {
      error: error.message,
      stack: error.stack,
      component: 'SystemHealthMonitor'
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h3>System Health Monitor Error</h3>
          <p>Unable to load system health information. Please refresh the page.</p>
          <button onClick={() => window.location.reload()}>Refresh Page</button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### Backend Error Handling

```javascript
// Centralized error handling for system health endpoints
class SystemHealthError extends Error {
  constructor(message, code, module, details = {}) {
    super(message);
    this.name = 'SystemHealthError';
    this.code = code;
    this.module = module;
    this.details = details;
  }
}

// Error handling middleware
const systemHealthErrorHandler = (error, req, res, next) => {
  if (error instanceof SystemHealthError) {
    res.status(400).json({
      success: false,
      error: {
        message: error.message,
        code: error.code,
        module: error.module,
        details: error.details
      }
    });
  } else {
    // Log unexpected errors
    console.error('Unexpected System Health Error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal system health error',
        code: 'INTERNAL_ERROR'
      }
    });
  }
};
```

## Testing Strategy

### Unit Testing

```javascript
// SystemMonitoringService tests
describe('SystemMonitoringService', () => {
  let service;
  let mockPool;

  beforeEach(() => {
    mockPool = {
      query: jest.fn()
    };
    service = new SystemMonitoringService(mockPool);
  });

  describe('getSystemHealth', () => {
    it('should return health status for all modules', async () => {
      // Mock database responses
      mockPool.query
        .mockResolvedValueOnce({ rows: [{ /* shift data */ }] })
        .mockResolvedValueOnce({ rows: [{ /* assignment data */ }] })
        .mockResolvedValueOnce({ rows: [{ /* trip data */ }] });

      const health = await service.getSystemHealth();

      expect(health).toHaveProperty('shifts');
      expect(health).toHaveProperty('assignments');
      expect(health).toHaveProperty('trips');
      expect(health).toHaveProperty('overall');
    });
  });

  describe('fixShiftIssues', () => {
    it('should execute fixes and return results', async () => {
      mockPool.query.mockResolvedValue({ rows: [] });

      const result = await service.fixShiftIssues();

      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('fixes');
      expect(mockPool.query).toHaveBeenCalledWith('SELECT schedule_auto_activation()');
    });
  });
});
```

### Integration Testing

```javascript
// API endpoint integration tests
describe('System Health API', () => {
  describe('GET /api/system-health/status', () => {
    it('should return current system health status', async () => {
      const response = await request(app)
        .get('/api/system-health/status')
        .expect(200);

      expect(response.body).toHaveProperty('shifts');
      expect(response.body).toHaveProperty('assignments');
      expect(response.body).toHaveProperty('trips');
    });
  });

  describe('POST /api/system-health/fix-shifts', () => {
    it('should execute shift fixes and return results', async () => {
      const response = await request(app)
        .post('/api/system-health/fix-shifts')
        .expect(200);

      expect(response.body).toHaveProperty('success');
      expect(response.body).toHaveProperty('fixesApplied');
    });
  });
});
```

### End-to-End Testing

```javascript
// E2E tests for Settings page integration
describe('Settings Page System Health', () => {
  it('should display system health status and allow fixes', async () => {
    await page.goto('/settings');
    
    // Wait for system health monitor to load
    await page.waitForSelector('[data-testid="system-health-monitor"]');
    
    // Check that all modules are displayed
    const shiftsStatus = await page.textContent('[data-testid="shifts-status"]');
    const assignmentsStatus = await page.textContent('[data-testid="assignments-status"]');
    const tripsStatus = await page.textContent('[data-testid="trips-status"]');
    
    expect(shiftsStatus).toBeTruthy();
    expect(assignmentsStatus).toBeTruthy();
    expect(tripsStatus).toBeTruthy();
    
    // Test fix functionality
    if (shiftsStatus.includes('Issues Detected')) {
      await page.click('[data-testid="fix-shifts-button"]');
      await page.waitForSelector('[data-testid="fix-success-message"]');
    }
  });
});
```

This comprehensive design provides a solid foundation for implementing the System Health Monitoring Integration. The architecture is modular, scalable, and maintains compatibility with existing systems while adding powerful new capabilities for automated monitoring and maintenance.