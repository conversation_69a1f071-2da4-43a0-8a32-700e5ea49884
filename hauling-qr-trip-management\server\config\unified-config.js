/**
 * Unified Configuration Module for Server
 * Loads configuration from the root .env file and applies environment-specific settings
 */

const path = require('path');
const fs = require('fs');

// Load configuration from root .env file
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import the config loader from root
const { loadConfig } = require('../../config-loader');

/**
 * Get unified configuration for server
 */
function getServerConfig() {
  const config = loadConfig();
  
  // Add server-specific derived values
  config.IS_DEVELOPMENT = config.NODE_ENV === 'development';
  config.IS_PRODUCTION = config.NODE_ENV === 'production';
  
  // CORS configuration - COMPREHENSIVE FIX for all access patterns
  config.CORS_ORIGINS = [
    // Environment-based URLs
    config.FRONTEND_URL,
    config.FRONTEND_URL_HTTP,
    config.FRONTEND_URL_HTTPS,

    // Localhost variations (using centralized port configuration)
    `http://localhost:${config.CLIENT_PORT}`,
    `https://localhost:${config.CLIENT_PORT}`,
    `http://localhost:${config.BACKEND_HTTP_PORT}`,
    `https://localhost:${config.HTTPS_PORT}`,
    // Legacy port support for backward compatibility
    'http://localhost:3000',
    'https://localhost:3000',
    'http://localhost:5000',
    'https://localhost:5000',

    // 127.0.0.1 variations (using centralized port configuration)
    `http://127.0.0.1:${config.CLIENT_PORT}`,
    `https://127.0.0.1:${config.CLIENT_PORT}`,
    `http://127.0.0.1:${config.BACKEND_HTTP_PORT}`,
    `https://127.0.0.1:${config.HTTPS_PORT}`,
    // Legacy port support for backward compatibility
    'http://127.0.0.1:3000',
    'https://127.0.0.1:3000',
    'http://127.0.0.1:5000',
    'https://127.0.0.1:5000',

    // IP-based origins for mobile/network access (using centralized port configuration)
    `http://${config.IP_ADDRESS}:${config.CLIENT_PORT}`,
    `https://${config.IP_ADDRESS}:${config.CLIENT_PORT}`,
    `http://${config.IP_ADDRESS}:${config.BACKEND_HTTP_PORT}`,
    `https://${config.IP_ADDRESS}:${config.HTTPS_PORT}`,
    // Legacy port support for backward compatibility
    `http://${config.IP_ADDRESS}:3000`,
    `https://${config.IP_ADDRESS}:3000`,
    `http://${config.IP_ADDRESS}:5000`,
    `https://${config.IP_ADDRESS}:5000`,

    // Common development patterns (using centralized port configuration)
    `http://0.0.0.0:${config.CLIENT_PORT}`,
    `https://0.0.0.0:${config.CLIENT_PORT}`
  ];

  // CRITICAL: In development, allow ALL origins to prevent any CORS issues
  if (config.IS_DEVELOPMENT) {
    config.CORS_ORIGINS.push('*');
    // Only log CORS configuration if not suppressed
    if (process.env.SUPPRESS_CORS_CONFIG_MESSAGES !== 'true') {
      console.log('🌐 DEVELOPMENT MODE: CORS set to allow ALL origins');
    }
  }

  // Remove duplicates and null values
  config.CORS_ORIGINS = [...new Set(config.CORS_ORIGINS.filter(Boolean))];
  
  // SSL configuration paths (absolute paths)
  if (config.SSL_CERT_PATH) {
    // The SSL paths in .env are relative to server directory
    config.SSL_CERT_PATH_ABS = path.resolve(__dirname, '..', config.SSL_CERT_PATH);
    config.SSL_KEY_PATH_ABS = path.resolve(__dirname, '..', config.SSL_KEY_PATH);
    if (config.SSL_CA_PATH) {
      config.SSL_CA_PATH_ABS = path.resolve(__dirname, '..', config.SSL_CA_PATH);
    }
  }
  
  // Validate SSL certificates if HTTPS is enabled
  if (config.ENABLE_HTTPS) {
    if (!config.SSL_CERT_PATH_ABS || !fs.existsSync(config.SSL_CERT_PATH_ABS)) {
      console.warn(`⚠️  SSL certificate not found: ${config.SSL_CERT_PATH_ABS}`);
      config.SSL_AVAILABLE = false;
    } else if (!config.SSL_KEY_PATH_ABS || !fs.existsSync(config.SSL_KEY_PATH_ABS)) {
      console.warn(`⚠️  SSL key not found: ${config.SSL_KEY_PATH_ABS}`);
      config.SSL_AVAILABLE = false;
    } else {
      config.SSL_AVAILABLE = true;
    }
  } else {
    config.SSL_AVAILABLE = false;
  }
  
  // Rate limiting configuration - CRITICAL FIX for development
  config.RATE_LIMIT_CONFIG = {
    windowMs: config.IS_DEVELOPMENT ? 60000 : config.RATE_LIMIT_WINDOW_MS, // 1 minute in dev
    max: config.IS_DEVELOPMENT ? 1000 : config.RATE_LIMIT_MAX_REQUESTS, // Much higher limit in dev
    message: {
      error: config.IS_DEVELOPMENT ? 'Too Many HTTPS Requests' : 'Too many requests from this IP, please try again later.',
      message: config.IS_DEVELOPMENT ? 'Too many HTTPS requests from this IP, please try again later.' : 'Rate limit exceeded',
      retryAfter: config.IS_DEVELOPMENT ? '15 minutes' : Math.ceil(config.RATE_LIMIT_WINDOW_MS / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: () => config.IS_DEVELOPMENT && config.DEV_DISABLE_RATE_LIMITING
  };
  
  config.AUTH_RATE_LIMIT_CONFIG = {
    windowMs: config.IS_DEVELOPMENT ? 60000 : config.AUTH_RATE_LIMIT_WINDOW_MS, // 1 minute in dev
    max: config.IS_DEVELOPMENT ? 100 : config.AUTH_RATE_LIMIT_MAX_REQUESTS, // Higher limit in dev
    message: {
      error: 'Too many authentication attempts from this IP, please try again later.',
      retryAfter: config.IS_DEVELOPMENT ? '1 minute' : Math.ceil(config.AUTH_RATE_LIMIT_WINDOW_MS / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: () => config.IS_DEVELOPMENT && config.DEV_DISABLE_RATE_LIMITING,
    skipSuccessfulRequests: true // Don't count successful requests
  };
  
  // Database configuration with SSL for production
  config.DB_CONFIG = {
    host: config.DB_HOST,
    port: config.DB_PORT,
    database: config.DB_NAME,
    user: config.DB_USER,
    password: config.DB_PASSWORD,
    max: config.DB_POOL_MAX,
    min: config.DB_POOL_MIN,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 60000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
    statement_timeout: 30000,
    query_timeout: 25000,
    application_name: 'hauling-qr-system',
    // Disable SSL for local development (localhost/127.0.0.1)
    ssl: (config.IS_PRODUCTION &&
          config.DB_HOST !== 'localhost' &&
          config.DB_HOST !== '127.0.0.1') ? { rejectUnauthorized: false } : false
  };
  
  // Enhanced Logging configuration with environment-specific settings
  config.LOG_CONFIG = {
    // Environment-specific log levels
    level: config.IS_PRODUCTION ? 'warn' : (config.LOG_LEVEL || 'info'),

    // Monitoring service logging configuration
    monitoring: {
      enabled: config.IS_PRODUCTION ? false : true, // Disable verbose monitoring logs in production
      level: config.IS_PRODUCTION ? 'error' : 'info',
      throttle: config.IS_PRODUCTION ? 300000 : 60000, // 5 min prod, 1 min dev
      deduplication: {
        enabled: true,
        windowMs: 300000, // 5 minutes deduplication window
        maxDuplicates: 3 // Max 3 identical messages per window
      }
    },

    // Performance logging
    performance: {
      enabled: config.IS_DEVELOPMENT,
      slowQueryThreshold: config.IS_PRODUCTION ? 2000 : 1000, // 2s prod, 1s dev
      logAllRequests: config.IS_DEVELOPMENT && config.DEV_ENABLE_DETAILED_LOGS
    },

    // File rotation and management
    rotation: {
      maxSize: config.IS_PRODUCTION ? 10485760 : 5242880, // 10MB prod, 5MB dev
      maxFiles: config.IS_PRODUCTION ? 20 : 10,
      compress: config.IS_PRODUCTION,
      datePattern: 'YYYY-MM-DD',
      auditFile: path.join(__dirname, '../logs/audit.json')
    },

    // Transport configuration
    transports: {
      console: {
        enabled: true,
        level: config.IS_DEVELOPMENT ? 'debug' : 'error'
      },
      file: {
        enabled: true,
        level: config.IS_PRODUCTION ? 'warn' : 'info'
      },
      error: {
        enabled: true,
        level: 'error'
      }
    },

    // Legacy compatibility
    enableDetailedLogs: config.IS_DEVELOPMENT && config.DEV_ENABLE_DETAILED_LOGS,
    enableConsole: true,
    enableFile: true
  };
  
  return config;
}

/**
 * Get SSL options for HTTPS server
 */
function getSSLOptions(config) {
  if (!config.ENABLE_HTTPS || !config.SSL_AVAILABLE) {
    return null;
  }
  
  const options = {
    cert: fs.readFileSync(config.SSL_CERT_PATH_ABS),
    key: fs.readFileSync(config.SSL_KEY_PATH_ABS)
  };
  
  if (config.SSL_CA_PATH_ABS && fs.existsSync(config.SSL_CA_PATH_ABS)) {
    options.ca = fs.readFileSync(config.SSL_CA_PATH_ABS);
  }
  
  return options;
}

/**
 * Display server configuration summary
 */
function displayServerConfig(config) {
  console.log('\n🔧 SERVER CONFIGURATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`🌍 Environment: ${config.NODE_ENV}`);
  console.log(`🔒 HTTPS: ${config.ENABLE_HTTPS ? 'Enabled' : 'Disabled'}`);
  console.log(`🌐 IP Address: ${config.IP_ADDRESS}`);
  console.log(`🚀 HTTP Port: ${config.BACKEND_HTTP_PORT}`);
  if (config.ENABLE_HTTPS) {
    console.log(`🔐 HTTPS Port: ${config.HTTPS_PORT}`);
    console.log(`📜 SSL Cert: ${config.SSL_AVAILABLE ? '✅ Available' : '❌ Missing'}`);
  }
  console.log(`🗄️  Database: ${config.DB_HOST}:${config.DB_PORT}/${config.DB_NAME}`);
  console.log(`⚡ Rate Limiting: ${config.RATE_LIMIT_CONFIG.skip ? 'Disabled (Dev)' : 'Enabled'}`);
  console.log(`📊 Logging: ${config.LOG_CONFIG.level} (${config.LOG_CONFIG.enableDetailedLogs ? 'Detailed' : 'Standard'})`);
  console.log('='.repeat(50));
}

module.exports = {
  getServerConfig,
  getSSLOptions,
  displayServerConfig
};
