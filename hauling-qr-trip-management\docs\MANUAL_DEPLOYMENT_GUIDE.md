# 🚀 Hauling QR Trip System - Manual Deployment Guide

## 📋 Overview
This guide provides step-by-step manual deployment instructions for Ubuntu 24.04 VPS using the `ubuntu` user (non-root). This approach avoids the segfault issues in the automated scripts and gives you full control over each step.

## 🎯 Target Environment
- **OS**: Ubuntu 24.04 LTS
- **User**: ubuntu (non-root, requires sudo)
- **Domain**: truckhaul.top
- **VPS IP**: **************
- **Backend Port**: 8080 (Cloudflare compatible)
- **Frontend Port**: 3000

## ⚠️ Prerequisites
```bash
# Verify you're running as ubuntu user
whoami
# Should output: ubuntu

# Verify sudo access
sudo whoami
# Should output: root

# Check system info
lsb_release -a
# Should show Ubuntu 24.04
```

## 📦 Phase 1: System Dependencies Installation

### 1.1 Update System Packages
```bash
# Update package lists
sudo apt update

# Upgrade existing packages (optional but recommended)
sudo apt upgrade -y
```

### 1.2 Install Essential Packages
```bash
# Install core dependencies (avoiding problematic net-tools)
sudo apt install -y \
  ca-certificates \
  curl \
  git \
  gnupg \
  lsb-release \
  software-properties-common \
  apt-transport-https \
  build-essential

# Verify installation
git --version
curl --version
```

### 1.3 Install Node.js (Multiple Methods)

**Method 1: NodeSource Repository (Recommended)**
```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -

# Install Node.js
sudo apt install -y nodejs

# Verify installation
node --version  # Should be v20.x
npm --version   # Should be 10.x+
```

**Method 2: If Method 1 Fails - Use Snap**
```bash
# Install via snap
sudo snap install node --classic

# Verify installation
node --version
npm --version
```

**Method 3: If Both Fail - Manual Installation**
```bash
# Download and install manually
cd /tmp
wget https://nodejs.org/dist/v20.11.0/node-v20.11.0-linux-x64.tar.xz
tar -xf node-v20.11.0-linux-x64.tar.xz
sudo mv node-v20.11.0-linux-x64 /opt/nodejs
sudo ln -s /opt/nodejs/bin/node /usr/local/bin/node
sudo ln -s /opt/nodejs/bin/npm /usr/local/bin/npm
```

### 1.4 Install PM2 Process Manager
```bash
# Install PM2 globally
sudo npm install -g pm2

# Verify installation
pm2 --version

# Setup PM2 startup script
sudo pm2 startup systemd -u ubuntu --hp /home/<USER>
```

### 1.5 Install PostgreSQL
```bash
# Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Verify installation
sudo systemctl status postgresql
```

### 1.6 Install Nginx
```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Verify installation
sudo systemctl status nginx
nginx -v
```

## 🗂️ Phase 2: Repository and Application Setup

### 2.1 Create Application Directory
```bash
# Create application directory with proper permissions
sudo mkdir -p /var/www/hauling-qr-system
sudo chown ubuntu:ubuntu /var/www/hauling-qr-system
cd /var/www/hauling-qr-system
```

### 2.2 Clone Repository
```bash
# Clone the repository
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git .

# Verify clone
ls -la
# Should show client/, server/, database/, etc.
```

### 2.3 Setup Environment Files
```bash
# Create server environment file
cat > server/.env << 'EOF'
NODE_ENV=production
PORT=8080
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=PostgreSQLPassword123
JWT_SECRET=hauling_qr_jwt_secret_2025_secure_key_for_production
CORS_ORIGIN=https://truckhaul.top,http://truckhaul.top,http://**************:3000,https://**************:3000
EOF

# Create client environment file
cat > client/.env << 'EOF'
REACT_APP_API_URL=https://truckhaul.top/api
REACT_APP_ENVIRONMENT=production
GENERATE_SOURCEMAP=false
EOF

# Set proper permissions
chmod 600 server/.env client/.env
```

## 🗄️ Phase 3: Database Configuration

### 3.1 Setup PostgreSQL User and Database
```bash
# Switch to postgres user and create database
sudo -u postgres psql << 'EOF'
-- Set password for postgres user
ALTER USER postgres PASSWORD 'PostgreSQLPassword123';

-- Create database
CREATE DATABASE hauling_qr_system;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO postgres;

-- Exit
\q
EOF
```

### 3.2 Test Database Connection
```bash
# Test connection
PGPASSWORD=PostgreSQLPassword123 psql -h localhost -U postgres -d hauling_qr_system -c "SELECT version();"
```

### 3.3 Run Database Migrations
```bash
# Navigate to database directory
cd /var/www/hauling-qr-system/database

# Run migrations
PGPASSWORD=PostgreSQLPassword123 psql -h localhost -U postgres -d hauling_qr_system -f schema.sql

# Verify tables were created
PGPASSWORD=PostgreSQLPassword123 psql -h localhost -U postgres -d hauling_qr_system -c "\dt"
```

## 🏗️ Phase 4: Application Building

### 4.1 Install Server Dependencies
```bash
# Navigate to server directory
cd /var/www/hauling-qr-system/server

# Install dependencies
npm install

# Verify installation
ls -la node_modules/ | head -5
```

### 4.2 Install and Build Client
```bash
# Navigate to client directory
cd /var/www/hauling-qr-system/client

# Install dependencies
npm install

# Build production version
npm run build

# Verify build
ls -la build/
```

## ⚙️ Phase 5: Service Configuration

### 5.1 Setup PM2 Ecosystem
```bash
# Create PM2 ecosystem file
cd /var/www/hauling-qr-system
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: '/var/www/hauling-qr-system',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 8080
    },
    error_file: '/var/log/hauling-qr-system/server-error.log',
    out_file: '/var/log/hauling-qr-system/server-out.log',
    log_file: '/var/log/hauling-qr-system/server-combined.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF

# Create log directory
sudo mkdir -p /var/log/hauling-qr-system
sudo chown ubuntu:ubuntu /var/log/hauling-qr-system
```

### 5.2 Configure Nginx
```bash
# Create Nginx configuration
sudo tee /etc/nginx/sites-available/hauling-qr-system << 'EOF'
server {
    listen 80;
    server_name truckhaul.top www.truckhaul.top **************;

    # Serve React build files
    location / {
        root /var/www/hauling-qr-system/client/build;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Proxy API requests to Node.js backend
    location /api {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t
```

## 🚀 Phase 6: Start Services and Verification

### 6.1 Start Backend Service
```bash
# Start the application with PM2
cd /var/www/hauling-qr-system
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Check status
pm2 status
pm2 logs hauling-qr-server --lines 20
```

### 6.2 Restart Nginx
```bash
# Restart Nginx to load new configuration
sudo systemctl restart nginx

# Check status
sudo systemctl status nginx
```

### 6.3 Configure Firewall
```bash
# Configure UFW firewall
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# Check status
sudo ufw status
```

### 6.4 Final Verification
```bash
# Test backend API
curl -I http://localhost:8080/api/health

# Test frontend (should return HTML)
curl -I http://localhost/

# Check all services
sudo systemctl status nginx
pm2 status
sudo systemctl status postgresql
```

## 🔧 Troubleshooting

### Common Issues and Solutions

**1. Node.js Installation Failed**
```bash
# Try alternative installation
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 20
nvm use 20
```

**2. Database Connection Issues**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Reset postgres password
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'PostgreSQLPassword123';"
```

**3. Permission Issues**
```bash
# Fix ownership of application directory
sudo chown -R ubuntu:ubuntu /var/www/hauling-qr-system

# Fix log directory permissions
sudo chown -R ubuntu:ubuntu /var/log/hauling-qr-system
```

**4. PM2 Service Not Starting**
```bash
# Check logs
pm2 logs hauling-qr-server

# Restart service
pm2 restart hauling-qr-server

# Check environment variables
pm2 env hauling-qr-server
```

**5. Nginx Configuration Issues**
```bash
# Test configuration
sudo nginx -t

# Check error logs
sudo tail -f /var/log/nginx/error.log
```

## ✅ Success Indicators

Your deployment is successful when:
- ✅ `curl http://localhost:8080/api/health` returns 200 OK
- ✅ `curl http://localhost/` returns HTML content
- ✅ `pm2 status` shows hauling-qr-server as "online"
- ✅ `sudo systemctl status nginx` shows "active (running)"
- ✅ `sudo systemctl status postgresql` shows "active (running)"

## 🌐 Access Your Application

Once deployed successfully:
- **Frontend**: http://truckhaul.top or http://**************
- **API**: http://truckhaul.top/api or http://**************/api

## 📞 Next Steps

1. **SSL Setup**: Configure Cloudflare SSL or Let's Encrypt
2. **Monitoring**: Setup log rotation and monitoring
3. **Backups**: Configure database backups
4. **Updates**: Plan for application updates

---

**This manual approach avoids all the segfault issues and gives you complete control over each step. Follow each phase carefully and verify before proceeding to the next.**
