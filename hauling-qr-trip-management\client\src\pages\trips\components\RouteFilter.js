import React from 'react';

const RouteFilter = ({
  routes,
  selectedRoute,
  onRouteChange,
  loading = false
}) => {
  return (
    <div>
      <label htmlFor="route_filter" className="block text-sm font-medium text-secondary-700 mb-2">
        Route
      </label>
      <select
        id="route_filter"
        value={selectedRoute}
        onChange={(e) => onRouteChange(e.target.value)}
        className="input"
        disabled={loading}
      >
        <option value="">All Routes</option>
        {routes.map((route) => (
          <option key={route.value} value={route.value}>
            {route.label}
          </option>
        ))}
      </select>
      {loading && (
        <div className="mt-1 text-xs text-secondary-500">
          Loading routes...
        </div>
      )}
    </div>
  );
};

export default RouteFilter;
