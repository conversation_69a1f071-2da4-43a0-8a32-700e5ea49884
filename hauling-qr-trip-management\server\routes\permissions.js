const express = require('express');
const router = express.Router();
const { query, transaction } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas
const bulkUpdatePermissionsSchema = Joi.object({
  permissions: Joi.array().items(
    Joi.object({
      role_name: Joi.string().required(),
      page_key: Joi.string().required(),
      has_access: Joi.boolean().required()
    })
  ).required()
});

// @route   GET /api/permissions
// @desc    Get all role-page permissions
// @access  Private (Admin only)
router.get('/', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can access permission management'
      });
    }

    // Get all permissions with role and page information
    const result = await query(`
      SELECT 
        rp.role_name,
        rp.page_key,
        rp.has_access,
        rp.created_at,
        rp.updated_at
      FROM role_permissions rp
      ORDER BY rp.role_name, rp.page_key
    `);

    // Get all available roles
    const rolesResult = await query('SELECT * FROM get_user_roles()');
    const roles = rolesResult.rows.map(row => row.role_name);

    // Get all unique page keys
    const pagesResult = await query('SELECT DISTINCT page_key FROM role_permissions ORDER BY page_key');
    const pages = pagesResult.rows.map(row => row.page_key);

    // Create a matrix structure for easier frontend consumption
    const permissionMatrix = {};
    
    // Initialize matrix with all roles and pages
    roles.forEach(role => {
      permissionMatrix[role] = {};
      pages.forEach(page => {
        permissionMatrix[role][page] = false; // default to no access
      });
    });

    // Fill in actual permissions
    result.rows.forEach(row => {
      if (permissionMatrix[row.role_name]) {
        permissionMatrix[row.role_name][row.page_key] = row.has_access;
      }
    });

    res.json({
      success: true,
      data: {
        permissions: result.rows,
        matrix: permissionMatrix,
        roles: roles,
        pages: pages
      }
    });

  } catch (error) {
    console.error('Get permissions error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve permissions'
    });
  }
});

// @route   GET /api/permissions/:role
// @desc    Get permissions for a specific role
// @access  Private (Admin or own role permissions)
router.get('/:role', auth, async (req, res) => {
  try {
    const { role } = req.params;

    // Check if user is admin or requesting their own role's permissions
    if (req.user.role !== 'admin' && req.user.role !== role) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only access your own role permissions'
      });
    }

    // Verify role exists
    const rolesResult = await query('SELECT * FROM get_user_roles()');
    const roleExists = rolesResult.rows.some(row => row.role_name === role);

    if (!roleExists) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Role not found'
      });
    }

    // Get permissions for the specific role
    const result = await query(
      'SELECT page_key, has_access, created_at, updated_at FROM role_permissions WHERE role_name = $1 ORDER BY page_key',
      [role]
    );

    // Convert array to object format for frontend consumption
    const permissions = {};
    result.rows.forEach(row => {
      permissions[row.page_key] = row.has_access;
    });

    res.json({
      success: true,
      data: {
        role_name: role,
        permissions: permissions
      }
    });

  } catch (error) {
    console.error('Get role permissions error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve role permissions'
    });
  }
});

// @route   POST /api/permissions
// @desc    Bulk update permissions
// @access  Private (Admin only)
router.post('/', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can update permissions'
      });
    }

    // Validate input
    const { error } = bulkUpdatePermissionsSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const { permissions } = req.body;

    // Verify all roles exist
    const rolesResult = await query('SELECT * FROM get_user_roles()');
    const validRoles = rolesResult.rows.map(row => row.role_name);

    for (const permission of permissions) {
      if (!validRoles.includes(permission.role_name)) {
        return res.status(400).json({
          error: 'Invalid Role',
          message: `Role '${permission.role_name}' does not exist`
        });
      }
    }

    // Use transaction to ensure all updates succeed or fail together
    const result = await transaction(async (client) => {
      const updatedPermissions = [];

      for (const permission of permissions) {
        const { role_name, page_key, has_access } = permission;

        // Upsert permission (insert or update)
        const upsertResult = await client.query(`
          INSERT INTO role_permissions (role_name, page_key, has_access, created_at, updated_at)
          VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          ON CONFLICT (role_name, page_key)
          DO UPDATE SET 
            has_access = EXCLUDED.has_access,
            updated_at = CURRENT_TIMESTAMP
          RETURNING role_name, page_key, has_access, updated_at
        `, [role_name, page_key, has_access]);

        updatedPermissions.push(upsertResult.rows[0]);
      }

      return updatedPermissions;
    });

    res.json({
      success: true,
      message: 'Permissions updated successfully',
      data: {
        updated_count: result.length,
        permissions: result
      }
    });

  } catch (error) {
    console.error('Update permissions error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update permissions'
    });
  }
});

// @route   PUT /api/permissions/:role/:page
// @desc    Update a single permission
// @access  Private (Admin only)
router.put('/:role/:page', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can update permissions'
      });
    }

    const { role, page } = req.params;
    const { has_access } = req.body;

    // Validate has_access is boolean
    if (typeof has_access !== 'boolean') {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'has_access must be a boolean value'
      });
    }

    // Verify role exists
    const rolesResult = await query('SELECT * FROM get_user_roles()');
    const roleExists = rolesResult.rows.some(row => row.role_name === role);

    if (!roleExists) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Role not found'
      });
    }

    // Upsert the permission
    const result = await query(`
      INSERT INTO role_permissions (role_name, page_key, has_access, created_at, updated_at)
      VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      ON CONFLICT (role_name, page_key)
      DO UPDATE SET 
        has_access = EXCLUDED.has_access,
        updated_at = CURRENT_TIMESTAMP
      RETURNING role_name, page_key, has_access, updated_at
    `, [role, page, has_access]);

    res.json({
      success: true,
      message: 'Permission updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update single permission error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update permission'
    });
  }
});

// @route   DELETE /api/permissions/:role/:page
// @desc    Delete a specific permission (sets to false)
// @access  Private (Admin only)
router.delete('/:role/:page', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Only administrators can delete permissions'
      });
    }

    const { role, page } = req.params;

    // Verify role exists
    const rolesResult = await query('SELECT * FROM get_user_roles()');
    const roleExists = rolesResult.rows.some(row => row.role_name === role);

    if (!roleExists) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Role not found'
      });
    }

    // Set permission to false instead of deleting (safer approach)
    const result = await query(`
      UPDATE role_permissions 
      SET has_access = false, updated_at = CURRENT_TIMESTAMP
      WHERE role_name = $1 AND page_key = $2
      RETURNING role_name, page_key, has_access, updated_at
    `, [role, page]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Permission not found'
      });
    }

    res.json({
      success: true,
      message: 'Permission access revoked successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Delete permission error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete permission'
    });
  }
});

// @route   GET /api/permissions/user/:userId
// @desc    Get permissions for a specific user (based on their role)
// @access  Private (Admin or own permissions)
router.get('/user/:userId', auth, async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user is admin or requesting own permissions
    if (req.user.role !== 'admin' && req.user.id !== parseInt(userId)) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only access your own permissions'
      });
    }

    // Get user's role
    const userResult = await query('SELECT role FROM users WHERE id = $1', [userId]);
    
    if (userResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    const userRole = userResult.rows[0].role;

    // Get permissions for user's role
    const permissionsResult = await query(
      'SELECT page_key, has_access FROM role_permissions WHERE role_name = $1 ORDER BY page_key',
      [userRole]
    );

    // Create a simple object for easy permission checking
    const permissions = {};
    permissionsResult.rows.forEach(row => {
      permissions[row.page_key] = row.has_access;
    });

    res.json({
      success: true,
      data: {
        user_id: parseInt(userId),
        role: userRole,
        permissions: permissions
      }
    });

  } catch (error) {
    console.error('Get user permissions error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve user permissions'
    });
  }
});

module.exports = router;