import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';

const LocationFormModal = ({ location, onClose, onSubmit }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: {
      location_code: '',
      name: '',
      type: 'loading',
      address: '',
      coordinates: '',
      status: 'active',
      notes: ''
    }
  });


  // Reset form when location changes
  useEffect(() => {
    if (location) {
      reset({
        location_code: location.location_code || '',
        name: location.name || '',
        type: location.type || 'loading',
        address: location.address || '',
        coordinates: location.coordinates || '',
        status: location.status || 'active',
        notes: location.notes || ''
      });
    } else {
      reset({
        location_code: '',
        name: '',
        type: 'loading',
        address: '',
        coordinates: '',
        status: 'active',
        notes: ''
      });
    }
  }, [location, reset]);

  const onFormSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Validate coordinates format (latitude,longitude)
  const validateCoordinates = (value) => {
    if (!value) return true; // Optional field
    const coordRegex = /^-?\d+\.?\d*,-?\d+\.?\d*$/;
    return coordRegex.test(value) || 'Invalid format. Use: latitude,longitude (e.g., 40.7128,-74.0060)';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-200">
          <h2 className="text-xl font-semibold text-secondary-900">
            {location ? 'Edit Location' : 'Add New Location'}
          </h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-secondary-400 hover:text-secondary-600 transition-colors disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onFormSubmit)} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Location Code */}
            <div>
              <label htmlFor="location_code" className="block text-sm font-medium text-secondary-700 mb-1">
                Location Code *
              </label>
              <input
                type="text"
                id="location_code"
                {...register('location_code', {
                  required: 'Location code is required',
                  minLength: {
                    value: 3,
                    message: 'Location code must be at least 3 characters'
                  },
                  maxLength: {
                    value: 20,
                    message: 'Location code must not exceed 20 characters'
                  },
                  pattern: {
                    value: /^[A-Za-z0-9-_]+$/,
                    message: 'Location code can only contain letters, numbers, hyphens, and underscores'
                  }
                })}
                className={`input ${errors.location_code ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="e.g., LOC-001"
                disabled={isSubmitting}
              />
              {errors.location_code && (
                <p className="text-danger-600 text-sm mt-1">{errors.location_code.message}</p>
              )}
            </div>

            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-secondary-700 mb-1">
                Location Name *
              </label>
              <input
                type="text"
                id="name"
                {...register('name', {
                  required: 'Location name is required',
                  minLength: {
                    value: 2,
                    message: 'Location name must be at least 2 characters'
                  },
                  maxLength: {
                    value: 100,
                    message: 'Location name must not exceed 100 characters'
                  }
                })}
                className={`input ${errors.name ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="e.g., Main Loading Point"
                disabled={isSubmitting}
              />
              {errors.name && (
                <p className="text-danger-600 text-sm mt-1">{errors.name.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Type */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-secondary-700 mb-1">
                Location Type *
              </label>
              <select
                id="type"
                {...register('type', {
                  required: 'Location type is required'
                })}
                className={`input ${errors.type ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                disabled={isSubmitting}
              >
                <option value="loading">Loading Point</option>
                <option value="unloading">Unloading Point</option>
                <option value="checkpoint">Checkpoint</option>
              </select>
              {errors.type && (
                <p className="text-danger-600 text-sm mt-1">{errors.type.message}</p>
              )}
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-secondary-700 mb-1">
                Status
              </label>
              <select
                id="status"
                {...register('status')}
                className="input"
                disabled={isSubmitting}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          {/* Address */}
          <div>
            <label htmlFor="address" className="block text-sm font-medium text-secondary-700 mb-1">
              Address
            </label>
            <textarea
              id="address"
              rows={2}
              {...register('address', {
                maxLength: {
                  value: 500,
                  message: 'Address must not exceed 500 characters'
                }
              })}
              className={`input ${errors.address ? 'border-danger-500 focus:ring-danger-500' : ''}`}
              placeholder="e.g., 123 Industrial Ave, Loading District"
              disabled={isSubmitting}
            />
            {errors.address && (
              <p className="text-danger-600 text-sm mt-1">{errors.address.message}</p>
            )}
          </div>

          {/* Coordinates */}
          <div>
            <label htmlFor="coordinates" className="block text-sm font-medium text-secondary-700 mb-1">
              GPS Coordinates
            </label>
            <input
              type="text"
              id="coordinates"
              {...register('coordinates', {
                validate: validateCoordinates
              })}
              className={`input ${errors.coordinates ? 'border-danger-500 focus:ring-danger-500' : ''}`}
              placeholder="e.g., 40.7128,-74.0060 (latitude,longitude)"
              disabled={isSubmitting}
            />
            {errors.coordinates && (
              <p className="text-danger-600 text-sm mt-1">{errors.coordinates.message}</p>
            )}
            <p className="text-secondary-500 text-sm mt-1">
              Optional. Enter coordinates in decimal degrees format: latitude,longitude
            </p>
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-secondary-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              rows={3}
              {...register('notes', {
                maxLength: {
                  value: 1000,
                  message: 'Notes must not exceed 1000 characters'
                }
              })}
              className={`input ${errors.notes ? 'border-danger-500 focus:ring-danger-500' : ''}`}
              placeholder="Additional notes about this location..."
              disabled={isSubmitting}
            />
            {errors.notes && (
              <p className="text-danger-600 text-sm mt-1">{errors.notes.message}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-secondary-200">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  {location ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                location ? 'Update Location' : 'Create Location'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LocationFormModal;