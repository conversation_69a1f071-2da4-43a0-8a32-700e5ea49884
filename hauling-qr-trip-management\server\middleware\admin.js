/**
 * Admin Authorization Middleware
 * Verifies that the authenticated user has admin role
 * Must be used after the auth middleware
 */
module.exports = (req, res, next) => {
  // Check if user exists (should be set by auth middleware)
  if (!req.user) {
    return res.status(401).json({
      error: 'Authentication Required',
      message: 'User authentication required before checking admin status'
    });
  }

  // Check if user has admin role
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'Admin privileges required to access this resource'
    });
  }

  next();
};