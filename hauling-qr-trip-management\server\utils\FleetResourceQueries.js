const { query } = require('../config/database');

/**
 * Fleet Resource Query Utilities
 * Provides database query functions for fleet resource monitoring
 */
class FleetResourceQueries {
  
  /**
   * Get driver resource counts and details
   * @param {string} dateFrom - Optional start date filter (YYYY-MM-DD)
   * @param {string} dateTo - Optional end date filter (YYYY-MM-DD)
   * @returns {Object} Driver resource summary
   */
  static async getDriverResourceSummary(dateFrom = null, dateTo = null) {
    try {
      // Use the exact same query as analytics to get total drivers
      const totalDriversResult = await query(`
        SELECT COUNT(*) as total_drivers 
        FROM drivers
      `);
      
      const activeDriversResult = await query(`
        SELECT COUNT(*) as active_drivers 
        FROM drivers 
        WHERE status = 'active'
      `);
      
      // Build date filter conditions for drivers
      let driverDateFilter = '';
      const driverQueryParams = [];
      if (dateFrom && dateTo) {
        driverDateFilter = `AND ds.start_date >= $1 AND ds.start_date <= $2`;
        driverQueryParams.push(dateFrom, dateTo);
      } else if (dateFrom || dateTo) {
        const filterDate = dateFrom || dateTo;
        driverDateFilter = `AND ds.start_date = $1`;
        driverQueryParams.push(filterDate);
      } else {
        // Default behavior: current date and active status
        driverDateFilter = `AND ds.start_date <= CURRENT_DATE
          AND (ds.end_date >= CURRENT_DATE OR ds.end_date IS NULL)`;
      }

      // Get drivers assigned to shifts within date range
      const assignedDriversResult = await query(`
        SELECT COUNT(DISTINCT ds.driver_id) as assigned_drivers
        FROM driver_shifts ds
        WHERE ds.status = 'active'
          ${driverDateFilter}
      `, driverQueryParams);
      
      const totalDrivers = parseInt(totalDriversResult.rows[0].total_drivers);
      const activeDrivers = parseInt(activeDriversResult.rows[0].active_drivers);
      const assignedDrivers = parseInt(assignedDriversResult.rows[0].assigned_drivers);
      const unassignedDrivers = activeDrivers - assignedDrivers;
      
      // Get unassigned driver details
      const unassignedDetails = await this.getUnassignedDriverDetails();
      

      
      return {
        total_available: activeDrivers, // Use active drivers as available
        assigned_to_active_shifts: assignedDrivers,
        unassigned: unassignedDrivers,
        unassigned_details: unassignedDetails
      };
    } catch (error) {
      console.error('Error getting driver resource summary:', error);
      throw error;
    }
  }
  
  /**
   * Get truck resource counts and details
   * @param {string} dateFrom - Optional start date filter (YYYY-MM-DD)
   * @param {string} dateTo - Optional end date filter (YYYY-MM-DD)
   * @returns {Object} Truck resource summary
   */
  static async getTruckResourceSummary(dateFrom = null, dateTo = null) {
    try {
      // Use the exact same query as analytics to get total trucks
      const totalTrucksResult = await query(`
        SELECT COUNT(*) as total_trucks 
        FROM dump_trucks
      `);
      
      const activeTrucksResult = await query(`
        SELECT COUNT(*) as active_trucks 
        FROM dump_trucks 
        WHERE status = 'active'
      `);
      
      // Build date filter conditions
      let dateFilter = '';
      const queryParams = [];
      if (dateFrom && dateTo) {
        dateFilter = `AND ds.start_date >= $1 AND ds.start_date <= $2`;
        queryParams.push(dateFrom, dateTo);
      } else if (dateFrom || dateTo) {
        // Default to current date filtering if only one date provided
        const filterDate = dateFrom || dateTo;
        dateFilter = `AND ds.start_date = $1`;
        queryParams.push(filterDate);
      } else {
        // Default behavior: current date and active status
        dateFilter = `AND ds.start_date <= CURRENT_DATE
          AND (ds.end_date >= CURRENT_DATE OR ds.end_date IS NULL)`;
      }

      // Get trucks assigned to shifts within date range
      const assignedTrucksResult = await query(`
        SELECT COUNT(DISTINCT ds.truck_id) as assigned_trucks
        FROM driver_shifts ds
        WHERE ds.status = 'active'
          ${dateFilter}
      `, queryParams);
      
      // Get trucks currently on routes using the exact same logic as truck trip summary
      // Count trucks with ANY active trip status (loading_start, loading_end, unloading_start, unloading_end)
      let tripDateFilter = '';
      const tripQueryParams = [];
      if (dateFrom && dateTo) {
        tripDateFilter = `AND DATE(tl.created_at) >= $1 AND DATE(tl.created_at) <= $2`;
        tripQueryParams.push(dateFrom, dateTo);
      } else if (dateFrom || dateTo) {
        const filterDate = dateFrom || dateTo;
        tripDateFilter = `AND DATE(tl.created_at) = $1`;
        tripQueryParams.push(filterDate);
      }

      const onRouteTrucksResult = await query(`
        SELECT COUNT(DISTINCT dt.id) as on_route_trucks
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        WHERE tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')
          ${tripDateFilter}
      `, tripQueryParams);
      
      const totalTrucks = parseInt(totalTrucksResult.rows[0].total_trucks);
      const activeTrucks = parseInt(activeTrucksResult.rows[0].active_trucks);
      const assignedTrucks = parseInt(assignedTrucksResult.rows[0].assigned_trucks);
      const onRouteTrucks = parseInt(onRouteTrucksResult.rows[0].on_route_trucks);
      const unassignedTrucks = activeTrucks - assignedTrucks;
      
      // Get unassigned truck details
      const unassignedDetails = await this.getUnassignedTruckDetails();
      

      
      return {
        total_available: activeTrucks, // Use active trucks as available
        assigned_to_active_shifts: assignedTrucks,
        unassigned: unassignedTrucks,
        on_route: onRouteTrucks,
        unassigned_details: unassignedDetails
      };
    } catch (error) {
      console.error('Error getting truck resource summary:', error);
      throw error;
    }
  }
  
  /**
   * Get details of unassigned drivers
   * @returns {Array} Array of unassigned driver objects
   */
  static async getUnassignedDriverDetails() {
    try {
      const result = await query(`
        SELECT 
          d.id as driver_id,
          d.full_name,
          d.employee_id,
          d.status,
          MAX(ds.end_date) as last_shift_date
        FROM drivers d
        LEFT JOIN driver_shifts ds ON d.id = ds.driver_id
        WHERE d.status = 'active'
          AND d.id NOT IN (
            SELECT DISTINCT driver_id 
            FROM driver_shifts 
            WHERE start_date <= CURRENT_DATE 
              AND (end_date >= CURRENT_DATE OR end_date IS NULL)
              AND status = 'active'
          )
        GROUP BY d.id, d.full_name, d.employee_id, d.status
        ORDER BY d.full_name
      `);
      
      return result.rows.map(row => ({
        driver_id: row.driver_id,
        full_name: row.full_name,
        employee_id: row.employee_id,
        status: row.status,
        last_shift: row.last_shift_date
      }));
    } catch (error) {
      console.error('Error getting unassigned driver details:', error);
      throw error;
    }
  }
  
  /**
   * Get details of unassigned trucks
   * @returns {Array} Array of unassigned truck objects
   */
  static async getUnassignedTruckDetails() {
    try {
      const result = await query(`
        SELECT 
          dt.id as truck_id,
          dt.truck_number,
          dt.status,
          dt.license_plate,
          MAX(ds.end_date) as last_assignment_date
        FROM dump_trucks dt
        LEFT JOIN driver_shifts ds ON dt.id = ds.truck_id
        WHERE dt.status = 'active'
          AND dt.id NOT IN (
            SELECT DISTINCT truck_id 
            FROM driver_shifts 
            WHERE start_date <= CURRENT_DATE 
              AND (end_date >= CURRENT_DATE OR end_date IS NULL)
              AND status = 'active'
          )
        GROUP BY dt.id, dt.truck_number, dt.status, dt.license_plate
        ORDER BY dt.truck_number
      `);
      
      return result.rows.map(row => ({
        truck_id: row.truck_id,
        truck_number: row.truck_number,
        status: row.status,
        license_plate: row.license_plate,
        last_assignment: row.last_assignment_date
      }));
    } catch (error) {
      console.error('Error getting unassigned truck details:', error);
      throw error;
    }
  }
  
  /**
   * Get trucks grouped by loading location using truck trip summary approach
   * @param {string} dateFrom - Optional start date filter (YYYY-MM-DD)
   * @param {string} dateTo - Optional end date filter (YYYY-MM-DD)
   * @returns {Array} Array of loading location objects with truck assignments
   */
  static async getLoadingLocationBreakdown(dateFrom = null, dateTo = null) {
    try {
      // Build date filter for loading location breakdown
      let locationDateFilter = '';
      const locationQueryParams = [];
      if (dateFrom && dateTo) {
        locationDateFilter = `AND DATE(tl.created_at) >= $1 AND DATE(tl.created_at) <= $2`;
        locationQueryParams.push(dateFrom, dateTo);
      } else if (dateFrom || dateTo) {
        const filterDate = dateFrom || dateTo;
        locationDateFilter = `AND DATE(tl.created_at) = $1`;
        locationQueryParams.push(filterDate);
      }

      // Get unique dump trucks per loading location (count trucks, not trips)
      const result = await query(`
        SELECT
          COALESCE(ll.name, al.name, 'Unknown') as location_name,
          dt.truck_number,
          dt.id as truck_id,
          COALESCE(tl.performed_by_driver_name, 'Unknown') as driver_name,
          COALESCE(ul.name, au.name, 'Unknown') as unloading_location_name,
          MAX(CASE WHEN tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end') THEN 1 ELSE 0 END) as is_currently_in_transit,
          MAX(tl.created_at) as last_trip_date
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        LEFT JOIN locations ll ON tl.actual_loading_location_id = ll.id
        LEFT JOIN locations ul ON tl.actual_unloading_location_id = ul.id
        LEFT JOIN locations al ON a.loading_location_id = al.id
        LEFT JOIN locations au ON a.unloading_location_id = au.id
        WHERE (tl.actual_loading_location_id IS NOT NULL OR a.loading_location_id IS NOT NULL)
          AND (tl.actual_unloading_location_id IS NOT NULL OR a.unloading_location_id IS NOT NULL)
          AND tl.status IN ('trip_completed', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'exception_triggered', 'exception_pending', 'cancelled', 'stopped')
          ${locationDateFilter}
        GROUP BY
          COALESCE(ll.name, al.name, 'Unknown'),
          dt.truck_number,
          dt.id,
          COALESCE(tl.performed_by_driver_name, 'Unknown'),
          COALESCE(ul.name, au.name, 'Unknown')
        ORDER BY location_name, dt.truck_number
      `, locationQueryParams);
      
      // Group results by loading location and count unique trucks
      const locationMap = new Map();
      
      result.rows.forEach(row => {
        const locationName = row.location_name;
        
        if (!locationMap.has(locationName)) {
          locationMap.set(locationName, {
            location_name: locationName,
            assigned_trucks: [],
            total_assigned: 0,
            currently_at_location: 0,
            on_route: 0,
            unique_truck_ids: new Set()
          });
        }
        
        const location = locationMap.get(locationName);
        
        // Only count each unique truck once per location
        if (!location.unique_truck_ids.has(row.truck_id)) {
          location.unique_truck_ids.add(row.truck_id);
          
          const isCurrentlyInTransit = parseInt(row.is_currently_in_transit);
          const isOnRoute = isCurrentlyInTransit > 0;
          
          const truckData = {
            truck_id: row.truck_id,
            truck_number: row.truck_number,
            status: isOnRoute ? 'on_route' : 'at_location',
            driver_name: row.driver_name,
            current_trip: isOnRoute ? {
              unloading_location: row.unloading_location_name,
              trip_start: row.last_trip_date
            } : null
          };
          
          location.assigned_trucks.push(truckData);
          location.total_assigned++;
          
          if (isOnRoute) {
            location.on_route++;
          } else {
            location.currently_at_location++;
          }
        }
      });
      
      // Remove the unique_truck_ids set from the final result
      return Array.from(locationMap.values()).map(location => {
        const { unique_truck_ids, ...locationData } = location;
        return locationData;
      });
    } catch (error) {
      console.error('Error getting loading location breakdown:', error);
      throw error;
    }
  }
  
  /**
   * Get trucks currently on routes using the exact same logic as truck trip summary
   * @returns {Array} Array of trucks currently in transit
   */
  static async getTrucksOnRoute() {
    try {
      // Get trucks with active trips using the exact same logic as truck trip summary
      // Include all active trip statuses: loading_start, loading_end, unloading_start, unloading_end
      const result = await query(`
        SELECT
          dt.truck_number,
          COALESCE(tl.performed_by_driver_name, 'Unknown') as driver_name,
          COALESCE(ll.name, al.name, 'Unknown') as loading_location_name,
          COALESCE(ul.name, au.name, 'Unknown') as unloading_location_name,
          tl.created_at as trip_start,
          tl.status as trip_status,
          CASE
            WHEN tl.status = 'loading_start' THEN 'at_loading_location'
            WHEN tl.status = 'loading_end' THEN 'traveling_to_unloading'
            WHEN tl.status = 'unloading_start' THEN 'at_unloading_location'
            WHEN tl.status = 'unloading_end' THEN 'traveling_to_loading'
          END as travel_direction
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        -- Use actual locations from trip_logs for accurate route data
        LEFT JOIN locations ll ON tl.actual_loading_location_id = ll.id
        LEFT JOIN locations ul ON tl.actual_unloading_location_id = ul.id
        -- Fallback to assignment locations if actual locations not recorded
        LEFT JOIN locations al ON a.loading_location_id = al.id
        LEFT JOIN locations au ON a.unloading_location_id = au.id
        WHERE tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')
        ORDER BY tl.created_at DESC
      `);
      
      return result.rows.map(row => ({
        truck_number: row.truck_number,
        driver_name: row.driver_name,
        loading_location: row.loading_location_name,
        unloading_location: row.unloading_location_name,
        trip_start: row.trip_start,
        trip_status: row.trip_status,
        travel_direction: row.travel_direction
      }));
    } catch (error) {
      console.error('Error getting trucks on route:', error);
      throw error;
    }
  }
  
  /**
   * Get historical utilization data for trends
   * @param {number} days - Number of days to look back
   * @returns {Object} Historical utilization data
   */
  static async getUtilizationHistory(days = 7) {
    try {
      const result = await query(`
        WITH date_series AS (
          SELECT generate_series(
            CURRENT_DATE - INTERVAL '${days} days',
            CURRENT_DATE,
            INTERVAL '1 day'
          )::date AS date
        ),
        daily_driver_utilization AS (
          SELECT 
            ds.date,
            COUNT(DISTINCT ds.driver_id) as drivers_assigned,
            (SELECT COUNT(*) FROM drivers WHERE status = 'active') as total_drivers
          FROM date_series ds
          LEFT JOIN driver_shifts dsh ON (
            dsh.start_date <= ds.date 
            AND (dsh.end_date >= ds.date OR dsh.end_date IS NULL)
            AND dsh.status = 'active'
          )
          GROUP BY ds.date
        ),
        daily_truck_utilization AS (
          SELECT 
            ds.date,
            COUNT(DISTINCT ds.truck_id) as trucks_assigned,
            (SELECT COUNT(*) FROM dump_trucks WHERE status = 'active') as total_trucks
          FROM date_series ds
          LEFT JOIN driver_shifts dsh ON (
            dsh.start_date <= ds.date 
            AND (dsh.end_date >= ds.date OR dsh.end_date IS NULL)
            AND dsh.status = 'active'
          )
          GROUP BY ds.date
        )
        SELECT 
          ds.date,
          COALESCE(ddu.drivers_assigned, 0) as drivers_assigned,
          COALESCE(ddu.total_drivers, 0) as total_drivers,
          COALESCE(dtu.trucks_assigned, 0) as trucks_assigned,
          COALESCE(dtu.total_trucks, 0) as total_trucks,
          CASE 
            WHEN COALESCE(ddu.total_drivers, 0) > 0 
            THEN ROUND((COALESCE(ddu.drivers_assigned, 0)::decimal / ddu.total_drivers) * 100, 2)
            ELSE 0 
          END as driver_utilization_percent,
          CASE 
            WHEN COALESCE(dtu.total_trucks, 0) > 0 
            THEN ROUND((COALESCE(dtu.trucks_assigned, 0)::decimal / dtu.total_trucks) * 100, 2)
            ELSE 0 
          END as truck_utilization_percent
        FROM date_series ds
        LEFT JOIN daily_driver_utilization ddu ON ds.date = ddu.date
        LEFT JOIN daily_truck_utilization dtu ON ds.date = dtu.date
        ORDER BY ds.date
      `);
      
      const driverUtilization = result.rows.map(row => ({
        date: row.date,
        assigned: parseInt(row.drivers_assigned),
        total: parseInt(row.total_drivers),
        utilization_percent: parseFloat(row.driver_utilization_percent)
      }));
      
      const truckUtilization = result.rows.map(row => ({
        date: row.date,
        assigned: parseInt(row.trucks_assigned),
        total: parseInt(row.total_trucks),
        utilization_percent: parseFloat(row.truck_utilization_percent)
      }));
      
      return {
        period_days: days,
        driver_utilization: driverUtilization,
        truck_utilization: truckUtilization
      };
    } catch (error) {
      console.error('Error getting utilization history:', error);
      throw error;
    }
  }

  /**
   * Calculate alert levels based on resource data
   * @param {Object} driverData - Driver resource summary
   * @param {Object} truckData - Truck resource summary
   * @returns {Object} Alert summary
   */
  static calculateAlerts(driverData, truckData) {
    const driverShortage = driverData.unassigned < (driverData.total_available * 0.1);
    const truckShortage = truckData.unassigned < (truckData.total_available * 0.1);
    
    return {
      driver_shortage: driverShortage,
      truck_shortage: truckShortage,
      underutilization: {
        unassigned_drivers: driverData.unassigned,
        unassigned_trucks: truckData.unassigned
      }
    };
  }
}

module.exports = FleetResourceQueries;