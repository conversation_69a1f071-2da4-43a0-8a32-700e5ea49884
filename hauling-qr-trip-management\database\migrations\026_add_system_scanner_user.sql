-- Migration: Add system scanner user for public scans
-- This fixes the "invalid input syntax for type integer: 'public-scanner'" error

-- Insert system user for public scanner operations
INSERT INTO users (
    id,
    username,
    email,
    password_hash,
    full_name,
    role,
    status,
    created_at,
    updated_at
) VALUES (
    -1,
    'trip-scanner',
    '<EMAIL>',
    '$2b$10$dummy.hash.for.system.user.no.login.allowed',
    'System Trip Scanner',
    'supervisor',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (id) DO UPDATE SET
    username = EXCLUDED.username,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    updated_at = CURRENT_TIMESTAMP;

-- Ensure the users sequence doesn't conflict with negative system IDs
SELECT setval('users_id_seq', GREATEST(1, (SELECT COALESCE(MAX(id), 0) FROM users WHERE id > 0)));