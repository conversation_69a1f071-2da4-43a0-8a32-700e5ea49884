import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { getApiBaseUrl } from '../../../../utils/network-utils';
import LiveDashboard from './LiveDashboard';
import RouteVisualization from './RouteVisualization';
import AlertSystem from './AlertSystem';

const LiveOperations = ({ lastUpdated, isConnected, loading }) => {
  const [operationsData, setOperationsData] = useState(null);
  const [dataLoading, setDataLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Load live operations data
  const loadLiveOperationsData = async () => {
    try {
      setDataLoading(true);
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/analytics/live-operations`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch live operations data');
      }
      
      const result = await response.json();
      setOperationsData(result.data);
      
    } catch (error) {
      console.error('Error loading live operations data:', error);
      toast.error('Failed to load live operations data');
    } finally {
      setDataLoading(false);
    }
  };

  // Load data on component mount and when lastUpdated changes
  useEffect(() => {
    loadLiveOperationsData();
  }, [lastUpdated]);

  // Auto-refresh every 15 seconds when enabled
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadLiveOperationsData();
    }, 15000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  // Show loading state
  if (dataLoading && !operationsData) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="bg-secondary-200 h-16 rounded-lg"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-secondary-200 h-32 rounded-lg"></div>
          ))}
        </div>
        <div className="bg-secondary-200 h-96 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header Controls */}
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center mb-4 sm:mb-0">
            <h2 className="text-lg font-medium text-secondary-900 mr-4">
              🔴 Live Operations Monitor
            </h2>
            <div className={`flex items-center text-sm ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              {isConnected ? 'Live Updates Active' : 'Connection Lost'}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-secondary-700">Auto-refresh (15s)</span>
            </label>
            
            <button
              onClick={loadLiveOperationsData}
              disabled={dataLoading}
              className="inline-flex items-center px-3 py-2 border border-secondary-300 shadow-sm text-sm leading-4 font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              <span className={`mr-2 ${dataLoading ? 'animate-spin' : ''}`}>🔄</span>
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Connection Status Alert */}
      {!isConnected && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400 text-xl">🚨</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Real-time monitoring unavailable
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>Live operations data may be outdated. Check your connection.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Operations Summary */}
      {operationsData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-50 text-blue-600">
                <span className="text-2xl">🚛</span>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-secondary-900">
                  {operationsData.summary?.totalActive || 0}
                </div>
                <div className="text-sm text-secondary-500">Active Trucks</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-50 text-green-600">
                <span className="text-2xl">⚡</span>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-secondary-900">
                  {(operationsData.summary?.byPhase?.loading || 0) + 
                   (operationsData.summary?.byPhase?.unloading || 0)}
                </div>
                <div className="text-sm text-secondary-500">In Operation</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-red-50 text-red-600">
                <span className="text-2xl">⚠️</span>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-secondary-900">
                  {(operationsData.summary?.alerts?.stopped || 0) +
                   (operationsData.summary?.alerts?.overdue || 0)}
                </div>
                <div className="text-sm text-secondary-500">Active Alerts</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Alert System */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          🚨 Active Alerts
        </h2>
        <AlertSystem 
          data={operationsData} 
          loading={dataLoading || loading}
        />
      </div>

      {/* Live Dashboard */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          📊 Live Operations Dashboard
        </h2>
        <LiveDashboard 
          data={operationsData} 
          loading={dataLoading || loading}
        />
      </div>

      {/* Route Visualization */}
      <div>
        <h2 className="text-lg font-medium text-secondary-900 mb-4">
          🗺️ Active Route Visualization
        </h2>
        <RouteVisualization 
          data={operationsData} 
          loading={dataLoading || loading}
        />
      </div>
    </div>
  );
};

export default LiveOperations;
