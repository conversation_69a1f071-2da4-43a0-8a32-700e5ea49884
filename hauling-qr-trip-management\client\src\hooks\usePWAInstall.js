import { useState, useEffect } from 'react';

/**
 * Custom hook for PWA installation functionality
 * Handles beforeinstallprompt event and provides installation methods
 */
export const usePWAInstall = () => {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkInstallStatus = () => {
      // Check if running in standalone mode (installed PWA)
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      // Check if running in PWA mode on iOS
      const isIOSPWA = window.navigator.standalone === true;
      // Check if running in TWA (Trusted Web Activity) on Android
      const isTWA = document.referrer.includes('android-app://');
      
      setIsInstalled(isStandalone || isIOSPWA || isTWA);
    };

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e) => {
      console.log('[PWA Install] beforeinstallprompt event fired');
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      // Save the event so it can be triggered later
      setDeferredPrompt(e);
      setIsInstallable(true);
    };

    // Listen for appinstalled event
    const handleAppInstalled = () => {
      console.log('[PWA Install] App was installed');
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
    };

    // Add event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Check initial install status
    checkInstallStatus();

    // Cleanup event listeners
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  /**
   * Trigger PWA installation
   * @param {string} source - Source of installation trigger (e.g., 'login-page', 'trip-scanner')
   * @returns {Promise<boolean>} - Success status
   */
  const installPWA = async (source = 'unknown') => {
    if (!deferredPrompt) {
      console.warn('[PWA Install] No deferred prompt available');
      return false;
    }

    setIsInstalling(true);

    try {
      console.log(`[PWA Install] Triggering installation from: ${source}`);
      
      // Show the install prompt
      deferredPrompt.prompt();
      
      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice;
      
      console.log(`[PWA Install] User choice: ${outcome}`);
      
      if (outcome === 'accepted') {
        console.log('[PWA Install] User accepted the install prompt');
        setIsInstallable(false);
        setDeferredPrompt(null);
        return true;
      } else {
        console.log('[PWA Install] User dismissed the install prompt');
        return false;
      }
    } catch (error) {
      console.error('[PWA Install] Error during installation:', error);
      return false;
    } finally {
      setIsInstalling(false);
    }
  };

  /**
   * Get installation instructions for different platforms
   * @returns {Object} - Platform-specific instructions
   */
  const getInstallInstructions = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const isIOS = /iphone|ipad|ipod/.test(userAgent);
    const isAndroid = /android/.test(userAgent);
    const isChrome = /chrome/.test(userAgent) && !/edg/.test(userAgent);
    const isFirefox = /firefox/.test(userAgent);
    const isSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent);

    if (isIOS && isSafari) {
      return {
        platform: 'iOS Safari',
        steps: [
          'Tap the Share button (square with arrow)',
          'Scroll down and tap "Add to Home Screen"',
          'Tap "Add" to install the app'
        ]
      };
    } else if (isAndroid && isChrome) {
      return {
        platform: 'Android Chrome',
        steps: [
          'Tap the menu (three dots)',
          'Tap "Add to Home screen"',
          'Tap "Add" to install the app'
        ]
      };
    } else if (isChrome) {
      return {
        platform: 'Desktop Chrome',
        steps: [
          'Click the install icon in the address bar',
          'Or use the menu → "Install Hauling QR System"',
          'Click "Install" to add to your desktop'
        ]
      };
    } else if (isFirefox) {
      return {
        platform: 'Firefox',
        steps: [
          'Click the menu (three lines)',
          'Click "Install this site as an app"',
          'Click "Install" to add to your system'
        ]
      };
    } else {
      return {
        platform: 'Browser',
        steps: [
          'Look for an install option in your browser menu',
          'Or bookmark this page for quick access',
          'Some browsers may not support PWA installation'
        ]
      };
    }
  };

  return {
    isInstallable,
    isInstalled,
    isInstalling,
    installPWA,
    getInstallInstructions,
    canInstall: isInstallable && !isInstalled
  };
};

export default usePWAInstall;
