/**
 * Environment Loader Module for Hauling QR Trip System
 * Handles automatic environment detection and configuration loading
 * Supports dual environment system (.env.dev and .env.prod)
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * Detect the current deployment environment
 * @returns {string} 'development' | 'production'
 */
function detectEnvironment() {
  // Priority 1: Command-line flag
  const args = process.argv;
  const envFlag = args.find(arg => arg.startsWith('--environment='));
  if (envFlag) {
    const env = envFlag.split('=')[1];
    if (env === 'development' || env === 'production') {
      console.log(`🔍 Environment detected via command-line flag: ${env}`);
      return env;
    }
  }

  // Priority 2: Environment variable
  if (process.env.DEPLOYMENT_ENV) {
    const env = process.env.DEPLOYMENT_ENV;
    if (env === 'development' || env === 'production') {
      console.log(`🔍 Environment detected via DEPLOYMENT_ENV: ${env}`);
      return env;
    }
  }

  // Priority 3: Hostname pattern matching
  const hostname = os.hostname().toLowerCase();
  
  // WSL Ubuntu patterns
  if (hostname.includes('wsl') || hostname.includes('ubuntu') || process.env.WSL_DISTRO_NAME) {
    console.log(`🔍 Environment detected via hostname (WSL): development`);
    return 'development';
  }

  // VPS patterns
  if (hostname.includes('vps') || hostname.includes('truckhaul') || hostname.includes('hauling')) {
    console.log(`🔍 Environment detected via hostname (VPS): production`);
    return 'production';
  }

  // Priority 4: IP address range detection
  const networkInterfaces = os.networkInterfaces();
  let hasWSLIP = false;
  let hasVPSIP = false;

  for (const interfaceName in networkInterfaces) {
    const addresses = networkInterfaces[interfaceName];
    for (const addr of addresses) {
      if (addr.family === 'IPv4' && !addr.internal) {
        // WSL IP range (172.x.x.x)
        if (addr.address.startsWith('172.')) {
          hasWSLIP = true;
        }
        // VPS IP range (158.69.x.x for OVH)
        if (addr.address.startsWith('158.69.')) {
          hasVPSIP = true;
        }
      }
    }
  }

  if (hasWSLIP && !hasVPSIP) {
    console.log(`🔍 Environment detected via IP range (WSL): development`);
    return 'development';
  }

  if (hasVPSIP) {
    console.log(`🔍 Environment detected via IP range (VPS): production`);
    return 'production';
  }

  // Priority 5: Check for production indicators
  if (process.env.NODE_ENV === 'production') {
    console.log(`🔍 Environment detected via NODE_ENV: production`);
    return 'production';
  }

  // Default to development
  console.log(`🔍 Environment defaulted to: development`);
  return 'development';
}

/**
 * Load environment configuration based on detected environment
 * @param {string} environment - 'development' | 'production'
 * @returns {boolean} Success status
 */
function loadEnvironmentConfig(environment = null) {
  const rootDir = path.join(__dirname, '..');
  const targetEnvFile = path.join(rootDir, '.env');
  
  // Detect environment if not provided
  if (!environment) {
    environment = detectEnvironment();
  }

  // Determine source environment file
  const sourceEnvFile = environment === 'production' 
    ? path.join(rootDir, '.env.prod')
    : path.join(rootDir, '.env.dev');

  console.log(`⚙️ Loading environment configuration for: ${environment}`);
  console.log(`📁 Source file: ${path.basename(sourceEnvFile)}`);
  console.log(`📁 Target file: ${path.basename(targetEnvFile)}`);

  try {
    // Check if source file exists
    if (!fs.existsSync(sourceEnvFile)) {
      console.error(`❌ Source environment file not found: ${sourceEnvFile}`);
      return false;
    }

    // Note: Backup creation removed to prevent file clutter in production

    // Copy source environment file to .env
    fs.copyFileSync(sourceEnvFile, targetEnvFile);
    console.log(`✅ Environment configuration loaded successfully`);

    // Log configuration summary
    const envContent = fs.readFileSync(targetEnvFile, 'utf8');
    const nodeEnv = envContent.match(/NODE_ENV=(.+)/)?.[1] || 'unknown';
    const httpsEnabled = envContent.match(/ENABLE_HTTPS=(.+)/)?.[1] || 'unknown';
    const backendPort = envContent.match(/BACKEND_HTTP_PORT=(.+)/)?.[1] || 'unknown';
    
    console.log(`📊 Configuration Summary:`);
    console.log(`   • NODE_ENV: ${nodeEnv}`);
    console.log(`   • HTTPS_ENABLED: ${httpsEnabled}`);
    console.log(`   • BACKEND_PORT: ${backendPort}`);

    return true;
  } catch (error) {
    console.error(`❌ Failed to load environment configuration: ${error.message}`);
    return false;
  }
}

/**
 * Validate that all required environment variables exist
 * @param {string} environment - 'development' | 'production'
 * @returns {boolean} Validation status
 */
function validateConfiguration(environment = null) {
  if (!environment) {
    environment = detectEnvironment();
  }

  console.log(`🔍 Validating configuration for: ${environment}`);

  // Load environment variables
  require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

  const requiredVars = [
    'NODE_ENV',
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'BACKEND_HTTP_PORT',
    'FRONTEND_PORT',
    'JWT_SECRET'
  ];

  const productionVars = [
    'PRODUCTION_DOMAIN',
    'API_BASE_URL',
    'FRONTEND_URL'
  ];

  const developmentVars = [
    'AUTO_DETECT_IP'
  ];

  let missingVars = [];

  // Check required variables
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  }

  // Check environment-specific variables
  if (environment === 'production') {
    for (const varName of productionVars) {
      if (!process.env[varName]) {
        missingVars.push(varName);
      }
    }
  } else {
    for (const varName of developmentVars) {
      if (!process.env[varName]) {
        missingVars.push(varName);
      }
    }
  }

  if (missingVars.length > 0) {
    console.error(`❌ Missing required environment variables:`);
    missingVars.forEach(varName => {
      console.error(`   • ${varName}`);
    });
    return false;
  }

  // Validate port configuration for production
  if (environment === 'production') {
    const backendPort = parseInt(process.env.BACKEND_HTTP_PORT);
    const httpsEnabled = process.env.ENABLE_HTTPS === 'true';

    if (backendPort !== 8080) {
      console.error(`❌ Production backend must use port 8080 (Cloudflare compatible), found: ${backendPort}`);
      return false;
    }

    if (httpsEnabled) {
      console.error(`❌ Production HTTPS must be disabled (Cloudflare handles SSL termination)`);
      return false;
    }

    console.log(`✅ Production configuration validated: port 8080, HTTPS disabled`);
  }

  console.log(`✅ Configuration validation passed for: ${environment}`);
  return true;
}

/**
 * Get detailed environment information
 * @returns {object} Environment information
 */
function getEnvironmentInfo() {
  const environment = detectEnvironment();
  
  // Load current environment variables
  require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

  return {
    detectedEnvironment: environment,
    nodeEnv: process.env.NODE_ENV || 'unknown',
    httpsEnabled: process.env.ENABLE_HTTPS === 'true',
    autoDetectIP: process.env.AUTO_DETECT_IP !== 'false',
    backendPort: parseInt(process.env.BACKEND_HTTP_PORT) || 'unknown',
    frontendPort: parseInt(process.env.FRONTEND_PORT) || 'unknown',
    databaseName: process.env.DB_NAME || 'unknown',
    databaseUser: process.env.DB_USER || 'unknown',
    productionDomain: process.env.PRODUCTION_DOMAIN || 'unknown',
    hostname: os.hostname(),
    platform: os.platform(),
    networkInterfaces: Object.keys(os.networkInterfaces())
  };
}

module.exports = {
  detectEnvironment,
  loadEnvironmentConfig,
  validateConfiguration,
  getEnvironmentInfo
};
