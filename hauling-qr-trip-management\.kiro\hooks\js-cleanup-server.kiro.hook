{"enabled": true, "name": "Server JS Function Cleanup", "description": "Analyzes all JavaScript files in the server/ directory to identify and remove unused functions while preserving route handlers, middleware, database functions, and core system functionality for the Hauling QR Trip System", "version": "1", "when": {"type": "userTriggered", "patterns": ["server/**/*.js"]}, "then": {"type": "askAgent", "prompt": "Analyze all JavaScript files in the `server/` directory and remove unused functions while preserving code functionality. Given the context of our Hauling QR Trip System with its complex shift management, assignment synchronization, and API endpoints, perform the following systematic cleanup:\n\n**Scope and Files to Analyze:**\n1. Analyze ALL `.js` files in the `server/` directory recursively, including:\n   - `server/routes/*.js` (API route handlers)\n   - `server/utils/*.js` (utility functions and helpers)\n   - `server/middleware/*.js` (authentication and middleware)\n   - `server/models/*.js` (database models if present)\n   - `server/server.js` (main server file)\n\n**Function Identification Process:**\n1. Identify ALL function types:\n   - Function declarations: `function functionName() {}`\n   - Function expressions: `const functionName = function() {}`\n   - Arrow functions: `const functionName = () => {}`\n   - Method definitions in objects/classes: `methodName() {}`\n   - Async functions: `async function functionName() {}`\n\n**Usage Analysis - Check if each function is:**\n1. **Exported** via any method:\n   - `module.exports = functionName`\n   - `module.exports.functionName = functionName`\n   - `exports.functionName = functionName`\n   - ES6 exports: `export function functionName()` or `export { functionName }`\n2. **Called/invoked** within the same file:\n   - Direct calls: `functionName()`\n   - Method calls: `object.functionName()`\n   - Callback usage: `array.map(functionName)`\n3. **Referenced by name** (indirect usage):\n   - Assigned to variables: `const handler = functionName`\n   - Passed as parameters: `router.get('/path', functionName)`\n   - Used in object literals: `{ handler: functionName }`\n   - Referenced in template literals or string concatenation\n\n**Critical Preservation Rules:**\n1. **DO NOT REMOVE** functions that are:\n   - Express.js route handlers (even if only referenced in router definitions)\n   - Middleware functions (authentication, validation, error handling)\n   - Database query functions used by routes\n   - Utility functions supporting shift synchronization or assignment management\n   - Event handlers or callback functions\n   - Functions used in our SimpleShiftSyncMonitor or related monitoring systems\n2. **PRESERVE** any function that supports:\n   - Shift management operations (cancel, complete, create)\n   - Assignment Management synchronization\n   - Trip workflow (4-phase: assigned → loading_start → loading_end → unloading_start → unloading_end → trip_completed)\n   - Authentication and authorization\n   - Database connections and queries\n\n**Removal Criteria - Remove ONLY functions that meet ALL conditions:**\n1. Not exported from the module\n2. Not called or referenced anywhere within the file\n3. Not used as route handlers, middleware, or callbacks\n4. Not supporting any core system functionality\n5. Confirmed to be completely isolated with no dependencies\n\n**Verification Requirements:**\n1. **Cross-file analysis**: Check if functions are imported/required by other files\n2. **Dynamic usage patterns**: Look for `eval()`, `this[functionName]()`, or string-based function calls\n3. **Configuration objects**: Check if functions are referenced in config objects or arrays\n4. **Route definitions**: Verify route handlers are not accidentally removed\n5. **Test the system**: Ensure shift management, assignment synchronization, and monitoring systems remain functional\n\n**Implementation Process:**\n1. Create a comprehensive inventory of all functions across server files\n2. Map dependencies and usage patterns between files\n3. Identify truly unused functions with 100% certainty\n4. Remove unused functions while maintaining code structure and formatting\n5. Verify no breaking changes to existing functionality\n6. Test critical workflows: shift operations, assignment sync, and monitoring\n\n**Output Requirements:**\n- Provide a detailed report of functions analyzed and removed\n- List any functions that were borderline cases and why they were preserved\n- Confirm that all core system functionality remains intact\n- Maintain proper indentation, comments, and code structure"}}