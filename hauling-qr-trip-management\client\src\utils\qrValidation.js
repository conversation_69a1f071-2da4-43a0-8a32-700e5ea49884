/**
 * Validates QR code data structure and content
 * @param {string} data - Raw QR code data
 * @returns {Object} Parsed and validated QR data
 * @throws {Error} If validation fails
 */
export const validateQRData = (data) => {
  // Input sanitization
  if (typeof data !== 'string' || data.length > 1000) {
    throw new Error('Invalid QR code data format');
  }

  let qrData;
  try {
    qrData = JSON.parse(data);
  } catch (parseError) {
    throw new Error('Invalid QR code format - not valid JSON');
  }

  // Validate required fields
  if (!qrData || typeof qrData !== 'object') {
    throw new Error('QR code data must be an object');
  }

  if (!qrData.type || typeof qrData.type !== 'string') {
    throw new Error('QR code missing or invalid type field');
  }

  if (!qrData.id || typeof qrData.id !== 'string') {
    throw new Error('QR code missing or invalid id field');
  }

  // Validate allowed types
  const allowedTypes = ['driver', 'truck'];
  if (!allowedTypes.includes(qrData.type)) {
    throw new Error(`Invalid QR code type: ${qrData.type}. Expected: ${allowedTypes.join(', ')}`);
  }

  // Additional validation based on type
  if (qrData.type === 'driver' && !qrData.employee_id) {
    throw new Error('Driver QR code missing employee_id field');
  }

  // Note: Truck QR codes use 'id' field to store truck_number, not 'truck_number' field
  // This is consistent with the backend services and QR generation logic
  if (qrData.type === 'truck' && !qrData.id) {
    throw new Error('Truck QR code missing id field');
  }

  return qrData;
};