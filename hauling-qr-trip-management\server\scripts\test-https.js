#!/usr/bin/env node

/**
 * HTTPS Testing and Validation Script
 * Tests SSL/HTTPS configuration for the Hauling QR Trip System
 */

const https = require('https');
const http = require('http');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  httpPort: process.env.PORT || 5000,
  httpsPort: process.env.HTTPS_PORT || 5443,
  hostname: process.env.LOCAL_NETWORK_IP || '**************',
  timeout: 10000
};

console.log('🧪 HTTPS Testing and Validation for Hauling QR Trip System');
console.log('=' .repeat(60));

/**
 * Test HTTP Health Check
 */
async function testHttpHealth() {
  console.log('\n📊 Testing HTTP Health Check...');
  
  return new Promise((resolve) => {
    const options = {
      hostname: CONFIG.hostname,
      port: CONFIG.httpPort,
      path: '/health',
      method: 'GET',
      timeout: CONFIG.timeout
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ HTTP Health Check: PASSED');
          console.log(`   Status: ${res.statusCode}`);
          console.log(`   URL: http://${CONFIG.hostname}:${CONFIG.httpPort}/health`);
        } else {
          console.log('❌ HTTP Health Check: FAILED');
          console.log(`   Status: ${res.statusCode}`);
        }
        resolve(res.statusCode === 200);
      });
    });
    
    req.on('error', (error) => {
      console.log('❌ HTTP Health Check: ERROR');
      console.log(`   Error: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ HTTP Health Check: TIMEOUT');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * Test HTTPS Health Check
 */
async function testHttpsHealth() {
  console.log('\n🔐 Testing HTTPS Health Check...');
  
  return new Promise((resolve) => {
    const options = {
      hostname: CONFIG.hostname,
      port: CONFIG.httpsPort,
      path: '/health',
      method: 'GET',
      rejectUnauthorized: false, // Allow self-signed certificates
      timeout: CONFIG.timeout
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ HTTPS Health Check: PASSED');
          console.log(`   Status: ${res.statusCode}`);
          console.log(`   URL: https://${CONFIG.hostname}:${CONFIG.httpsPort}/health`);
          const cipher = res.socket && res.socket.getCipher ? res.socket.getCipher() : null;
          const protocol = res.socket && res.socket.getProtocol ? res.socket.getProtocol() : null;
          console.log(`   Cipher: ${cipher?.name || 'Unknown'}`);
          console.log(`   Protocol: ${protocol || 'Unknown'}`);
        } else {
          console.log('❌ HTTPS Health Check: FAILED');
          console.log(`   Status: ${res.statusCode}`);
        }
        resolve(res.statusCode === 200);
      });
    });
    
    req.on('error', (error) => {
      console.log('❌ HTTPS Health Check: ERROR');
      console.log(`   Error: ${error.message}`);
      if (error.code === 'ECONNREFUSED') {
        console.log('   Hint: Make sure HTTPS server is running');
      } else if (error.code === 'CERT_UNTRUSTED') {
        console.log('   Hint: Self-signed certificate - this is expected in development');
      }
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ HTTPS Health Check: TIMEOUT');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * Test WebSocket Connection (WS)
 */
async function testWebSocketHttp() {
  console.log('\n🔌 Testing WebSocket (WS) Connection...');
  
  return new Promise((resolve) => {
    const wsUrl = `ws://${CONFIG.hostname}:${CONFIG.httpPort}`;
    
    try {
      const ws = new WebSocket(wsUrl);
      
      const timeout = setTimeout(() => {
        console.log('❌ WebSocket (WS): TIMEOUT');
        ws.close();
        resolve(false);
      }, CONFIG.timeout);
      
      ws.on('open', () => {
        clearTimeout(timeout);
        console.log('✅ WebSocket (WS): CONNECTED');
        console.log(`   URL: ${wsUrl}`);
        
        // Test authentication
        ws.send(JSON.stringify({
          type: 'auth',
          userId: 'test-user',
          role: 'admin'
        }));
        
        setTimeout(() => {
          ws.close();
          resolve(true);
        }, 1000);
      });
      
      ws.on('error', (error) => {
        clearTimeout(timeout);
        console.log('❌ WebSocket (WS): ERROR');
        console.log(`   Error: ${error.message}`);
        resolve(false);
      });
      
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          if (message.type === 'auth_success') {
            console.log('✅ WebSocket Authentication: PASSED');
          }
        } catch (e) {
          // Ignore parse errors
        }
      });
      
    } catch (error) {
      console.log('❌ WebSocket (WS): ERROR');
      console.log(`   Error: ${error.message}`);
      resolve(false);
    }
  });
}

/**
 * Test Secure WebSocket Connection (WSS)
 */
async function testWebSocketHttps() {
  console.log('\n🔐 Testing Secure WebSocket (WSS) Connection...');
  
  return new Promise((resolve) => {
    const wsUrl = `wss://${CONFIG.hostname}:${CONFIG.httpsPort}`;
    
    try {
      const ws = new WebSocket(wsUrl, {
        rejectUnauthorized: false // Allow self-signed certificates
      });
      
      const timeout = setTimeout(() => {
        console.log('❌ WebSocket (WSS): TIMEOUT');
        ws.close();
        resolve(false);
      }, CONFIG.timeout);
      
      ws.on('open', () => {
        clearTimeout(timeout);
        console.log('✅ WebSocket (WSS): CONNECTED');
        console.log(`   URL: ${wsUrl}`);
        
        // Test authentication
        ws.send(JSON.stringify({
          type: 'auth',
          userId: 'test-user',
          role: 'admin'
        }));
        
        setTimeout(() => {
          ws.close();
          resolve(true);
        }, 1000);
      });
      
      ws.on('error', (error) => {
        clearTimeout(timeout);
        console.log('❌ WebSocket (WSS): ERROR');
        console.log(`   Error: ${error.message}`);
        if (error.code === 'CERT_UNTRUSTED') {
          console.log('   Hint: Self-signed certificate - this is expected in development');
        }
        resolve(false);
      });
      
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          if (message.type === 'auth_success') {
            console.log('✅ WebSocket Authentication: PASSED');
          }
        } catch (e) {
          // Ignore parse errors
        }
      });
      
    } catch (error) {
      console.log('❌ WebSocket (WSS): ERROR');
      console.log(`   Error: ${error.message}`);
      resolve(false);
    }
  });
}

/**
 * Test SSL Certificate
 */
async function testSSLCertificate() {
  console.log('\n📜 Testing SSL Certificate...');
  
  return new Promise((resolve) => {
    const options = {
      hostname: CONFIG.hostname,
      port: CONFIG.httpsPort,
      method: 'GET',
      path: '/health',
      rejectUnauthorized: false
    };
    
    const req = https.request(options, (res) => {
      const cert = res.socket.getPeerCertificate();
      
      if (cert && Object.keys(cert).length > 0) {
        console.log('✅ SSL Certificate: FOUND');
        console.log(`   Subject: ${cert.subject?.CN || 'Unknown'}`);
        console.log(`   Issuer: ${cert.issuer?.CN || 'Unknown'}`);
        console.log(`   Valid From: ${cert.valid_from || 'Unknown'}`);
        console.log(`   Valid To: ${cert.valid_to || 'Unknown'}`);
        console.log(`   Fingerprint: ${cert.fingerprint || 'Unknown'}`);
        
        // Check if certificate is expired
        const now = new Date();
        const validTo = new Date(cert.valid_to);
        
        if (validTo < now) {
          console.log('⚠️  Certificate is EXPIRED');
        } else {
          const daysUntilExpiry = Math.ceil((validTo - now) / (1000 * 60 * 60 * 24));
          console.log(`   Days until expiry: ${daysUntilExpiry}`);
        }
        
        resolve(true);
      } else {
        console.log('❌ SSL Certificate: NOT FOUND');
        resolve(false);
      }
      
      res.on('data', () => {}); // Consume response
      res.on('end', () => {});
    });
    
    req.on('error', (error) => {
      console.log('❌ SSL Certificate: ERROR');
      console.log(`   Error: ${error.message}`);
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * Main test runner
 */
async function runTests() {
  console.log(`Testing configuration:`);
  console.log(`  HTTP Port: ${CONFIG.httpPort}`);
  console.log(`  HTTPS Port: ${CONFIG.httpsPort}`);
  console.log(`  Hostname: ${CONFIG.hostname}`);
  console.log(`  Timeout: ${CONFIG.timeout}ms`);
  
  const results = {
    httpHealth: await testHttpHealth(),
    httpsHealth: await testHttpsHealth(),
    sslCertificate: await testSSLCertificate(),
    webSocketHttp: await testWebSocketHttp(),
    webSocketHttps: await testWebSocketHttps()
  };
  
  console.log('\n📋 Test Results Summary:');
  console.log('=' .repeat(40));
  console.log(`HTTP Health Check:     ${results.httpHealth ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`HTTPS Health Check:    ${results.httpsHealth ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`SSL Certificate:       ${results.sslCertificate ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`WebSocket (WS):        ${results.webSocketHttp ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`WebSocket (WSS):       ${results.webSocketHttps ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log('\n🎯 Overall Result:');
  console.log(`${passCount}/${totalCount} tests passed`);
  
  if (passCount === totalCount) {
    console.log('🎉 All tests PASSED! HTTPS configuration is working correctly.');
  } else {
    console.log('⚠️  Some tests FAILED. Please check the configuration.');
  }
  
  console.log('\n📱 Mobile Device Testing:');
  console.log('To test on mobile devices:');
  console.log(`1. Connect mobile device to same network`);
  console.log(`2. Install SSL certificate if using self-signed certificates`);
  console.log(`3. Access: https://${CONFIG.hostname}:${CONFIG.httpsPort}`);
  console.log(`4. Test QR scanner camera access functionality`);
  
  return passCount === totalCount;
}

// Run tests if called directly
if (require.main === module) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
}

module.exports = { runTests, CONFIG };
