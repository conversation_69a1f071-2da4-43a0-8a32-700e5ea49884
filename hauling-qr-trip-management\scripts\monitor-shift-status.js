#!/usr/bin/env node

/**
 * Shift Status Monitoring Tool
 * 
 * This script provides real-time monitoring of shift statuses and can be run
 * periodically to ensure the system is working correctly.
 * 
 * Run with: node scripts/monitor-shift-status.js
 * 
 * This module has been enhanced to export granular monitoring functions
 * for integration with the SystemMonitoringService API.
 */

const { Pool } = require('pg');
require('dotenv').config();

// Create a pool if running standalone, otherwise expect it to be provided
let defaultPool = null;
try {
  defaultPool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
  });
} catch (error) {
  console.warn('Warning: Could not create default database pool. A pool must be provided when calling functions.');
}

/**
 * Get current time context for shift monitoring
 * @param {Object} dbPool - Database connection pool
 * @returns {Object} Time context information
 */
async function getTimeContext(dbPool) {
  const pool = dbPool || defaultPool;
  if (!pool) throw new Error('Database pool is required');
  
  const timeContext = await pool.query(`
    SELECT 
      CURRENT_DATE as current_date,
      CURRENT_TIME as current_time,
      EXTRACT(hour FROM CURRENT_TIME) as current_hour,
      EXTRACT(minute FROM CURRENT_TIME) as current_minute
  `);
  
  const ctx = timeContext.rows[0];
  const isNightShiftTime = ctx.current_hour >= 18 || ctx.current_hour < 6;
  const isDayShiftTime = ctx.current_hour >= 6 && ctx.current_hour < 18;
  
  return {
    date: ctx.current_date,
    time: ctx.current_time,
    hour: ctx.current_hour,
    minute: ctx.current_minute,
    isNightShiftTime,
    isDayShiftTime,
    formattedDate: ctx.current_date.toISOString().substring(0,10),
    formattedTime: ctx.current_time.substring(0,8),
    formattedMinute: ctx.current_minute.toString().padStart(2, '0')
  };
}

/**
 * Check current shift statuses and identify issues
 * @param {Object} dbPool - Database connection pool
 * @param {Object} timeContext - Time context from getTimeContext()
 * @returns {Object} Shift status information and issues
 */
async function checkShiftStatuses(dbPool, timeContext) {
  const pool = dbPool || defaultPool;
  if (!pool) throw new Error('Database pool is required');
  
  // Get current shift statuses
  const currentShifts = await pool.query(`
    SELECT 
      ds.id,
      dt.truck_number,
      d.full_name as driver_name,
      ds.shift_type,
      ds.status,
      ds.start_time,
      ds.end_time,
      evaluate_shift_status(ds.id, CURRENT_TIMESTAMP) as calculated_status,
      CASE 
        WHEN ds.shift_type = 'day' AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time THEN 'ACTIVE'
        WHEN ds.shift_type = 'night' AND (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time) THEN 'ACTIVE'
        ELSE 'SCHEDULED'
      END as expected_status
    FROM driver_shifts ds
    JOIN dump_trucks dt ON ds.truck_id = dt.id
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.status != 'cancelled'
    ORDER BY dt.truck_number, ds.shift_type
  `);
  
  // Process shift statuses and identify issues
  let issueCount = 0;
  const issues = [];
  const shifts = currentShifts.rows.map(shift => {
    const statusMatch = shift.status === shift.calculated_status && 
                       shift.calculated_status.toUpperCase() === shift.expected_status;
    const statusIcon = statusMatch ? '✅' : '⚠️';
    
    if (!statusMatch) {
      issueCount++;
      issues.push({
        id: `shift-${shift.id}`,
        type: 'status_mismatch',
        severity: 'medium',
        description: `Shift status mismatch for ${shift.truck_number} (${shift.driver_name}): Current: ${shift.status}, Should be: ${shift.expected_status}`,
        affectedRecords: [`shift_id_${shift.id}`],
        autoFixable: true
      });
    }
    
    return {
      ...shift,
      statusMatch,
      statusIcon
    };
  });
  
  return {
    shifts,
    issues,
    issueCount,
    metrics: {
      totalShifts: shifts.length,
      activeShifts: shifts.filter(s => s.status === 'active').length,
      scheduledShifts: shifts.filter(s => s.status === 'scheduled').length,
      completedShifts: shifts.filter(s => s.status === 'completed').length,
      statusMismatches: issueCount
    }
  };
}

/**
 * Check assignment display status and identify issues
 * @param {Object} dbPool - Database connection pool
 * @returns {Object} Assignment display information and issues
 */
async function checkAssignmentDisplay(dbPool) {
  const pool = dbPool || defaultPool;
  if (!pool) throw new Error('Database pool is required');
  
  // Get assignment display status
  const assignmentDisplay = await pool.query(`
    SELECT
      a.id as assignment_id,
      t.truck_number,
      d.full_name as assigned_driver,
      ds.shift_type as active_shift_type,
      ds.status as shift_status,
      CASE
        WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
          CONCAT('✅ ', ds.shift_type, ' Shift Active')
        WHEN ds.id IS NOT NULL AND ds.status = 'scheduled' THEN
          CONCAT('📅 ', ds.shift_type, ' Shift Scheduled')
        ELSE '⚠️ No Active Shift'
      END as display_status
    FROM assignments a
    JOIN dump_trucks t ON a.truck_id = t.id
    LEFT JOIN drivers d ON a.driver_id = d.id
    LEFT JOIN driver_shifts ds ON (
      ds.truck_id = a.truck_id
      AND ds.status = 'active'
      -- Trust the 'active' status - the evaluate_shift_status function
      -- and auto-activation system handles the complex time/date logic
    )
    ORDER BY t.truck_number
  `);
  
  // Process assignment display and identify issues
  let noActiveShiftCount = 0;
  const issues = [];
  
  assignmentDisplay.rows.forEach(row => {
    if (row.display_status.includes('No Active Shift')) {
      noActiveShiftCount++;
      issues.push({
        id: `assignment-${row.assignment_id}`,
        type: 'no_active_shift',
        severity: 'medium',
        description: `No active shift for truck ${row.truck_number} (${row.assigned_driver || 'Unassigned'})`,
        affectedRecords: [`assignment_id_${row.assignment_id}`],
        autoFixable: true
      });
    }
  });
  
  return {
    assignments: assignmentDisplay.rows,
    issues,
    noActiveShiftCount,
    metrics: {
      totalAssignments: assignmentDisplay.rows.length,
      withActiveShift: assignmentDisplay.rows.length - noActiveShiftCount,
      withoutActiveShift: noActiveShiftCount,
      unassignedTrucks: assignmentDisplay.rows.filter(r => !r.assigned_driver).length
    }
  };
}

/**
 * Run auto-activation to fix shift and assignment issues
 * @param {Object} dbPool - Database connection pool
 * @returns {Object} Results of auto-activation
 */
async function runAutoActivation(dbPool) {
  const pool = dbPool || defaultPool;
  if (!pool) throw new Error('Database pool is required');
  
  // Run auto-activation
  await pool.query('SELECT schedule_auto_activation()');
  
  // Check if issues were resolved
  const fixedShifts = await pool.query(`
    SELECT 
      COUNT(*) as total,
      COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
      COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled,
      COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
    FROM driver_shifts 
    WHERE status != 'cancelled'
  `);
  
  return {
    total: parseInt(fixedShifts.rows[0].total),
    active: parseInt(fixedShifts.rows[0].active),
    scheduled: parseInt(fixedShifts.rows[0].scheduled),
    completed: parseInt(fixedShifts.rows[0].completed),
    timestamp: new Date().toISOString()
  };
}

/**
 * Monitor shift statuses and detect any issues
 * @param {Object} dbPool - Optional database connection pool
 * @param {Boolean} silent - If true, suppresses console output
 * @returns {Object} Comprehensive monitoring results
 */
async function monitorShiftStatus(dbPool, silent = false) {
  const pool = dbPool || defaultPool;
  const closePool = !dbPool && pool; // Close pool only if we created it
  
  if (!silent) {
    console.log('🔍 Shift Status Monitoring Tool');
    console.log('===============================\n');
  }
  
  try {
    // Step 1: Get current time context
    const timeContext = await getTimeContext(pool);
    
    if (!silent) {
      console.log('📅 Current Time Context:');
      console.log(`   Date: ${timeContext.formattedDate}`);
      console.log(`   Time: ${timeContext.formattedTime} (${timeContext.hour}:${timeContext.formattedMinute})`);
      console.log(`   Night shifts (18:00-06:00): Should be ${timeContext.isNightShiftTime ? 'ACTIVE ✅' : 'SCHEDULED 📅'}`);
      console.log(`   Day shifts (06:00-18:00): Should be ${timeContext.isDayShiftTime ? 'ACTIVE ✅' : 'SCHEDULED 📅'}`);
    }
    
    // Step 2: Check current shift statuses
    const shiftStatus = await checkShiftStatuses(pool, timeContext);
    
    if (!silent) {
      console.log('\n📊 Current Shift Status:');
      console.log('   Truck   | Driver        | Type  | Status    | Should Be | Match');
      console.log('   --------|---------------|-------|-----------|-----------|-------');
      
      shiftStatus.shifts.forEach(shift => {
        console.log(`   ${shift.truck_number.padEnd(7)} | ${shift.driver_name.substring(0,13).padEnd(13)} | ${shift.shift_type.padEnd(5)} | ${shift.status.padEnd(9)} | ${shift.expected_status.padEnd(9)} | ${shift.statusIcon}`);
      });
    }
    
    // Step 3: Check assignment display
    const assignmentStatus = await checkAssignmentDisplay(pool);
    
    if (!silent) {
      console.log('\n📋 Assignment Display Status:');
      console.log('   Truck   | Driver          | Display Status');
      console.log('   --------|-----------------|----------------');
      
      assignmentStatus.assignments.forEach(row => {
        console.log(`   ${row.truck_number.padEnd(7)} | ${(row.assigned_driver || 'None').substring(0,15).padEnd(15)} | ${row.display_status}`);
      });
    }
    
    // Step 4: Run auto-activation if needed
    let autoActivationResults = null;
    if (shiftStatus.issueCount > 0 || assignmentStatus.noActiveShiftCount > 0) {
      if (!silent) {
        console.log('\n⚠️ Issues detected! Running auto-activation...');
      }
      
      autoActivationResults = await runAutoActivation(pool);
      
      if (!silent) {
        console.log('   ✅ Auto-activation completed');
        console.log('   📊 Updated shift counts:');
        console.log(`      Total: ${autoActivationResults.total}`);
        console.log(`      Active: ${autoActivationResults.active}`);
        console.log(`      Scheduled: ${autoActivationResults.scheduled}`);
        console.log(`      Completed: ${autoActivationResults.completed}`);
      }
    }
    
    // Step 5: Summary
    if (!silent) {
      console.log('\n📝 Monitoring Summary:');
      console.log(`   Total shifts: ${shiftStatus.shifts.length}`);
      console.log(`   Status issues: ${shiftStatus.issueCount}`);
      console.log(`   Assignment display issues: ${assignmentStatus.noActiveShiftCount}`);
      
      if (shiftStatus.issueCount === 0 && assignmentStatus.noActiveShiftCount === 0) {
        console.log('\n✅ All systems operational! Shift management is working correctly.');
      } else {
        console.log('\n⚠️ Some issues were detected and auto-fixed. Please check again in a few minutes.');
      }
    }
    
    // Return comprehensive monitoring results
    return {
      timeContext,
      shiftStatus,
      assignmentStatus,
      autoActivationResults,
      summary: {
        totalIssues: shiftStatus.issueCount + assignmentStatus.noActiveShiftCount,
        allSystemsOperational: shiftStatus.issueCount === 0 && assignmentStatus.noActiveShiftCount === 0,
        timestamp: new Date().toISOString()
      }
    };
    
  } catch (error) {
    if (!silent) {
      console.error('❌ Monitoring error:', error.message);
    }
    throw error;
  } finally {
    if (closePool) {
      await closePool.end();
    }
  }
}

// Run monitoring if executed directly
if (require.main === module) {
  monitorShiftStatus();
}

module.exports = {
  monitorShiftStatus,
  getTimeContext,
  checkShiftStatuses,
  checkAssignmentDisplay,
  runAutoActivation
};