import { useState, useCallback } from 'react';
import toast from 'react-hot-toast';
import shiftService from '../services/shiftService';
import { getApiBaseUrl } from '../utils/network-utils';

// Constants
const RESULTS_LIMIT = 100;

// Safe token getter with error handling
const getAuthToken = () => {
  try {
    return localStorage.getItem('hauling_token');
  } catch (error) {
    console.error('❌ Error accessing localStorage:', error);
    return null;
  }
};

/**
 * Custom hook for managing shift data and related entities
 * Extracted from SimplifiedShiftManagement to reduce component complexity
 */
const useShiftManagementData = () => {
  const [shifts, setShifts] = useState([]);
  const [trucks, setTrucks] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({});

  const loadShifts = useCallback(async (filters) => {
    try {
      console.log('🔄 loadShifts() called - starting to refresh shift data...');
      setLoading(true);
      setShifts([]); // Reset shifts to force UI refresh

      // Build query parameters from filters
      const queryParams = {};
      if (filters.truck_id) queryParams.truck_id = filters.truck_id;
      if (filters.driver_id) queryParams.driver_id = filters.driver_id;
      if (filters.status) queryParams.status = filters.status;
      if (filters.shift_type) queryParams.shift_type = filters.shift_type;
      if (filters.date_from) queryParams.date_from = filters.date_from;
      if (filters.date_to) queryParams.date_to = filters.date_to;

      queryParams.sort_by = filters.sort_by;
      queryParams.sort_order = filters.sort_order;
      queryParams.limit = RESULTS_LIMIT;

      console.log('📊 Loading shifts with filters:', queryParams);

      const response = await shiftService.getShifts(queryParams);
      console.log('📊 Received shift data:', {
        totalShifts: response.data?.length || 0,
        shifts: response.data?.map(s => ({ id: s.id, status: s.status, truck: s.truck_number })) || []
      });

      setShifts(response.data || []);
      setPagination(response.pagination || {});
      console.log('✅ State updated - component should re-render now');

    } catch (error) {
      console.error('❌ Error loading shifts:', error);
      toast.error('Failed to load shifts');
    } finally {
      setLoading(false);
      console.log('🔄 loadShifts() completed');
    }
  }, []);

  const loadTrucks = useCallback(async () => {
    try {
      console.log('🚛 Loading trucks data...');
      const apiUrl = getApiBaseUrl();
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }
      
      const response = await fetch(`${apiUrl}/trucks`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to load trucks: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('🚛 Trucks loaded:', data.data?.length || 0, 'trucks');
      setTrucks(data.data || []);
    } catch (error) {
      console.error('❌ Error loading trucks:', error);
      toast.error('Failed to load trucks data');
      setTrucks([]);
    }
  }, []);

  const loadDrivers = useCallback(async () => {
    try {
      console.log('👨‍💼 Loading drivers data...');
      const apiUrl = getApiBaseUrl();
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }
      
      const response = await fetch(`${apiUrl}/drivers`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to load drivers: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('👨‍💼 Drivers loaded:', data.data?.length || 0, 'drivers');
      setDrivers(data.data || []);
    } catch (error) {
      console.error('❌ Error loading drivers:', error);
      toast.error('Failed to load drivers data');
      setDrivers([]);
    }
  }, []);

  const handleStatusChange = useCallback(async (shiftId, newStatus) => {
    const STATUS_CHANGE_DELAY = 500; // ms - Faster refresh for status changes
    
    try {
      console.log(`🔄 Starting ${newStatus} operation for shift ID: ${shiftId}`);
      const startTime = Date.now();

      // Immediate UI update before API call
      setShifts(prevShifts =>
        prevShifts.map(shift =>
          shift.id === shiftId ? { ...shift, status: newStatus } : shift
        )
      );
      console.log('🔄 Immediate UI update applied');

      // Make API call
      let apiResponse;
      if (newStatus === 'cancelled') {
        console.log('📞 Calling cancelShift API...');
        apiResponse = await shiftService.cancelShift(shiftId);
        console.log('✅ Cancel API call successful', { response: apiResponse });
        toast.success('Shift cancelled successfully!');
      } else if (newStatus === 'completed') {
        console.log('📞 Calling completeShift API...');
        apiResponse = await shiftService.completeShift(shiftId);
        console.log('✅ Complete API call successful', { response: apiResponse });
        toast.success('Shift completed successfully!');
      } else {
        console.log(`📞 Calling updateShift API for status: ${newStatus}...`);
        apiResponse = await shiftService.updateShift(shiftId, { status: newStatus });
        console.log('✅ Update API call successful', { response: apiResponse });
        toast.success(`Shift ${newStatus} successfully!`);
      }

      // Optimized refresh - wait for database consistency then update only the changed shift
      console.log(`⏳ Waiting ${STATUS_CHANGE_DELAY}ms before refresh...`);
      await new Promise(resolve => setTimeout(resolve, STATUS_CHANGE_DELAY));
      
      console.log('🔄 Optimized UI refresh starting...');
      setShifts(prevShifts =>
        prevShifts.map(shift =>
          shift.id === shiftId
            ? { ...shift, ...apiResponse.data } // Merge API response
            : shift
        )
      );
      console.log('✅ Optimized UI refresh completed', {
        duration: `${Date.now() - startTime}ms`
      });

    } catch (error) {
      console.error('❌ Error updating shift status:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        shiftId,
        newStatus,
        time: new Date().toISOString()
      });
      toast.error(error.message || 'Failed to update shift status');
      
      // Revert UI update if API call fails
      setShifts(prevShifts =>
        prevShifts.map(shift =>
          shift.id === shiftId ? { ...shift, status: shift.status } : shift
        )
      );
      console.log('🔄 Reverted UI update due to error');
    }
  }, []);

  return {
    // Data
    shifts,
    trucks,
    drivers,
    pagination,
    loading,
    
    // Actions
    loadShifts,
    loadTrucks,
    loadDrivers,
    handleStatusChange,
    setShifts // For WebSocket updates
  };
};

export default useShiftManagementData;