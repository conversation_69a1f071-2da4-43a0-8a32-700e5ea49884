/**
 * Diagnostic Script for Analytics Data Issues
 * 
 * This script helps identify why Route Pattern Analysis and Performance Rankings
 * are not showing data in the analytics dashboard.
 */

const { query } = require('../server/config/database');

async function diagnoseAnalyticsData() {
  console.log('🔍 Diagnosing Analytics Data Issues...\n');

  try {
    // 1. Check basic trip_logs data
    console.log('1. Checking trip_logs table...');
    const tripLogsCount = await query('SELECT COUNT(*) as count FROM trip_logs');
    console.log(`   Total trip logs: ${tripLogsCount.rows[0].count}`);

    // 2. Check trip statuses
    console.log('\n2. Checking trip statuses...');
    const statusCounts = await query(`
      SELECT status, COUNT(*) as count 
      FROM trip_logs 
      GROUP BY status 
      ORDER BY count DESC
    `);
    statusCounts.rows.forEach(row => {
      console.log(`   ${row.status}: ${row.count}`);
    });

    // 3. Check completed trips in last 30 days
    console.log('\n3. Checking completed trips in last 30 days...');
    const recentCompleted = await query(`
      SELECT COUNT(*) as count 
      FROM trip_logs 
      WHERE status = 'trip_completed' 
      AND created_at >= CURRENT_DATE - INTERVAL '30 days'
    `);
    console.log(`   Completed trips (last 30 days): ${recentCompleted.rows[0].count}`);

    // 4. Check location data availability
    console.log('\n4. Checking location data...');
    const locationsCount = await query('SELECT COUNT(*) as count FROM locations');
    console.log(`   Total locations: ${locationsCount.rows[0].count}`);

    // 5. Check assignments with location data
    console.log('\n5. Checking assignments with locations...');
    const assignmentsWithLocations = await query(`
      SELECT COUNT(*) as count 
      FROM assignments a
      WHERE a.loading_location_id IS NOT NULL 
      AND a.unloading_location_id IS NOT NULL
    `);
    console.log(`   Assignments with locations: ${assignmentsWithLocations.rows[0].count}`);

    // 6. Check trucks and drivers
    console.log('\n6. Checking trucks and drivers...');
    const trucksCount = await query('SELECT COUNT(*) as count FROM dump_trucks');
    const driversCount = await query('SELECT COUNT(*) as count FROM drivers');
    console.log(`   Total trucks: ${trucksCount.rows[0].count}`);
    console.log(`   Total drivers: ${driversCount.rows[0].count}`);

    // 7. Check for route pattern data
    console.log('\n7. Checking route pattern data...');
    const routePatternSample = await query(`
      SELECT
        CONCAT(COALESCE(al.name, ll.name), ' → ', COALESCE(aul.name, ul.name)) as route,
        COUNT(*) as trip_count
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY
        CONCAT(COALESCE(al.name, ll.name), ' → ', COALESCE(aul.name, ul.name))
      ORDER BY trip_count DESC
      LIMIT 5
    `);
    
    if (routePatternSample.rows.length > 0) {
      console.log('   Sample route patterns:');
      routePatternSample.rows.forEach(row => {
        console.log(`     ${row.route}: ${row.trip_count} trips`);
      });
    } else {
      console.log('   ❌ No route patterns found');
    }

    // 8. Check for performance rankings data
    console.log('\n8. Checking performance rankings data...');
    const rankingsSample = await query(`
      SELECT
        dt.truck_number,
        COUNT(*) as total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY dt.truck_number, dt.id
      ORDER BY total_trips DESC
      LIMIT 5
    `);

    if (rankingsSample.rows.length > 0) {
      console.log('   Sample truck performance:');
      rankingsSample.rows.forEach(row => {
        console.log(`     ${row.truck_number}: ${row.completed_trips}/${row.total_trips} completed`);
      });
    } else {
      console.log('   ❌ No truck performance data found');
    }

    // 9. Check date ranges
    console.log('\n9. Checking data date ranges...');
    const dateRange = await query(`
      SELECT 
        MIN(created_at) as earliest,
        MAX(created_at) as latest,
        COUNT(*) as total
      FROM trip_logs
    `);
    
    if (dateRange.rows[0].total > 0) {
      console.log(`   Data range: ${dateRange.rows[0].earliest} to ${dateRange.rows[0].latest}`);
      console.log(`   Total records: ${dateRange.rows[0].total}`);
    } else {
      console.log('   ❌ No trip logs found');
    }

    // 10. Recommendations
    console.log('\n📋 RECOMMENDATIONS:');
    
    if (parseInt(recentCompleted.rows[0].count) === 0) {
      console.log('   ❌ No completed trips in last 30 days');
      console.log('   → Check if trips are being marked as completed');
      console.log('   → Verify trip workflow is working correctly');
    }
    
    if (parseInt(assignmentsWithLocations.rows[0].count) === 0) {
      console.log('   ❌ No assignments with location data');
      console.log('   → Check if locations are being assigned to trips');
      console.log('   → Verify location data is being saved');
    }
    
    if (routePatternSample.rows.length === 0) {
      console.log('   ❌ No route patterns available');
      console.log('   → Ensure trips have both loading and unloading locations');
      console.log('   → Check if location names are properly set');
    }
    
    if (rankingsSample.rows.length === 0) {
      console.log('   ❌ No truck performance data available');
      console.log('   → Verify trucks are assigned to trips');
      console.log('   → Check if trip durations are being calculated');
    }

    console.log('\n✅ Diagnosis complete!');

  } catch (error) {
    console.error('❌ Error during diagnosis:', error);
  }
}

// Run the diagnosis
if (require.main === module) {
  diagnoseAnalyticsData().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { diagnoseAnalyticsData };