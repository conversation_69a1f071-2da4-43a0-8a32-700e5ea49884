#!/bin/bash

# =============================================================================
# TEST ERROR 520 FIXES
# =============================================================================
# Tests the Error 520 fixes in a local environment
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="/var/www/hauling-qr-system"
PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[TEST]${NC} $1"
}

log_error() {
    echo -e "${RED}[TEST]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[TEST]${NC} $1"
}

echo "🧪 TESTING ERROR 520 FIXES"
echo "=========================="
echo "Script Directory: $SCRIPT_DIR"
echo "App Directory: $APP_DIR"
echo "Production Domain: $PRODUCTION_DOMAIN"
echo "Date: $(date)"
echo

# Test 1: Validate PM2 script has correct configuration
log_info "Test 1: Validating PM2 deployment script configuration..."
if grep -q "instances: 1," "$SCRIPT_DIR/6_install-pm2.sh"; then
    log_success "✅ PM2 deployment script configured for 1 instance"
else
    log_error "❌ PM2 deployment script still has multiple instances"
    echo "Found: $(grep -n "instances:" "$SCRIPT_DIR/6_install-pm2.sh" | head -3)"
fi

# Test 2: Validate root ecosystem.config.js has correct configuration
log_info "Test 2: Validating root ecosystem.config.js configuration..."
ROOT_ECOSYSTEM="$SCRIPT_DIR/../ecosystem.config.js"
if [[ -f "$ROOT_ECOSYSTEM" ]]; then
    if grep -q "instances: 1," "$ROOT_ECOSYSTEM"; then
        log_success "✅ Root ecosystem.config.js configured for 1 instance"
    else
        log_error "❌ Root ecosystem.config.js still has multiple instances"
        echo "Found: $(grep -n "instances:" "$ROOT_ECOSYSTEM" | head -3)"
    fi
else
    log_warning "⚠️ Root ecosystem.config.js not found at $ROOT_ECOSYSTEM"
fi

# Test 3: Validate NGINX script has correct upstream configuration
log_info "Test 3: Validating NGINX deployment script configuration..."
if grep -q "upstream hauling_backend" "$SCRIPT_DIR/5_install-nginx.sh"; then
    log_success "✅ NGINX deployment script has correct upstream configuration"
else
    log_error "❌ NGINX deployment script missing upstream configuration"
fi

# Test 4: Validate validation script exists and is executable
log_info "Test 4: Validating Error 520 validation script..."
VALIDATION_SCRIPT="$SCRIPT_DIR/validate-error-520-fixes.sh"
if [[ -f "$VALIDATION_SCRIPT" ]]; then
    if [[ -x "$VALIDATION_SCRIPT" ]]; then
        log_success "✅ Error 520 validation script exists and is executable"
    else
        log_warning "⚠️ Error 520 validation script exists but is not executable"
        chmod +x "$VALIDATION_SCRIPT" 2>/dev/null || true
    fi
else
    log_error "❌ Error 520 validation script not found"
fi

# Test 5: Check if PM2 script has Error 520 prevention logic
log_info "Test 5: Validating PM2 script has Error 520 prevention logic..."
if grep -q "Error 520 fix" "$SCRIPT_DIR/6_install-pm2.sh"; then
    log_success "✅ PM2 script contains Error 520 prevention logic"
else
    log_error "❌ PM2 script missing Error 520 prevention logic"
fi

# Test 6: Check if PM2 script has port validation
log_info "Test 6: Validating PM2 script has port validation..."
if grep -q "port 8080" "$SCRIPT_DIR/6_install-pm2.sh"; then
    log_success "✅ PM2 script contains port 8080 validation"
else
    log_error "❌ PM2 script missing port 8080 validation"
fi

echo
echo "🏁 ERROR 520 FIXES TEST COMPLETED"
echo "================================="
echo
echo "📋 SUMMARY:"
echo "- PM2 configuration: Fixed to use 1 instance instead of 4"
echo "- Root ecosystem.config.js: Updated to prevent port binding conflicts"
echo "- NGINX configuration: Includes proper upstream configuration"
echo "- Validation script: Created for post-deployment verification"
echo "- Error 520 prevention: Integrated into deployment pipeline"
echo
echo "🚀 NEXT STEPS:"
echo "1. Deploy using: ./auto-deploy.sh"
echo "2. Validate using: ./validate-error-520-fixes.sh"
echo "3. Monitor port 8080 and backend health endpoint"
echo
