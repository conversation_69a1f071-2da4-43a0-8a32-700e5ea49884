-- Migration 024: Update Shift Type Detection Logic
-- Updates the shift type detection to match new requirements:
-- Day shift: 06:00 AM to 06:00 PM (hours 6-18)
-- Night shift: 06:01 PM to 05:59 AM (hours 19-23 OR 0-5)

-- Update the determine_shift_type_by_time function with new logic
CREATE OR REPLACE FUNCTION determine_shift_type_by_time(p_start_time TIME)
RETURNS shift_type AS $$
BEGIN
    -- Day shift: 06:00 AM to 06:00 PM (hours 6-18)
    IF EXTRACT(HOUR FROM p_start_time) BETWEEN 6 AND 18 THEN
        RETURN 'day';
    -- Night shift: 06:01 PM to 05:59 AM (hours 19-23 OR 0-5)
    ELSIF EXTRACT(HOUR FROM p_start_time) >= 19 OR EXTRACT(HOUR FROM p_start_time) <= 5 THEN
        RETURN 'night';
    ELSE
        RETURN 'custom';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Update any existing active shifts that might have been classified incorrectly
-- This specifically targets shifts created at hour 18 (6 PM) that should be 'day' instead of 'night'
UPDATE driver_shifts 
SET 
    shift_type = 'day',
    updated_at = CURRENT_TIMESTAMP
WHERE auto_created = true 
  AND EXTRACT(HOUR FROM start_time) = 18
  AND shift_type = 'night'
  AND status = 'active';

-- Add comment for documentation
COMMENT ON FUNCTION determine_shift_type_by_time(TIME) IS 'Updated shift type determination: Day shift 6AM-6PM (hours 6-18), Night shift 6:01PM-5:59AM (hours 19-23 OR 0-5)';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 024 completed: Updated shift type detection logic - Day shift now includes 6 PM (hour 18)';
END $$;