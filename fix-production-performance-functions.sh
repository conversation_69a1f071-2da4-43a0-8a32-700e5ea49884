#!/bin/bash

# =============================================================================
# PRODUCTION VPS PERFORMANCE FUNCTIONS FIX
# =============================================================================
# This script adds the missing performance monitoring functions to the
# intelligent-deployment-framework.sh file on production VPS
# =============================================================================

set -euo pipefail

echo "🔧 Fixing missing performance functions on production VPS..."

# Check if we're in the right directory
if [[ ! -f "intelligent-deployment-framework.sh" ]]; then
  echo "❌ ERROR: intelligent-deployment-framework.sh not found"
  echo "Please run this script from the deploy-hauling-qr-ubuntu directory"
  exit 1
fi

# Check if functions already exist
if grep -q "start_performance_timer" intelligent-deployment-framework.sh; then
  echo "✅ Performance functions already exist - no fix needed"
  exit 0
fi

echo "📝 Adding missing performance monitoring functions..."

# Add the missing functions to the end of the file
cat >> intelligent-deployment-framework.sh << 'EOF'

# =============================================================================
# ENHANCED PERFORMANCE MONITORING FUNCTIONS
# =============================================================================

# Performance tracking arrays
declare -A PERFORMANCE_TIMERS
declare -A PERFORMANCE_START_TIMES

# Start performance timer for a specific operation
start_performance_timer() {
  local timer_name="$1"
  local start_time=$(date +%s)
  
  PERFORMANCE_START_TIMES["$timer_name"]="$start_time"
  log_info "⏱️ Started timer: $timer_name"
}

# End performance timer and calculate duration
end_performance_timer() {
  local timer_name="$1"
  local end_time=$(date +%s)
  local start_time="${PERFORMANCE_START_TIMES[$timer_name]:-$end_time}"
  local duration=$((end_time - start_time))
  
  PERFORMANCE_TIMERS["$timer_name"]="$duration"
  log_info "⏱️ $timer_name completed in ${duration}s"
}

# Generate comprehensive performance report
generate_performance_report() {
  log_info "📊 DEPLOYMENT PERFORMANCE REPORT:"
  local total_time=0
  
  for timer in "${!PERFORMANCE_TIMERS[@]}"; do
    local duration="${PERFORMANCE_TIMERS[$timer]}"
    total_time=$((total_time + duration))
    log_info "   • $timer: ${duration}s"
  done
  log_info "   • TOTAL DEPLOYMENT TIME: ${total_time}s"
}

# Initialize performance monitoring
init_performance_monitoring() {
  log_info "📊 Initializing performance monitoring..."
  declare -A PERFORMANCE_TIMERS
  declare -A PERFORMANCE_START_TIMES
  log_success "✅ Performance monitoring initialized"
}

# Initialize markers system
init_markers_system() {
  log_info "🔧 Initializing intelligent deployment markers system..."
  sudo mkdir -p "$PHASE_MARKERS_DIR" 2>/dev/null || true
  init_performance_monitoring
  log_info "✅ Intelligent deployment markers system initialized"
}

EOF

echo "✅ Performance functions added successfully!"
echo "🧪 Testing function availability..."

# Test if the functions are now available
if grep -q "start_performance_timer" intelligent-deployment-framework.sh; then
  echo "✅ start_performance_timer function found"
else
  echo "❌ ERROR: Function still not found"
  exit 1
fi

echo "🎉 Production VPS performance functions fix completed!"
echo ""
echo "Next steps:"
echo "1. Run: sudo -E ./auto-deploy.sh status"
echo "2. If status works, run: sudo -E ./auto-deploy.sh --environment=production"
