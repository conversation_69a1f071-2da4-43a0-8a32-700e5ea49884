/**
 * Database Reset Utility
 * Fixes IndexedDB version conflicts and corrupted databases
 */

const DB_NAME = 'HaulingQROffline';

export class DatabaseReset {
  
  /**
   * Delete the entire IndexedDB database to fix version conflicts
   */
  static async resetDatabase() {
    return new Promise((resolve, reject) => {
      console.log('[DatabaseReset] Deleting IndexedDB database:', DB_NAME);
      
      const deleteRequest = indexedDB.deleteDatabase(DB_NAME);
      
      deleteRequest.onsuccess = () => {
        console.log('[DatabaseReset] Database deleted successfully');
        resolve(true);
      };
      
      deleteRequest.onerror = (event) => {
        console.error('[DatabaseReset] Failed to delete database:', event.target.error);
        reject(event.target.error);
      };
      
      deleteRequest.onblocked = () => {
        console.warn('[DatabaseReset] Database deletion blocked - close all tabs and try again');
        reject(new Error('Database deletion blocked - close all tabs and try again'));
      };
    });
  }
  
  /**
   * Check if database exists and get its version
   */
  static async checkDatabaseVersion() {
    return new Promise((resolve, reject) => {
      // Try to open database without specifying version to get current version
      const request = indexedDB.open(DB_NAME);
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        const version = db.version;
        db.close();
        
        console.log('[DatabaseReset] Current database version:', version);
        resolve(version);
      };
      
      request.onerror = (event) => {
        console.error('[DatabaseReset] Failed to check database version:', event.target.error);
        reject(event.target.error);
      };
    });
  }
  
  /**
   * List all databases (if supported by browser)
   */
  static async listDatabases() {
    if ('databases' in indexedDB) {
      try {
        const databases = await indexedDB.databases();
        console.log('[DatabaseReset] All IndexedDB databases:', databases);
        return databases;
      } catch (error) {
        console.error('[DatabaseReset] Failed to list databases:', error);
        return [];
      }
    } else {
      console.log('[DatabaseReset] indexedDB.databases() not supported in this browser');
      return [];
    }
  }
  
  /**
   * Complete database reset and reinitialization
   */
  static async resetAndReinitialize() {
    try {
      console.log('[DatabaseReset] Starting complete database reset...');
      
      // Step 1: Check current version
      try {
        const currentVersion = await this.checkDatabaseVersion();
        console.log('[DatabaseReset] Current version before reset:', currentVersion);
      } catch (error) {
        console.log('[DatabaseReset] Could not check current version (database may not exist)');
      }
      
      // Step 2: Delete database
      await this.resetDatabase();
      
      // Step 3: Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Step 4: Reinitialize with correct version
      const { offlineDB } = await import('../services/offlineDB.js');
      await offlineDB.initialize();
      
      console.log('[DatabaseReset] Database reset and reinitialized successfully');
      return { success: true, message: 'Database reset successfully' };
      
    } catch (error) {
      console.error('[DatabaseReset] Failed to reset database:', error);
      return { success: false, error: error.message };
    }
  }
  
  /**
   * Clear all data without deleting the database structure
   */
  static async clearAllData() {
    try {
      const { offlineDB } = await import('../services/offlineDB.js');
      await offlineDB.initialize();
      
      // Clear all stores
      await offlineDB.clearStore('connectionQueue');
      await offlineDB.clearStore('conflicts');
      await offlineDB.clearStore('referenceData');
      
      console.log('[DatabaseReset] All data cleared successfully');
      return { success: true, message: 'All data cleared successfully' };
      
    } catch (error) {
      console.error('[DatabaseReset] Failed to clear data:', error);
      return { success: false, error: error.message };
    }
  }
}

// Make it available globally for console debugging
if (typeof window !== 'undefined') {
  window.DatabaseReset = DatabaseReset;
}

export default DatabaseReset;