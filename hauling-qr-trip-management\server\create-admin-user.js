/**
 * Create Admin User for Development/Testing
 */

const bcrypt = require('bcryptjs');
const { query } = require('./config/database');

async function createAdminUser() {
  console.log('🔧 Creating admin user for development...');
  
  try {
    // Check if admin user already exists
    const existingUser = await query(
      'SELECT id, username FROM users WHERE username = $1',
      ['admin']
    );
    
    if (existingUser.rows.length > 0) {
      console.log('✅ Admin user already exists');
      console.log('   Username: admin');
      console.log('   Password: admin123');
      console.log('   Role: admin');
      return;
    }
    
    // Hash password
    const password = 'admin123';
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    // Create admin user
    const result = await query(`
      INSERT INTO users (username, email, password_hash, full_name, role, status, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
      RETURNING id, username, email, full_name, role
    `, [
      'admin',
      '<EMAIL>',
      passwordHash,
      'System Administrator',
      'admin',
      'active'
    ]);
    
    const user = result.rows[0];
    
    console.log('✅ Admin user created successfully!');
    console.log('📋 Login Credentials:');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('   Email: <EMAIL>');
    console.log('   Role: admin');
    console.log('   ID:', user.id);
    
    // Also create a test driver user
    const driverPasswordHash = await bcrypt.hash('driver123', saltRounds);
    
    const driverResult = await query(`
      INSERT INTO users (username, email, password_hash, full_name, role, status, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
      RETURNING id, username, email, full_name, role
    `, [
      'driver',
      '<EMAIL>',
      driverPasswordHash,
      'Test Driver',
      'driver',
      'active'
    ]);
    
    const driver = driverResult.rows[0];
    
    console.log('✅ Test driver user created!');
    console.log('📋 Driver Credentials:');
    console.log('   Username: driver');
    console.log('   Password: driver123');
    console.log('   Role: driver');
    console.log('   ID:', driver.id);
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    
    if (error.code === '42P01') {
      console.error('💡 Users table does not exist. Please run database migrations first.');
    } else if (error.code === '23505') {
      console.error('💡 User already exists with this username or email.');
    }
  }
}

// Run if called directly
if (require.main === module) {
  createAdminUser().then(() => {
    console.log('\n🎯 Admin user setup complete!');
    console.log('🚀 You can now login to the system:');
    console.log('   Frontend: http://localhost:3000');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    process.exit(0);
  }).catch(error => {
    console.error('❌ Failed to create admin user:', error);
    process.exit(1);
  });
}

module.exports = { createAdminUser };
