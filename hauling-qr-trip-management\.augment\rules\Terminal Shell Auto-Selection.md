---
type: "always_apply"
description: "Terminal Shell Auto-Selection Rule Windows Powershell"
---
# Terminal Shell Auto-Selection Rule

**CRITICAL**: This rule automatically triggers for ALL terminal/command execution operations without user intervention.

## Scope and Activation

This rule applies to ALL terminal execution tools and operations:
- `launch-process` tool calls
- Any command-line operations  
- Script execution requests
- Process management commands
- File system operations via terminal
- Package manager operations
- Development server startup/shutdown
- Database operations via CLI
- Git operations
- Any other terminal-based activities

**Auto-Activation**: This rule triggers automatically whenever ANY terminal execution is required - no user request needed.

## Platform-Specific Shell Requirements

### Windows Systems (Detected: win32, Windows_NT)
**Primary Shell**: PowerShell (`powershell.exe` or `pwsh.exe`)
- Always use PowerShell as the default shell for Windows operations
- Format commands using PowerShell syntax:
  - Use semicolons (`;`) instead of `&&` for command chaining
  - Use PowerShell cmdlets when available (`Get-Process` vs `ps`)
  - Handle PowerShell-specific escaping: backticks for special chars
  - Use proper PowerShell quoting: single quotes for literals, double quotes for variables
- Examples:
  ```powershell
  # Correct PowerShell syntax
  cd server; npm start
  Get-Process | Where-Object {$_.Name -eq "node"}
  $env:NODE_ENV = "production"; node server.js
  ```

**Fallback**: Command Prompt (`cmd.exe`) if PowerShell unavailable
- Use CMD syntax: `&&` for chaining, `%VAR%` for variables
- Handle CMD-specific escaping and quoting

### Unix/Linux/macOS Systems (Detected: linux, darwin, unix)
**Primary Shell**: Bash (`/bin/bash`)
- Always use Bash as the default shell for Unix-like systems
- Format commands using Bash syntax:
  - Use `&&` for command chaining
  - Use standard Unix commands and utilities
  - Handle Bash-specific escaping and quoting
  - Use `$VAR` for variable expansion
- Examples:
  ```bash
  # Correct Bash syntax
  cd server && npm start
  ps aux | grep node
  export NODE_ENV=production && node server.js
  ```

**Fallback**: System default shell (`/bin/sh`) if Bash unavailable

## Implementation Requirements

### 1. Automatic OS Detection
Before executing ANY terminal command:
```javascript
// Detect operating system
const os = process.platform;
const isWindows = os === 'win32';
const isUnix = ['linux', 'darwin', 'freebsd', 'openbsd'].includes(os);
```

### 2. Shell Selection Logic
```javascript
// Windows shell selection
if (isWindows) {
  primaryShell = 'powershell.exe';
  fallbackShell = 'cmd.exe';
  commandSeparator = ';';
} 

// Unix shell selection  
if (isUnix) {
  primaryShell = '/bin/bash';
  fallbackShell = '/bin/sh';
  commandSeparator = '&&';
}
```

### 3. Command Syntax Translation
Automatically convert command syntax based on target shell:

**For PowerShell (Windows)**:
- Convert `command1 && command2` → `command1; command2`
- Convert `export VAR=value` → `$env:VAR = "value"`
- Convert `ps aux | grep pattern` → `Get-Process | Where-Object {$_.Name -match "pattern"}`
- Handle path separators: `/` → `\` when needed

**For Bash (Unix)**:
- Keep standard Unix syntax
- Ensure proper escaping for special characters
- Use standard Unix utilities and commands

### 4. Tool Call Implementation
Always specify shell explicitly in launch-process calls:

**Windows Example**:
```javascript
launch-process({
  command: "cd server; npm start",
  shell: "powershell.exe",
  cwd: workingDirectory,
  wait: true,
  max_wait_seconds: 30
})
```

**Unix Example**:
```javascript
launch-process({
  command: "cd server && npm start", 
  shell: "/bin/bash",
  cwd: workingDirectory,
  wait: true,
  max_wait_seconds: 30
})
```

## Error Handling and Fallbacks

### Shell Availability Check
1. **Primary Shell Test**: Attempt to use the preferred shell first
2. **Fallback Activation**: If primary shell fails, automatically switch to fallback
3. **Error Reporting**: Provide clear messages about shell selection and any issues

### Common Error Scenarios
- **PowerShell not found**: Fall back to cmd.exe with appropriate syntax conversion
- **Bash not available**: Fall back to /bin/sh with compatible syntax
- **Permission issues**: Provide guidance on shell access permissions
- **Syntax errors**: Detect and correct common cross-platform syntax issues

### Fallback Command Examples
```powershell
# Windows fallback (CMD syntax)
cd server && npm start  # If PowerShell fails, use CMD with &&

# Unix fallback (sh syntax)  
cd server; npm start    # If Bash fails, use sh with simpler syntax
```

## Automatic Syntax Conversion Rules

### Windows PowerShell Conversions
- `&&` → `;` (command chaining)
- `export VAR=value` → `$env:VAR = "value"` (environment variables)
- `which command` → `Get-Command command` (command location)
- `ps aux` → `Get-Process` (process listing)
- `kill PID` → `Stop-Process -Id PID` (process termination)
- `ls -la` → `Get-ChildItem` (directory listing)

### Unix Bash Conversions (from Windows)
- `;` → `&&` (when appropriate for error handling)
- `$env:VAR` → `$VAR` (environment variables)
- `Get-Process` → `ps aux` (process listing)
- `Stop-Process` → `kill` (process termination)
- Windows paths → Unix paths (when applicable)

## Integration with Existing Tools

This rule automatically enhances:
- **Package Management**: npm, pip, cargo commands with proper shell
- **Development Servers**: Node.js, Python, React development servers
- **Database Operations**: PostgreSQL, MySQL CLI operations
- **Git Operations**: All git commands with proper shell context
- **File Operations**: File system navigation and manipulation
- **Process Management**: Starting, stopping, and monitoring processes

## Validation and Testing

Before executing any terminal command:
1. **OS Detection**: Confirm operating system identification
2. **Shell Availability**: Verify preferred shell is accessible
3. **Syntax Validation**: Check command syntax matches target shell
4. **Path Validation**: Ensure file paths use correct separators
5. **Permission Check**: Verify shell execution permissions

## Error Messages and Debugging

Provide clear, actionable error messages:
- "PowerShell not available, falling back to Command Prompt"
- "Bash not found, using system default shell (/bin/sh)"
- "Command syntax converted from Bash to PowerShell format"
- "Shell execution failed, check permissions and try again"

This rule ensures consistent, reliable terminal operations across all platforms while maintaining the specific requirements and syntax of each operating system's preferred shell environment.
