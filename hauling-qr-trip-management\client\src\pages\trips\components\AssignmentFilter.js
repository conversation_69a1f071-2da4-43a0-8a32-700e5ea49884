import React, { useState, useMemo } from 'react';

const AssignmentFilter = ({ 
  assignments = [], 
  selectedAssignmentId = '', 
  onAssignmentChange, 
  loading = false,
  className = ""
}) => {
  const [filterType, setFilterType] = useState('all'); // 'all', 'truck', 'driver', 'route', 'status'
  const [searchTerm, setSearchTerm] = useState('');

  // Group assignments by different criteria
  const groupedAssignments = useMemo(() => {
    if (!assignments.length) return { trucks: {}, drivers: {}, routes: {}, statuses: {} };

    return assignments.reduce((acc, assignment) => {
      // Group by truck
      const truckKey = `${assignment.truck_number}-${assignment.license_plate}`;
      if (!acc.trucks[truckKey]) {
        acc.trucks[truckKey] = {
          label: `🚚 ${assignment.truck_number} (${assignment.license_plate})`,
          assignments: []
        };
      }
      acc.trucks[truckKey].assignments.push(assignment);

      // Group by driver
      const driverKey = `${assignment.driver_name}-${assignment.employee_id}`;
      if (!acc.drivers[driverKey]) {
        acc.drivers[driverKey] = {
          label: `👨‍💼 ${assignment.driver_name} (${assignment.employee_id})`,
          assignments: []
        };
      }
      acc.drivers[driverKey].assignments.push(assignment);

      // Group by route
      const routeKey = `${assignment.loading_location_name}-${assignment.unloading_location_name}`;
      if (!acc.routes[routeKey]) {
        acc.routes[routeKey] = {
          label: `🛣️ ${assignment.loading_location_name} → ${assignment.unloading_location_name}`,
          assignments: []
        };
      }
      acc.routes[routeKey].assignments.push(assignment);

      // Group by status
      if (!acc.statuses[assignment.status]) {
        acc.statuses[assignment.status] = {
          label: `📊 ${assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1).replace('_', ' ')}`,
          assignments: []
        };
      }
      acc.statuses[assignment.status].assignments.push(assignment);

      return acc;
    }, { trucks: {}, drivers: {}, routes: {}, statuses: {} });
  }, [assignments]);

  // Filter assignments based on search term
  const filteredAssignments = useMemo(() => {
    if (!searchTerm) return assignments;
    
    const term = searchTerm.toLowerCase();
    return assignments.filter(assignment => 
      assignment.truck_number.toLowerCase().includes(term) ||
      assignment.license_plate.toLowerCase().includes(term) ||
      assignment.driver_name.toLowerCase().includes(term) ||
      assignment.employee_id.toLowerCase().includes(term) ||
      assignment.loading_location_name.toLowerCase().includes(term) ||
      assignment.unloading_location_name.toLowerCase().includes(term) ||
      assignment.assignment_code?.toLowerCase().includes(term)
    );
  }, [assignments, searchTerm]);

  // Get filtered options based on filter type
  const getFilteredOptions = () => {
    switch (filterType) {
      case 'truck':
        return Object.entries(groupedAssignments.trucks).map(([key, group]) => ({
          label: group.label,
          options: group.assignments.filter(a => 
            !searchTerm || a.truck_number.toLowerCase().includes(searchTerm.toLowerCase())
          )
        }));
      case 'driver':
        return Object.entries(groupedAssignments.drivers).map(([key, group]) => ({
          label: group.label,
          options: group.assignments.filter(a => 
            !searchTerm || a.driver_name.toLowerCase().includes(searchTerm.toLowerCase())
          )
        }));
      case 'route':
        return Object.entries(groupedAssignments.routes).map(([key, group]) => ({
          label: group.label,
          options: group.assignments.filter(a => 
            !searchTerm || 
            a.loading_location_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            a.unloading_location_name.toLowerCase().includes(searchTerm.toLowerCase())
          )
        }));
      case 'status':
        return Object.entries(groupedAssignments.statuses).map(([key, group]) => ({
          label: group.label,
          options: group.assignments
        }));
      default:
        return [{
          label: 'All Assignments',
          options: filteredAssignments
        }];
    }
  };

  const handleAssignmentSelect = (assignmentId) => {
    onAssignmentChange(assignmentId);
  };

  const clearFilter = () => {
    setSearchTerm('');
    onAssignmentChange('');
  };

  const getSelectedAssignmentInfo = () => {
    if (!selectedAssignmentId) return null;
    return assignments.find(a => a.id.toString() === selectedAssignmentId.toString());
  };

  const selectedAssignment = getSelectedAssignmentInfo();

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Filter Type Selector */}
      <div className="flex flex-wrap gap-1">
        {[
          { key: 'all', label: 'All', icon: '📋' },
          { key: 'truck', label: 'Truck', icon: '🚚' },
          { key: 'driver', label: 'Driver', icon: '👨‍💼' },
          { key: 'route', label: 'Route', icon: '🛣️' },
          { key: 'status', label: 'Status', icon: '📊' }
        ].map(type => (
          <button
            key={type.key}
            onClick={() => setFilterType(type.key)}
            className={`px-2 py-1 text-xs rounded transition-colors ${
              filterType === type.key
                ? 'bg-primary-100 text-primary-700 border border-primary-300'
                : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
            }`}
            disabled={loading}
          >
            {type.icon} {type.label}
          </button>
        ))}
      </div>

      {/* Search Box */}
      <div className="relative">
        <input
          type="text"
          placeholder={`Search ${filterType === 'all' ? 'assignments' : filterType}...`}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          disabled={loading}
          className="input pl-8 text-sm"
        />
        <svg className="w-4 h-4 absolute left-2.5 top-2.5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>

      {/* Assignment Selector */}
      <div>
        <select
          value={selectedAssignmentId}
          onChange={(e) => handleAssignmentSelect(e.target.value)}
          disabled={loading}
          className="input text-sm"
        >
          <option value="">All Assignments</option>
          {getFilteredOptions().map((group, groupIndex) => (
            <optgroup key={groupIndex} label={group.label}>
              {group.options.slice(0, 50).map((assignment) => ( // Limit to 50 per group for performance
                <option key={assignment.id} value={assignment.id}>
                  {assignment.assignment_code || `ASG-${assignment.id}`} | 
                  {assignment.truck_number} | 
                  {assignment.driver_name}
                </option>
              ))}
              {group.options.length > 50 && (
                <option disabled>... and {group.options.length - 50} more</option>
              )}
            </optgroup>
          ))}
        </select>
      </div>

      {/* Selected Assignment Info */}
      {selectedAssignment && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <div className="font-medium text-blue-900">
                {selectedAssignment.assignment_code || `Assignment #${selectedAssignment.id}`}
              </div>
              <div className="text-sm text-blue-700 space-y-0.5">
                <div>🚚 {selectedAssignment.truck_number} ({selectedAssignment.license_plate})</div>
                <div>👨‍💼 {selectedAssignment.driver_name} ({selectedAssignment.employee_id})</div>
                <div>🛣️ {selectedAssignment.loading_location_name} → {selectedAssignment.unloading_location_name}</div>
                <div>📊 Status: {selectedAssignment.status.replace('_', ' ')}</div>
                {selectedAssignment.priority && (
                  <div>⚡ Priority: {selectedAssignment.priority.charAt(0).toUpperCase() + selectedAssignment.priority.slice(1)}</div>
                )}
              </div>
            </div>
            <button
              onClick={clearFilter}
              className="text-blue-600 hover:text-blue-800 p-1"
              title="Clear assignment filter"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="text-center py-2">
          <div className="inline-flex items-center text-sm text-secondary-600">
            <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-secondary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading assignments...
          </div>
        </div>
      )}

      {/* No Results */}
      {!loading && searchTerm && filteredAssignments.length === 0 && (
        <div className="text-center py-4 text-sm text-secondary-500">
          <svg className="w-8 h-8 mx-auto mb-2 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0118 12a8 8 0 00-8-8 8 8 0 00-8 8c0 2.75 1.387 5.18 3.5 6.65" />
          </svg>
          No assignments found for "{searchTerm}"
        </div>
      )}

      {/* Filter Summary */}
      {!loading && assignments.length > 0 && (
        <div className="text-xs text-secondary-500 flex justify-between">
          <span>
            {filteredAssignments.length} of {assignments.length} assignments
          </span>
          {selectedAssignmentId && (
            <span className="text-primary-600">
              1 selected
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default AssignmentFilter;