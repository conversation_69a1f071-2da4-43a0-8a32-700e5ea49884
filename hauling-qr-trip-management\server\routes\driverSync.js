/**
 * Driver Synchronization API Routes
 * Provides endpoints for managing driver synchronization between shifts and assignments
 */

const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const driverSyncService = require('../services/driverSynchronization');

// @route   POST /api/driver-sync/perform
// @desc    Perform manual driver synchronization
// @access  Private
router.post('/perform', auth, async (req, res) => {
  try {
    console.log('🔄 Manual driver synchronization requested');
    
    const result = await driverSyncService.performSynchronization();
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        data: result.results || []
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        error: result.error?.message
      });
    }
  } catch (error) {
    console.error('Manual synchronization error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform driver synchronization',
      error: error.message
    });
  }
});

// @route   POST /api/driver-sync/truck/:truckId
// @desc    Synchronize specific truck
// @access  Private
router.post('/truck/:truckId', auth, async (req, res) => {
  try {
    const truckId = parseInt(req.params.truckId);
    
    if (!truckId || isNaN(truckId)) {
      return res.status(400).json({
        success: false,
        message: 'Valid truck ID is required'
      });
    }
    
    console.log(`🔄 Manual synchronization requested for truck ID: ${truckId}`);
    
    const result = await driverSyncService.syncSpecificTruck(truckId);
    
    if (result.success) {
      res.json({
        success: true,
        message: `Successfully synchronized ${result.truck_number}`,
        data: result
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Truck synchronization error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to synchronize truck',
      error: error.message
    });
  }
});

// @route   GET /api/driver-sync/status
// @desc    Get synchronization status for all trucks
// @access  Private
router.get('/status', auth, async (req, res) => {
  try {
    const result = await driverSyncService.getSynchronizationStatus();
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Synchronization status retrieved successfully',
        data: result.trucks
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        error: result.error?.message
      });
    }
  } catch (error) {
    console.error('Status retrieval error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve synchronization status',
      error: error.message
    });
  }
});

// @route   POST /api/driver-sync/start
// @desc    Start automatic synchronization service
// @access  Private
router.post('/start', auth, async (req, res) => {
  try {
    driverSyncService.startAutoSync();
    
    res.json({
      success: true,
      message: 'Driver synchronization service started',
      data: {
        isRunning: true,
        intervalMs: driverSyncService.syncIntervalMs
      }
    });
  } catch (error) {
    console.error('Start service error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start synchronization service',
      error: error.message
    });
  }
});

// @route   POST /api/driver-sync/stop
// @desc    Stop automatic synchronization service
// @access  Private
router.post('/stop', auth, async (req, res) => {
  try {
    driverSyncService.stopAutoSync();
    
    res.json({
      success: true,
      message: 'Driver synchronization service stopped',
      data: {
        isRunning: false
      }
    });
  } catch (error) {
    console.error('Stop service error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop synchronization service',
      error: error.message
    });
  }
});

// @route   GET /api/driver-sync/service-status
// @desc    Get synchronization service status
// @access  Private
router.get('/service-status', auth, async (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Service status retrieved successfully',
      data: {
        isRunning: driverSyncService.isRunning,
        intervalMs: driverSyncService.syncIntervalMs,
        lastSync: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Service status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve service status',
      error: error.message
    });
  }
});

module.exports = router;
