const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');

// @route   DELETE /api/admin/cleanup/:table
// @desc    Clean specific database table
// @access  Private (Admin only)
router.delete('/cleanup/:table', auth, async (req, res) => {
  try {
    const { table } = req.params;
    
    // Validate table name to prevent SQL injection
    const allowedTables = ['trip_logs', 'scan_logs', 'assignments', 'migration_log'];
    
    if (!allowedTables.includes(table)) {
      return res.status(400).json({
        error: 'Invalid Table',
        message: `Table ${table} is not allowed for cleanup`
      });
    }

    // Get count before deletion
    const countResult = await query(`SELECT COUNT(*) as count FROM ${table}`);
    const deleted_count = parseInt(countResult.rows[0].count);

    // Delete all records from the table
    await query(`DELETE FROM ${table}`);
    
    // Reset sequences if needed
    if (table === 'trip_logs') {
      await query("ALTER SEQUENCE trip_logs_id_seq RESTART WITH 1");
    } else if (table === 'scan_logs') {
      await query("ALTER SEQUENCE scan_logs_id_seq RESTART WITH 1");
    } else if (table === 'assignments') {
      await query("ALTER SEQUENCE assignments_id_seq RESTART WITH 1");
    } else if (table === 'migration_log') {
      await query("ALTER SEQUENCE migration_log_id_seq RESTART WITH 1");
    }

    res.json({
      success: true,
      message: `${table} cleaned successfully`,
      data: {
        table,
        deleted_count,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error(`Database cleanup error for ${req.params.table}:`, error);
    res.status(500).json({
      error: 'Server Error',
      message: `Failed to cleanup ${req.params.table}`,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/admin/cleanup/status
// @desc    Get cleanup status and table counts
// @access  Private (Admin only)
router.get('/cleanup/status', auth, async (req, res) => {
  try {
    const tables = ['trip_logs', 'scan_logs', 'assignments', 'migration_log'];
    const status = {};

    for (const table of tables) {
      const result = await query(`SELECT COUNT(*) as count FROM ${table}`);
      status[table] = {
        count: parseInt(result.rows[0].count),
        last_updated: new Date().toISOString()
      };
    }

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('Cleanup status error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to get cleanup status'
    });
  }
});

// @route   GET /api/admin/cleanup/test
// @desc    Test endpoint for cleanup functionality
// @access  Private (Admin only)
router.get('/cleanup/test', auth, async (req, res) => {
  try {
    const tables = ['trip_logs', 'scan_logs', 'assignments', 'migration_log'];
    const status = {};

    for (const table of tables) {
      const result = await query(`SELECT COUNT(*) as count FROM ${table}`);
      status[table] = {
        exists: true,
        count: parseInt(result.rows[0].count)
      };
    }

    res.json({
      success: true,
      message: 'Cleanup test successful',
      data: {
        available_tables: tables,
        table_status: status,
        endpoint: '/api/admin/cleanup/:table'
      }
    });

  } catch (error) {
    console.error('Cleanup test error:', error);
    res.status(500).json({
      error: 'Test Error',
      message: error.message
    });
  }
});

// @route   POST /api/admin/clear-cache
// @desc    Clear application caches
// @access  Private (Admin only)
router.post('/clear-cache', auth, async (req, res) => {
  try {
    const { type } = req.body;
    
    const allowedTypes = ['memory', 'shift', 'analytics', 'all'];
    
    if (!allowedTypes.includes(type)) {
      return res.status(400).json({
        error: 'Invalid Type',
        message: `Cache type ${type} is not supported`
      });
    }

    let result = {
      type,
      cleared: [],
      timestamp: new Date().toISOString()
    };

    // Clear memory caches
    if (type === 'memory' || type === 'all') {
      try {
        // Import ShiftDisplayHelper if available
        const ShiftDisplayHelper = require('../utils/ShiftDisplayHelper');
        if (ShiftDisplayHelper && ShiftDisplayHelper.clearAllCache) {
          ShiftDisplayHelper.clearAllCache();
          result.cleared.push('shift-display-cache');
        }
        
        // Clear any other memory caches
        result.cleared.push('memory-cache');
      } catch (e) {
        console.warn('Could not clear shift display cache:', e.message);
      }
    }

    // Clear shift-specific caches
    if (type === 'shift' || type === 'all') {
      try {
        const ShiftDisplayHelper = require('../utils/ShiftDisplayHelper');
        if (ShiftDisplayHelper && ShiftDisplayHelper.clearAllCache) {
          ShiftDisplayHelper.clearAllCache();
          result.cleared.push('shift-display-cache');
        }
        result.cleared.push('shift-cache');
      } catch (e) {
        console.warn('Could not clear shift cache:', e.message);
      }
    }

    // Clear analytics caches
    if (type === 'analytics' || type === 'all') {
      // Analytics cache is client-side, but we can clear server-side if any
      result.cleared.push('analytics-cache');
    }

    res.json({
      success: true,
      message: `${type} cache cleared successfully`,
      data: result
    });

  } catch (error) {
    console.error('Cache clear error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to clear cache',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/admin/cache-status
// @desc    Get cache status and statistics
// @access  Private (Admin only)
router.get('/cache-status', auth, async (req, res) => {
  try {
    let cacheInfo = {
      memory: {
        shiftDisplayCache: 0,
        timestamp: new Date().toISOString()
      },
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage()
      }
    };

    // Get ShiftDisplayHelper cache info if available
    try {
      const ShiftDisplayHelper = require('../utils/ShiftDisplayHelper');
      if (ShiftDisplayHelper && ShiftDisplayHelper.getCacheInfo) {
        cacheInfo.memory.shiftDisplayCache = ShiftDisplayHelper.getCacheInfo();
      }
    } catch (e) {
      cacheInfo.memory.shiftDisplayCache = 'not-available';
    }

    res.json({
      success: true,
      data: cacheInfo
    });

  } catch (error) {
    console.error('Cache status error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to get cache status'
    });
  }
});

module.exports = router;