const { query } = require('../config/database');
const { getServerConfig } = require('../config/unified-config');

// Constants for better maintainability
const SHIFT_STATUSES = {
  SCHEDULED: 'scheduled',
  ACTIVE: 'active',
  COMPLETED: 'completed'
};

const ISSUE_SEVERITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

// Environment-based default intervals
const getDefaultCheckInterval = () => {
  const config = getServerConfig();
  return parseInt(process.env.SHIFT_SYNC_MONITOR_INTERVAL) ||
         (config.IS_PRODUCTION ? 180000 : 30000); // 3 min prod, 30 sec dev
};

class SimpleShiftSyncMonitor {
  constructor(checkInterval = null) {
    this.isRunning = false;
    this.monitoringInterval = null;
    this.config = getServerConfig();
    this.checkInterval = this._validateCheckInterval(checkInterval || getDefaultCheckInterval());
    this.lastCheck = null;
    this.issues = [];
    this.cleanChecks = 0; // Track consecutive clean checks
    this.lastIssueHash = null; // For issue deduplication
  }

  /**
   * Validate check interval parameter
   * @param {number} interval - Check interval in milliseconds
   * @returns {number} Validated interval
   */
  _validateCheckInterval(interval) {
    const defaultInterval = getDefaultCheckInterval();
    if (typeof interval !== 'number' || interval < 5000 || interval > 300000) {
      console.warn('SHIFT_SYNC_MONITOR', `Invalid check interval ${interval}ms, using default ${defaultInterval}ms`);
      return defaultInterval;
    }
    return interval;
  }

  /**
   * Generate SQL condition for checking if current time is within shift hours
   * @returns {string} SQL WHERE condition
   */
  _getShiftTimeCondition() {
    return `(
      -- Handle NULL end_time for active shifts (always match)
      ds.end_time IS NULL
      OR
      -- Handle overnight shifts (end_time < start_time)
      (ds.end_time < ds.start_time AND
       (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
      OR
      -- Handle same-day shifts (end_time >= start_time)
      (ds.end_time >= ds.start_time AND
       CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
    )`;
  }

  /**
   * Generate base SELECT clause for shift queries
   * @returns {string} SQL SELECT clause
   */
  _getShiftSelectClause() {
    return `
      SELECT
        ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status,
        dt.truck_number, d.full_name,
        ds.start_time, ds.end_time,
        ds.start_date, ds.end_date
      FROM driver_shifts ds
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      JOIN drivers d ON ds.driver_id = d.id
    `;
  }

  /**
   * Start the synchronization monitoring
   */
  start() {
    if (this.isRunning) {
      console.log('SHIFT_SYNC_MONITOR', 'Monitor already running');
      return;
    }

    this.isRunning = true;
    console.log('SHIFT_SYNC_MONITOR', 'Starting shift synchronization monitoring');

    // Run initial check
    this.performSyncCheck();

    // Set up periodic monitoring
    this.monitoringInterval = setInterval(() => {
      this.performSyncCheck();
    }, this.checkInterval);
  }

  /**
   * Stop the synchronization monitoring
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    console.log('SHIFT_SYNC_MONITOR', 'Stopped shift synchronization monitoring');
  }

  /**
   * Perform synchronization check between systems
   */
  async performSyncCheck() {
    try {
      this.lastCheck = new Date();
      const issues = [];

      // 1. Check for scheduled shifts that should be active
      const scheduledShiftsResult = await this._getScheduledShiftsNeedingActivation();

      if (scheduledShiftsResult.rows.length > 0) {
        issues.push({
          type: 'scheduled_shifts_need_activation',
          severity: ISSUE_SEVERITIES.MEDIUM,
          count: scheduledShiftsResult.rows.length,
          shifts: scheduledShiftsResult.rows.map(shift => this._sanitizeShiftData(shift)),
          message: `${scheduledShiftsResult.rows.length} scheduled shift(s) should be auto-activated`
        });

        // Auto-fix: Activate the shifts
        await this.autoActivateShifts(scheduledShiftsResult.rows);
      }

      // 2. Check for orphaned active shifts (active shifts without time validity)
      const orphanedShiftsResult = await this._getOrphanedActiveShifts();

      if (orphanedShiftsResult.rows.length > 0) {
        issues.push({
          type: 'orphaned_active_shifts',
          severity: ISSUE_SEVERITIES.HIGH,
          count: orphanedShiftsResult.rows.length,
          shifts: orphanedShiftsResult.rows.map(shift => this._sanitizeShiftData(shift)),
          message: `${orphanedShiftsResult.rows.length} active shift(s) are outside their valid time range`
        });

        // Auto-fix: Complete the orphaned shifts
        await this.completeOrphanedShifts(orphanedShiftsResult.rows);
      }

      // Update issues list
      this.issues = issues;

      // Reset error tracking on successful check
      this._resetErrorTracking();

      // Log summary
      if (issues.length > 0) {
        console.log('SHIFT_SYNC_MONITOR', `Found ${issues.length} synchronization issue(s):`);
        issues.forEach(issue => {
          console.log(`  - ${issue.type}: ${issue.message} (${issue.severity})`);
        });
      }

    } catch (error) {
      const errorContext = {
        timestamp: new Date(),
        lastCheck: this.lastCheck,
        checkInterval: this.checkInterval,
        errorType: error.constructor.name,
        stack: error.stack
      };
      
      console.error('SHIFT_SYNC_MONITOR', 'Error during sync check:', {
        message: error.message,
        context: errorContext
      });
      
      this.issues.push({
        type: 'monitor_error',
        severity: ISSUE_SEVERITIES.HIGH,
        message: `Monitoring error: ${error.message}`,
        timestamp: new Date(),
        context: errorContext
      });

      // Implement exponential backoff for repeated failures
      this._handleMonitoringError(error);
    }
  }

  /**
   * Auto-activate shifts that should be active
   * FIXED: Respects manual-only completion requirement - deactivated shifts become 'scheduled', not 'completed'
   */
  async autoActivateShifts(shifts) {
    const results = [];
    
    for (const shift of shifts) {
      try {
        const result = await this._executeShiftActivation(shift);
        results.push({ success: true, shiftId: shift.id, result });
      } catch (error) {
        console.error('SHIFT_SYNC_MONITOR', `Failed to activate shift ${shift.id}:`, error);
        results.push({ success: false, shiftId: shift.id, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;
    
    console.log('SHIFT_SYNC_MONITOR', `Shift activation summary: ${successCount} successful, ${failureCount} failed`);
    return results;
  }

  /**
   * Execute shift activation within a transaction
   * @param {Object} shift - Shift to activate
   * @returns {Promise<Object>} Activation result
   */
  async _executeShiftActivation(shift) {
    return await this._executeInTransaction(async () => {
      // 1. Deactivate any currently active shifts for this truck
      const deactivatedResult = await query(`
        UPDATE driver_shifts
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE truck_id = $2 AND status = $3 AND id != $4
        RETURNING id
      `, [SHIFT_STATUSES.SCHEDULED, shift.truck_id, SHIFT_STATUSES.ACTIVE, shift.id]);

      // 2. Activate the scheduled shift
      const activatedResult = await query(`
        UPDATE driver_shifts
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING id
      `, [SHIFT_STATUSES.ACTIVE, shift.id]);

      // 3. Sync with Assignment Management
      await this.syncWithAssignmentManagement(shift.id, SHIFT_STATUSES.ACTIVE);

      console.log('SHIFT_SYNC_MONITOR', `Auto-activated shift ${shift.id} for ${shift.truck_number} - ${shift.full_name}`);
      
      return {
        deactivatedShifts: deactivatedResult.rows.length,
        activatedShift: activatedResult.rows.length > 0
      };
    });
  }

  /**
   * Handle orphaned active shifts
   * FIXED: Respects manual-only completion requirement - orphaned shifts become 'scheduled', not 'completed'
   */
  async completeOrphanedShifts(shifts) {
    const results = [];
    
    for (const shift of shifts) {
      try {
        const result = await this._executeOrphanedShiftHandling(shift);
        results.push({ success: true, shiftId: shift.id, result });
      } catch (error) {
        console.error('SHIFT_SYNC_MONITOR', `Failed to handle orphaned shift ${shift.id}:`, error);
        results.push({ success: false, shiftId: shift.id, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;
    
    console.log('SHIFT_SYNC_MONITOR', `Orphaned shift handling summary: ${successCount} successful, ${failureCount} failed`);
    return results;
  }

  /**
   * Execute orphaned shift handling within a transaction
   * @param {Object} shift - Orphaned shift to handle
   * @returns {Promise<Object>} Handling result
   */
  async _executeOrphanedShiftHandling(shift) {
    return await this._executeInTransaction(async () => {
      // 1. Set orphaned shift to scheduled (respects manual-only completion)
      const result = await query(`
        UPDATE driver_shifts
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING id
      `, [SHIFT_STATUSES.SCHEDULED, shift.id]);

      // 2. Sync with Assignment Management
      await this.syncWithAssignmentManagement(shift.id, SHIFT_STATUSES.SCHEDULED);

      console.log('SHIFT_SYNC_MONITOR', `Set orphaned shift ${shift.id} to scheduled for ${shift.truck_number} - ${shift.full_name}`);
      
      return {
        updatedShift: result.rows.length > 0
      };
    });
  }

  /**
   * Execute a function within a database transaction
   * @param {Function} fn - Function to execute within transaction
   * @returns {Promise<any>} Function result
   */
  async _executeInTransaction(fn) {
    await query('BEGIN');
    try {
      const result = await fn();
      await query('COMMIT');
      return result;
    } catch (error) {
      await query('ROLLBACK');
      throw error;
    }
  }

  /**
   * Get current synchronization status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastCheck: this.lastCheck,
      checkInterval: this.checkInterval,
      issueCount: this.issues.length,
      issues: this.issues,
      uptime: this.isRunning ? Date.now() - (this.lastCheck?.getTime() || Date.now()) : 0
    };
  }

  /**
   * Force a manual synchronization check
   */
  async forceCheck() {
    console.log('SHIFT_SYNC_MONITOR', 'Forcing manual synchronization check');
    await this.performSyncCheck();
    return this.getStatus();
  }

  /**
   * Clear all recorded issues
   */
  clearIssues() {
    this.issues = [];
    console.log('SHIFT_SYNC_MONITOR', 'Cleared all recorded issues');
  }

  /**
   * Handle monitoring errors with exponential backoff
   * @param {Error} error - The error that occurred
   */
  _handleMonitoringError(error) {
    // Count consecutive errors for backoff calculation
    if (!this.consecutiveErrors) {
      this.consecutiveErrors = 0;
    }
    this.consecutiveErrors++;

    // Implement exponential backoff (max 5 minutes)
    const backoffDelay = Math.min(this.checkInterval * Math.pow(2, this.consecutiveErrors - 1), 300000);
    
    if (this.consecutiveErrors > 3) {
      console.warn('SHIFT_SYNC_MONITOR', `Multiple consecutive errors (${this.consecutiveErrors}), applying backoff delay: ${backoffDelay}ms`);
      
      // Temporarily increase check interval
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = setInterval(() => {
          this.performSyncCheck();
        }, backoffDelay);
      }
    }
  }

  /**
   * Reset error tracking on successful check
   */
  _resetErrorTracking() {
    if (this.consecutiveErrors > 0) {
      console.log('SHIFT_SYNC_MONITOR', 'Sync check successful, resetting error tracking');
      this.consecutiveErrors = 0;
      
      // Reset to normal check interval
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = setInterval(() => {
          this.performSyncCheck();
        }, this.checkInterval);
      }
    }
  }

  /**
   * Get scheduled shifts that need activation
   * @returns {Promise<Object>} Query result with shifts
   */
  async _getScheduledShiftsNeedingActivation() {
    const sql = `
      ${this._getShiftSelectClause()}
      WHERE ds.status = $1
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        AND ${this._getShiftTimeCondition()}
    `;
    return await query(sql, [SHIFT_STATUSES.SCHEDULED]);
  }

  /**
   * Get orphaned active shifts (outside valid time range)
   * @returns {Promise<Object>} Query result with shifts
   */
  async _getOrphanedActiveShifts() {
    const sql = `
      ${this._getShiftSelectClause()}
      WHERE ds.status = $1
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        AND NOT ${this._getShiftTimeCondition()}
    `;
    return await query(sql, [SHIFT_STATUSES.ACTIVE]);
  }

  /**
   * Sanitize shift data for logging and issue reporting
   * @param {Object} shift - Raw shift data from database
   * @returns {Object} Sanitized shift data
   */
  _sanitizeShiftData(shift) {
    return {
      id: parseInt(shift.id),
      truck_id: parseInt(shift.truck_id),
      driver_id: parseInt(shift.driver_id),
      shift_type: String(shift.shift_type).replace(/[<>]/g, ''),
      status: String(shift.status).replace(/[<>]/g, ''),
      truck_number: String(shift.truck_number).replace(/[<>]/g, ''),
      full_name: String(shift.full_name).replace(/[<>]/g, ''),
      start_time: shift.start_time,
      end_time: shift.end_time,
      start_date: shift.start_date,
      end_date: shift.end_date
    };
  }

  /**
   * Synchronize shift status with Assignment Management system
   */
  async syncWithAssignmentManagement(shiftId, newStatus) {
    try {
      // Add your Assignment Management API call here
      // Example:
      // await assignmentManagementClient.updateShiftStatus(shiftId, newStatus);
      console.log('SHIFT_SYNC_MONITOR', `Synced shift ${shiftId} status (${newStatus}) with Assignment Management`);
    } catch (error) {
      console.error('SHIFT_SYNC_MONITOR', 'Failed to sync with Assignment Management:', error);
      throw error;
    }
  }
}

// Export singleton instance
const simpleShiftSyncMonitor = new SimpleShiftSyncMonitor();
module.exports = simpleShiftSyncMonitor;
