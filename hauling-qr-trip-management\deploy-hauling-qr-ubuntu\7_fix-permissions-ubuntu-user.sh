#!/bin/bash

# =============================================================================
# Hauling QR System - Fix Permissions and Ubuntu User Setup
# =============================================================================
# This script fixes file permissions and ensures the application runs as 
# ubuntu user instead of root, fixing PM2 monitoring issues after restart.
#
# Usage: sudo ./fix-permissions-ubuntu-user.sh
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/hauling-qr-system"
UBUNTU_USER="ubuntu"
UBUNTU_HOME="/home/<USER>"

echo -e "${BLUE}🔧 Hauling QR System - Permission Fix Script${NC}"
echo -e "${BLUE}=============================================${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check if ubuntu user exists
if ! id "$UBUNTU_USER" &>/dev/null; then
    print_error "Ubuntu user does not exist"
    exit 1
fi

# Check if application directory exists
if [[ ! -d "$APP_DIR" ]]; then
    print_error "Application directory $APP_DIR does not exist"
    exit 1
fi

echo -e "${YELLOW}🛑 Step 1: Stopping all processes${NC}"

# Stop any running Node.js processes
print_status "Stopping all Node.js processes..."
pkill -f "node.*server" 2>/dev/null || true
sleep 2

# Stop PM2 processes for all users
print_status "Stopping PM2 processes..."
sudo -u $UBUNTU_USER pm2 kill 2>/dev/null || true
pm2 kill 2>/dev/null || true
sleep 2

# Kill any remaining processes on port 8080 (Cloudflare compatible)
print_status "Freeing port 8080..."
fuser -k 8080/tcp 2>/dev/null || true
sleep 1

echo ""
echo -e "${YELLOW}🔧 Step 2: Fixing file permissions${NC}"

# Fix application directory ownership
print_status "Setting ownership of application directory to ubuntu user..."
chown -R $UBUNTU_USER:$UBUNTU_USER $APP_DIR

# Fix application directory permissions
print_status "Setting proper permissions for application directory..."
chmod -R 755 $APP_DIR

# Make server.js executable
chmod +x $APP_DIR/server/server.js

# Fix PM2 directory ownership
print_status "Fixing PM2 directory ownership..."
if [[ -d "$UBUNTU_HOME/.pm2" ]]; then
    chown -R $UBUNTU_USER:$UBUNTU_USER $UBUNTU_HOME/.pm2
    chmod -R 755 $UBUNTU_HOME/.pm2
fi

# Create PM2 directory if it doesn't exist
if [[ ! -d "$UBUNTU_HOME/.pm2" ]]; then
    sudo -u $UBUNTU_USER mkdir -p $UBUNTU_HOME/.pm2/logs
    chown -R $UBUNTU_USER:$UBUNTU_USER $UBUNTU_HOME/.pm2
fi

# Fix node_modules permissions if they exist
if [[ -d "$APP_DIR/node_modules" ]]; then
    print_status "Fixing node_modules permissions..."
    chown -R $UBUNTU_USER:$UBUNTU_USER $APP_DIR/node_modules
fi

if [[ -d "$APP_DIR/server/node_modules" ]]; then
    chown -R $UBUNTU_USER:$UBUNTU_USER $APP_DIR/server/node_modules
fi

if [[ -d "$APP_DIR/client/node_modules" ]]; then
    chown -R $UBUNTU_USER:$UBUNTU_USER $APP_DIR/client/node_modules
fi

echo ""
echo -e "${YELLOW}🚀 Step 3: Setting up PM2 for ubuntu user${NC}"

# Remove any existing PM2 startup scripts for root
print_status "Removing root PM2 startup configuration..."
systemctl disable pm2-root 2>/dev/null || true
rm -f /etc/systemd/system/pm2-root.service 2>/dev/null || true

# Check if optimized ecosystem.config.js exists (from Phase 11)
ECOSYSTEM_FILE="$APP_DIR/ecosystem.config.js"
if [[ -f "$ECOSYSTEM_FILE" ]] && grep -q "instances: 4" "$ECOSYSTEM_FILE" 2>/dev/null; then
    print_status "Found optimized ecosystem.config.js from Phase 11 - preserving it"
    # Ensure proper ownership of existing optimized config
    chown $UBUNTU_USER:$UBUNTU_USER $ECOSYSTEM_FILE
elif [[ ! -f "$ECOSYSTEM_FILE" ]]; then
    print_status "Creating basic ecosystem.config.js (will be optimized in Phase 11)..."
    cat > $ECOSYSTEM_FILE << 'EOF'
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: '/var/www/hauling-qr-system',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      BACKEND_HTTP_PORT: 8080,
      SERVER_PORT: 8080
    },
    log_file: '/home/<USER>/.pm2/logs/hauling-qr-server.log',
    out_file: '/home/<USER>/.pm2/logs/hauling-qr-server-out.log',
    error_file: '/home/<USER>/.pm2/logs/hauling-qr-server-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'logs', '.git'],
    restart_delay: 1000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF
    chown $UBUNTU_USER:$UBUNTU_USER $ECOSYSTEM_FILE
    print_status "Basic ecosystem.config.js created (Phase 11 will optimize it)"
else
    print_status "Existing ecosystem.config.js found - ensuring proper ownership"
    chown $UBUNTU_USER:$UBUNTU_USER $ECOSYSTEM_FILE
fi

# Start PM2 as ubuntu user (only if not already optimized)
print_status "Starting PM2 as ubuntu user..."
cd $APP_DIR

# Check if this is an optimized config (4 instances = Phase 11 already ran)
if grep -q "instances: 4" "$ECOSYSTEM_FILE" 2>/dev/null; then
    print_status "Detected optimized PM2 config - restarting with cluster mode..."
    sudo -u $UBUNTU_USER pm2 kill >/dev/null 2>&1 || true
    sudo -u $UBUNTU_USER pm2 start ecosystem.config.js --env production
else
    print_status "Starting with basic PM2 config (Phase 11 will optimize later)..."
    sudo -u $UBUNTU_USER pm2 start ecosystem.config.js
fi

# Set up PM2 startup for ubuntu user
print_status "Configuring PM2 startup for ubuntu user..."
STARTUP_CMD=$(sudo -u $UBUNTU_USER pm2 startup systemd -u $UBUNTU_USER --hp $UBUNTU_HOME | grep "sudo env" | head -1)
if [[ -n "$STARTUP_CMD" ]]; then
    eval $STARTUP_CMD
    print_status "PM2 startup script installed"
else
    print_warning "Could not generate PM2 startup command automatically"
fi

# Save PM2 processes
sudo -u $UBUNTU_USER pm2 save

echo ""
echo -e "${YELLOW}🔍 Step 4: Verification${NC}"

# Check process ownership
print_status "Checking process ownership..."
sleep 3
PROCESS_USER=$(ps aux | grep "node.*server" | grep -v grep | awk '{print $1}' | head -1)
if [[ "$PROCESS_USER" == "$UBUNTU_USER" ]]; then
    print_status "Node.js process is running as ubuntu user ✓"
else
    print_warning "Node.js process owner: $PROCESS_USER (should be ubuntu)"
fi

# Check PM2 status
print_status "Checking PM2 status..."
sudo -u $UBUNTU_USER pm2 status

# Check application health
print_status "Testing application health..."
sleep 2
if curl -f http://localhost:8080/health >/dev/null 2>&1; then
    print_status "Application is responding ✓"
else
    print_warning "Application health check failed"
fi

# Check file ownership
print_status "Verifying file ownership..."
APP_OWNER=$(stat -c '%U' $APP_DIR)
if [[ "$APP_OWNER" == "$UBUNTU_USER" ]]; then
    print_status "Application directory owned by ubuntu user ✓"
else
    print_warning "Application directory owner: $APP_OWNER (should be ubuntu)"
fi

echo ""
echo -e "${GREEN}🎉 Permission fix completed!${NC}"
echo ""
echo -e "${BLUE}📋 Summary:${NC}"
echo -e "   • Application directory: $APP_DIR"
echo -e "   • Owner: $UBUNTU_USER:$UBUNTU_USER"
echo -e "   • PM2 configured for ubuntu user"
echo -e "   • Startup script installed"
echo ""
echo -e "${BLUE}📝 Next steps:${NC}"
echo -e "   • Check PM2 status: ${YELLOW}pm2 status${NC}"
echo -e "   • View logs: ${YELLOW}pm2 logs hauling-qr-server${NC}"
echo -e "   • Monitor processes: ${YELLOW}pm2 monit${NC}"
echo -e "   • Test application: ${YELLOW}curl http://localhost:8080/health${NC}"
echo ""
echo -e "${GREEN}✅ The application should now persist correctly after reboots!${NC}"