import React, { useEffect, useState } from 'react';
import { usePWAStatus } from '../hooks/usePWAStatus';

/**
 * Test component to verify enhanced PWA status hook functionality
 * This component demonstrates the enhanced service worker communication
 */
const PWAStatusTest = () => {
  const pwaStatus = usePWAStatus();
  const [testResults, setTestResults] = useState([]);
  const [isTestRunning, setIsTestRunning] = useState(false);

  const addTestResult = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, { message, type, timestamp }]);
  };

  const runPWAStatusTest = async () => {
    setIsTestRunning(true);
    setTestResults([]);
    
    addTestResult('Starting PWA Status Hook Communication Test', 'info');
    
    // Test 1: Check PWA mode detection
    addTestResult(`PWA Mode Detected: ${pwaStatus.isPWA}`, pwaStatus.isPWA ? 'success' : 'warning');
    
    // Test 2: Check service worker availability
    if ('serviceWorker' in navigator) {
      addTestResult('Service Worker API Available: ✓', 'success');
      
      if (navigator.serviceWorker.controller) {
        addTestResult('Service Worker Controller Active: ✓', 'success');
        
        // Test 3: Send test message to service worker
        try {
          navigator.serviceWorker.controller.postMessage({
            type: 'PWA_STATUS_TEST',
            testData: {
              timestamp: new Date().toISOString(),
              isPWA: pwaStatus.isPWA,
              currentPath: window.location.pathname,
              source: 'pwa-status-test-component'
            }
          });
          addTestResult('Test message sent to service worker: ✓', 'success');
        } catch (error) {
          addTestResult(`Failed to send test message: ${error.message}`, 'error');
        }
      } else {
        addTestResult('Service Worker Controller Not Available: ✗', 'error');
      }
    } else {
      addTestResult('Service Worker API Not Available: ✗', 'error');
    }
    
    // Test 4: Check online status
    addTestResult(`Online Status: ${pwaStatus.isOnline ? 'Online' : 'Offline'}`, 
                  pwaStatus.isOnline ? 'success' : 'warning');
    
    // Test 5: Check sync status
    addTestResult(`Sync Status: ${pwaStatus.syncStatus}`, 
                  pwaStatus.syncStatus === 'synced' ? 'success' : 'warning');
    
    // Test 6: Check queued connections
    addTestResult(`Queued Connections: ${pwaStatus.queuedConnections}`, 
                  pwaStatus.queuedConnections === 0 ? 'success' : 'info');
    
    addTestResult('PWA Status Hook Communication Test Completed', 'info');
    setIsTestRunning(false);
  };

  // Listen for service worker messages during test
  useEffect(() => {
    if (!isTestRunning) return;

    const handleServiceWorkerMessage = (event) => {
      if (event.data && event.data.type === 'PWA_MODE_CONFIRMATION') {
        addTestResult(`Service Worker Confirmed PWA Mode: ${event.data.confirmedMode}`, 'success');
      } else if (event.data && event.data.type === 'SERVICE_WORKER_READY') {
        addTestResult('Service Worker Ready Notification Received: ✓', 'success');
      }
    };

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
      
      return () => {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      };
    }
  }, [isTestRunning]);

  const getResultColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">
        PWA Status Hook Communication Test
      </h2>
      
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Current PWA Status</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">PWA Mode:</span> 
            <span className={pwaStatus.isPWA ? 'text-green-600' : 'text-red-600'}>
              {pwaStatus.isPWA ? 'Yes' : 'No'}
            </span>
          </div>
          <div>
            <span className="font-medium">Online:</span> 
            <span className={pwaStatus.isOnline ? 'text-green-600' : 'text-red-600'}>
              {pwaStatus.isOnline ? 'Yes' : 'No'}
            </span>
          </div>
          <div>
            <span className="font-medium">Sync Status:</span> 
            <span className="text-blue-600">{pwaStatus.syncStatus}</span>
          </div>
          <div>
            <span className="font-medium">Queued:</span> 
            <span className="text-blue-600">{pwaStatus.queuedConnections}</span>
          </div>
        </div>
      </div>

      <button
        onClick={runPWAStatusTest}
        disabled={isTestRunning}
        className={`w-full py-2 px-4 rounded-lg font-medium ${
          isTestRunning
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
      >
        {isTestRunning ? 'Running Test...' : 'Run PWA Status Communication Test'}
      </button>

      {testResults.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Test Results</h3>
          <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="mb-2 text-sm">
                <span className="text-gray-500">[{result.timestamp}]</span>
                <span className={`ml-2 ${getResultColor(result.type)}`}>
                  {result.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PWAStatusTest;