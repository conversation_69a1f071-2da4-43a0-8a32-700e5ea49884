/**
 * Enhanced Shift Status Service
 * Purpose: Comprehensive shift status management with automated transitions
 * Features: Real-time status evaluation, overnight logic, cross-system integration
 * 
 * This service implements the business requirements:
 * - Rule 1: Active - within date range AND within time window
 * - Rule 2: Scheduled - within date range BUT outside time window  
 * - Rule 3: Completed - past end date and end time
 * - Enhanced overnight logic for night shifts
 * - Cross-system consistency with Assignment Management
 */

const { getClient } = require('../config/database');
const { getServerConfig } = require('../config/unified-config');

class EnhancedShiftStatusService {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.config = getServerConfig();

    // Environment-based update interval
    this.updateInterval = parseInt(process.env.ENHANCED_SHIFT_STATUS_INTERVAL) ||
                         (this.config.IS_PRODUCTION ? 300000 : 60000); // 5 min prod, 1 min dev

    this.logger = console;
    this.performanceTarget = 300; // 300ms target
    this.cleanUpdates = 0; // Track consecutive clean updates
  }

  /**
   * Start the automated shift status monitoring service
   */
  async start() {
    if (this.isRunning) {
      this.logger.warn('Enhanced shift status service is already running');
      return;
    }

    this.logger.info('🚀 Starting Enhanced Shift Status Service');
    this.isRunning = true;

    // Run immediately, then every minute
    await this.runStatusUpdates();
    this.intervalId = setInterval(async () => {
      await this.runStatusUpdates();
    }, this.updateInterval);
  }

  /**
   * Stop the automated shift status monitoring service
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('🛑 Stopping Enhanced Shift Status Service');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Run comprehensive status updates with performance monitoring
   */
  async runStatusUpdates() {
    if (!this.isRunning) {
      return;
    }

    const startTime = Date.now();
    let client;

    try {
      client = await getClient();

      // Execute the enhanced status update function with explicit parameter
      const result = await client.query(`
        SELECT * FROM update_all_shift_statuses(CURRENT_TIMESTAMP)
      `);

      const stats = result.rows[0];
      const duration = Date.now() - startTime;

      // Log significant updates
      if (stats.updated_count > 0) {
        this.logger.info('✅ Shift status updates completed', {
          updated: stats.updated_count,
          activated: stats.activated_count,
          completed: stats.completed_count,
          scheduled: stats.scheduled_count,
          duration_ms: duration
        });
      }

      // Performance monitoring
      if (duration > this.performanceTarget) {
        this.logger.warn('⚠️ Performance target exceeded', {
          duration_ms: duration,
          target_ms: this.performanceTarget
        });
      }

      // Trigger cross-system synchronization if there were activations
      if (stats.activated_count > 0) {
        await this.syncWithAssignmentManagement();
      }

    } catch (error) {
      this.logger.error('❌ Shift status update error:', error);
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Evaluate status for a specific shift
   */
  async evaluateShiftStatus(shiftId, referenceTimestamp = null) {
    let client;

    try {
      client = await getClient();

      const result = await client.query(`
        SELECT evaluate_shift_status($1) as status
      `, [shiftId]);

      return result.rows[0]?.status || 'error';

    } catch (error) {
      this.logger.error('Error evaluating shift status:', error);
      return 'error';
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Update status for a specific shift with detailed response
   */
  async updateShiftStatus(shiftId, referenceTimestamp = null) {
    let client;

    try {
      client = await getClient();

      const result = await client.query(`
        SELECT * FROM update_shift_status($1)
      `, [shiftId]);

      return result.rows[0] || null;

    } catch (error) {
      this.logger.error('Error updating shift status:', error);
      return null;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Get comprehensive shift status summary for monitoring
   */
  async getStatusSummary(referenceTimestamp = null) {
    let client;

    try {
      client = await getClient();

      const result = await client.query(`
        SELECT * FROM get_shift_status_summary()
      `);

      return result.rows[0] || null;

    } catch (error) {
      this.logger.error('Error getting status summary:', error);
      return null;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Test shift time logic for validation
   */
  async testShiftTimeLogic(startTime, endTime, testTime, isOvernight = null) {
    let client;

    try {
      client = await getClient();

      const result = await client.query(`
        SELECT * FROM test_shift_time_logic($1, $2, $3, $4)
      `, [startTime, endTime, testTime, isOvernight]);

      return result.rows[0] || null;

    } catch (error) {
      this.logger.error('Error testing shift time logic:', error);
      return null;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Synchronize with Assignment Management system
   */
  async syncWithAssignmentManagement() {
    try {
      // Import the driver synchronization service
      const driverSync = require('./driverSynchronization');

      // Trigger synchronization for all trucks with active shifts using the correct method name
      const result = await driverSync.performSynchronization();

      this.logger.info('🔄 Assignment Management sync completed', {
        success: result.success,
        message: result.message,
        synchronized_trucks: result.results?.length || 0,
        total_assignments_updated: result.results?.reduce((sum, r) => sum + (r.assignments_updated || 0), 0) || 0
      });

    } catch (error) {
      this.logger.error('❌ Assignment Management sync failed:', error);
    }
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return {
      service_name: 'EnhancedShiftStatusService',
      is_running: this.isRunning,
      update_interval_ms: this.updateInterval,
      performance_target_ms: this.performanceTarget,
      last_check: new Date().toISOString()
    };
  }

  /**
   * Force immediate status update cycle
   */
  async forceUpdate() {
    this.logger.info('🔄 Force updating all shift statuses');
    await this.runStatusUpdates();
  }
}

// Export singleton instance
const enhancedShiftStatusService = new EnhancedShiftStatusService();
module.exports = enhancedShiftStatusService;
