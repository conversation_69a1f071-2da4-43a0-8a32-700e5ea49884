import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { AVAILABLE_PAGES } from '../../hooks/usePermissions';
import ProtectedRoute from '../common/ProtectedRoute';
import Sidebar from './Sidebar';
import Header from './Header';
import Footer from './Footer';

// Lazy-loaded page components
const Dashboard = React.lazy(() => import('../../pages/dashboard/Dashboard'));
const UsersManagement = React.lazy(() => import('../../pages/users/UsersManagement'));
const TrucksManagement = React.lazy(() => import('../../pages/trucks/TrucksManagement'));
const DriversManagement = React.lazy(() => import('../../pages/drivers/DriversManagement'));
const DriverAttendance = React.lazy(() => import('../../pages/drivers/DriverAttendance'));
const LocationsManagement = React.lazy(() => import('../../pages/locations/LocationsManagement'));
const AssignmentsManagement = React.lazy(() => import('../../pages/assignments/AssignmentsManagement'));
const TripMonitoring = React.lazy(() => import('../../pages/trips/TripMonitoring'));
const QRScanner = React.lazy(() => import('../../pages/scanner/QRScanner'));
const UnifiedAnalytics = React.lazy(() => import('../../pages/analytics/UnifiedAnalytics'));
const AssignmentMonitoring = React.lazy(() => import('../../pages/assignment-monitoring/AssignmentMonitoring'));
const SimplifiedShiftManagement = React.lazy(() => import('../../pages/shifts/SimplifiedShiftManagement'));
const ShiftAssignmentIntegration = React.lazy(() => import('../../pages/shifts/ShiftAssignmentIntegration'));

const Settings = React.lazy(() => import('../../pages/settings/Settings'));
const TruckTripSummary = React.lazy(() => import('../../pages/trucks/TruckTripSummary'));
const FleetResourceMonitor = React.lazy(() => import('../../pages/fleet/FleetResourceMonitor'));

const DashboardLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  // Initialize sidebar hidden state from localStorage, default to false
  const [sidebarHidden, setSidebarHidden] = useState(() => {
    const saved = localStorage.getItem('sidebarHidden');
    return saved ? JSON.parse(saved) : false;
  });
  const { user } = useAuth();

  // Save sidebar hidden state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarHidden', JSON.stringify(sidebarHidden));
  }, [sidebarHidden]);

  return (
    <div className="min-h-screen bg-secondary-50 flex">
      {/* Sidebar - conditionally rendered */}
      {!sidebarHidden && <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />}
      
      {/* Main Content */}
      <div className={`flex-1 flex flex-col ${!sidebarHidden ? 'lg:ml-64' : ''}`}>        {/* Header */}
        <Header
          user={user}
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          sidebarHidden={sidebarHidden}
          setSidebarHidden={setSidebarHidden}
        />
          {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto flex flex-col">
          <div className="flex-1 py-6">
            <div className={`mx-auto px-4 sm:px-6 lg:px-8 ${sidebarHidden ? 'max-w-none' : 'max-w-7xl'}`}>
              <Routes>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.DASHBOARD}>
                      <Dashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/users"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.USERS}>
                      <UsersManagement />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/trucks"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.TRIPS}>
                      <TrucksManagement />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/trucks/trip-summary"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.TRIPS}>
                      <TruckTripSummary />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/drivers"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.USERS}>
                      <DriversManagement />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/driver-attendance"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.USERS}>
                      <DriverAttendance />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/locations"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.TRIPS}>
                      <LocationsManagement />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/assignments"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.ASSIGNMENTS}>
                      <AssignmentsManagement />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/trips"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.TRIPS}>
                      <TripMonitoring />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/scanner"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.TRIPS}>
                      <QRScanner />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/assignment-monitoring"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.ASSIGNMENTS}>
                      <AssignmentMonitoring />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/shifts"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.SHIFTS}>
                      <SimplifiedShiftManagement />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/shifts/integration"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.SHIFTS}>
                      <ShiftAssignmentIntegration />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/analytics"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.ANALYTICS}>
                      <UnifiedAnalytics />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/analytics-reports"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.ANALYTICS}>
                      <UnifiedAnalytics />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/fleet-resources"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.ANALYTICS}>
                      <FleetResourceMonitor />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/settings"
                  element={
                    <ProtectedRoute requiredPermission={AVAILABLE_PAGES.SETTINGS}>
                      <Settings />
                    </ProtectedRoute>
                  }
                />
              </Routes>
            </div>
          </div>
          
          {/* Footer */}
          <Footer />
        </main>
      </div>
        {/* Mobile sidebar backdrop */}
      {!sidebarHidden && sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default DashboardLayout;