// Security initialization utilities
export function initializeSecurity() {
  // Disable right-click context menu in production
  if (process.env.NODE_ENV === 'production') {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
    });

    // Disable F12, Ctrl+Shift+I, Ctrl+U
    document.addEventListener('keydown', (e) => {
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.key === 'u')
      ) {
        e.preventDefault();
      }
    });
  }

  // Set up CSP violation reporting
  if ('SecurityPolicyViolationEvent' in window) {
    document.addEventListener('securitypolicyviolation', (e) => {
      console.warn('CSP Violation:', {
        blockedURI: e.blockedURI,
        violatedDirective: e.violatedDirective,
        originalPolicy: e.originalPolicy
      });
      
      // Report to monitoring service if available
      if (window.reportSecurityViolation) {
        window.reportSecurityViolation(e);
      }
    });
  }

  // Prevent clickjacking
  if (window.self !== window.top) {
    console.warn('Application loaded in iframe - potential clickjacking attempt');
    // Optionally break out of iframe
    // window.top.location = window.self.location;
  }

  // Clear sensitive data on page unload
  window.addEventListener('beforeunload', () => {
    // Clear any sensitive data from memory
    if (window.clearSensitiveData) {
      window.clearSensitiveData();
    }
  });
}

// Sanitize user input (basic XSS prevention)
export function sanitizeInput(input) {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

// Validate URL to prevent open redirects
export function isValidURL(url) {
  try {
    const parsedURL = new URL(url, window.location.origin);
    // Only allow same origin or explicitly allowed domains
    const allowedDomains = [
      window.location.hostname,
      'localhost',
      '127.0.0.1'
    ];
    
    return allowedDomains.includes(parsedURL.hostname);
  } catch {
    return false;
  }
}

export default { initializeSecurity, sanitizeInput, isValidURL };