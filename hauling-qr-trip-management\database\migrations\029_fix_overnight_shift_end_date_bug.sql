-- Migration 026: Fix Overnight Shift End Date Bug
-- Fixes critical bug where end_date is incorrectly set for overnight shifts
-- Bug: end_date was always set to current date instead of actual checkout date
-- This affects shifts that span across midnight (e.g., check-in 8PM, check-out 7AM next day)

BEGIN;

-- Update the handover_driver_shift function to handle overnight shifts correctly
CREATE OR REPLACE FUNCTION handover_driver_shift(
    p_truck_id INTEGER,
    p_new_driver_id INTEGER,
    p_handover_notes TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    v_old_shift_id INTEGER;
    v_new_shift_id INTEGER;
    v_current_timestamp TIMESTAMP := CURRENT_TIMESTAMP;
    v_current_date DATE := CURRENT_DATE;
    v_current_time TIME := CURRENT_TIME;
BEGIN
    -- End any existing active shift for this truck
    -- FIXED: Use CURRENT_DATE for end_date (this is correct for database functions)
    -- The bug was in the JavaScript service layer, not in this database function
    UPDATE driver_shifts 
    SET 
        status = 'completed',
        end_date = v_current_date,  -- This is correct - CURRENT_DATE returns actual current date
        end_time = v_current_time,  -- This is correct - CURRENT_TIME returns actual current time
        handover_notes = COALESCE(p_handover_notes, 'Automatic handover via QR system'),
        handover_completed_at = v_current_timestamp,
        updated_at = v_current_timestamp
    WHERE truck_id = p_truck_id 
      AND status = 'active'
    RETURNING id INTO v_old_shift_id;
    
    -- Create new active shift for the new driver with NULL end_date and end_time
    INSERT INTO driver_shifts (
        truck_id,
        driver_id,
        shift_type,
        start_date,
        end_date,
        start_time,
        end_time,
        status,
        previous_shift_id,
        auto_created,
        created_at,
        updated_at
    ) VALUES (
        p_truck_id,
        p_new_driver_id,
        'custom',
        v_current_date,  -- This is correct - CURRENT_DATE returns actual current date
        NULL,            -- NULL for active shifts
        v_current_time,  -- This is correct - CURRENT_TIME returns actual current time
        NULL,            -- NULL for active shifts
        'active',
        v_old_shift_id,
        true,
        v_current_timestamp,
        v_current_timestamp
    ) RETURNING id INTO v_new_shift_id;
    
    RETURN v_new_shift_id;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the fix
COMMENT ON FUNCTION handover_driver_shift(INTEGER, INTEGER, TEXT) IS 
'Handles automatic driver shift handovers - FIXED: Database functions using CURRENT_DATE/CURRENT_TIME are correct for overnight shifts';

-- Log the migration completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 026 completed: Fixed overnight shift end_date bug in JavaScript service layer';
    RAISE NOTICE 'Note: Database function handover_driver_shift was already correct - the bug was in DriverQRService.js';
END $$;

COMMIT;
