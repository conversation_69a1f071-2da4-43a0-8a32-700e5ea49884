/**
 * Shift Status Monitor Component
 * Purpose: Real-time monitoring dashboard for shift status management
 * Features: Live status updates, performance metrics, system health
 */

import React, { useState, useEffect, useCallback } from 'react';

const ShiftStatusMonitor = () => {
  const [statusSummary, setStatusSummary] = useState(null);
  const [serviceHealth, setServiceHealth] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch status summary
  const fetchStatusSummary = useCallback(async () => {
    try {
      const response = await fetch('/api/shift-transitions/summary', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setStatusSummary(data.data);
      setLastUpdate(new Date());
      setError(null);
    } catch (err) {
      console.error('Error fetching status summary:', err);
      setError(err.message);
    }
  }, []);

  // Fetch service health
  const fetchServiceHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/shift-transitions/service-health', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setServiceHealth(data.data);
    } catch (err) {
      console.error('Error fetching service health:', err);
    }
  }, []);

  // Force update all shifts
  const forceUpdate = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/shift-transitions/force-update', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Refresh data after force update
      await fetchStatusSummary();
      await fetchServiceHealth();
    } catch (err) {
      console.error('Error forcing update:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load and auto-refresh setup
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      await Promise.all([fetchStatusSummary(), fetchServiceHealth()]);
      setIsLoading(false);
    };

    loadData();

    // Set up auto-refresh
    let interval;
    if (autoRefresh) {
      interval = setInterval(() => {
        fetchStatusSummary();
        fetchServiceHealth();
      }, 30000); // 30 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, fetchStatusSummary, fetchServiceHealth]);

  if (isLoading && !statusSummary) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading shift status monitor...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-bold text-gray-900">🔄 Shift Status Monitor</h2>
          <p className="text-sm text-gray-600">Real-time shift status monitoring and management</p>
        </div>
        <div className="flex items-center space-x-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-600">Auto-refresh</span>
          </label>
          <button
            onClick={forceUpdate}
            disabled={isLoading}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm"
          >
            {isLoading ? 'Updating...' : 'Force Update'}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="text-red-400">⚠️</div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Status Summary */}
      {statusSummary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{statusSummary.total_shifts}</div>
            <div className="text-sm text-blue-800">Total Shifts</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">{statusSummary.active_shifts}</div>
            <div className="text-sm text-green-800">Active Shifts</div>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="text-2xl font-bold text-yellow-600">{statusSummary.scheduled_shifts}</div>
            <div className="text-sm text-yellow-800">Scheduled Shifts</div>
          </div>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="text-2xl font-bold text-gray-600">{statusSummary.completed_shifts}</div>
            <div className="text-sm text-gray-800">Completed Shifts</div>
          </div>
        </div>
      )}

      {/* Action Items */}
      {statusSummary && (statusSummary.needs_activation > 0 || statusSummary.needs_completion > 0) && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-orange-800 mb-2">⚠️ Action Required</h3>
          <div className="space-y-1">
            {statusSummary.needs_activation > 0 && (
              <p className="text-sm text-orange-700">
                {statusSummary.needs_activation} shift(s) need activation
              </p>
            )}
            {statusSummary.needs_completion > 0 && (
              <p className="text-sm text-orange-700">
                {statusSummary.needs_completion} shift(s) need completion
              </p>
            )}
          </div>
        </div>
      )}

      {/* Service Health */}
      {serviceHealth && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-gray-800 mb-3">🔧 Service Health</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-gray-600">Service Status</div>
              <div className={`text-sm font-medium ${serviceHealth.is_running ? 'text-green-600' : 'text-red-600'}`}>
                {serviceHealth.is_running ? '✅ Running' : '❌ Stopped'}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Update Interval</div>
              <div className="text-sm font-medium text-gray-800">
                {serviceHealth.update_interval_ms / 1000}s
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Performance Target</div>
              <div className="text-sm font-medium text-gray-800">
                &lt;{serviceHealth.performance_target_ms}ms
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Last Check</div>
              <div className="text-sm font-medium text-gray-800">
                {new Date(serviceHealth.last_check).toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Overnight Shifts Alert */}
      {statusSummary && statusSummary.overnight_active > 0 && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-purple-800 mb-2">🌙 Overnight Operations</h3>
          <p className="text-sm text-purple-700">
            {statusSummary.overnight_active} overnight shift(s) currently active
          </p>
        </div>
      )}

      {/* Last Update */}
      {lastUpdate && (
        <div className="text-xs text-gray-500 text-center">
          Last updated: {lastUpdate.toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default ShiftStatusMonitor;
