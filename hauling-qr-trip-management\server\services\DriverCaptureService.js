/**
 * Driver Capture Service
 * 
 * Enhanced driver capture functionality that addresses requirements:
 * - Requirement 2: Correct identification of active drivers across overnight and multi-day shifts
 * - Requirement 4: Comprehensive logging and debugging capabilities
 * - Requirement 5: Automated validation and monitoring
 */

const { getClient } = require('../config/database');
const ShiftTypeDetector = require('../utils/ShiftTypeDetector');
const DataFlowLogger = require('../utils/DataFlowLogger');

class DriverCaptureService {
  /**
   * Enhanced driver capture with comprehensive logging and fallback methods
   * @param {Object} client - Database client
   * @param {number} truckId - Truck ID
   * @param {Date} timestamp - Timestamp for the capture
   * @returns {Object|null} - Driver information or null if not found
   */
  static async captureActiveDriverInfo(client, truckId, timestamp = new Date()) {
    const correlationId = `driver_capture_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Enhanced logging for debugging
      this.logDebug('DRIVER_CAPTURE_START', 'Starting enhanced driver capture operation', {
        correlation_id: correlationId,
        truck_id: truckId,
        timestamp: timestamp.toISOString(),
        timestamp_date: timestamp.toISOString().split('T')[0],
        timestamp_time: timestamp.toISOString().split('T')[1].split('.')[0],
        detected_shift_type: ShiftTypeDetector.detectShiftType(timestamp)
      });

      // Primary capture method with enhanced query
      const primaryResult = await this.primaryDriverCapture(client, truckId, timestamp, correlationId);
      if (primaryResult) {
        this.logSuccess('DRIVER_CAPTURE_PRIMARY_SUCCESS', 'Driver captured via primary method', {
          correlation_id: correlationId,
          truck_id: truckId,
          driver_id: primaryResult.driver_id,
          driver_name: primaryResult.driver_name,
          shift_type: primaryResult.shift_type,
          capture_method: 'primary'
        });
        return primaryResult;
      }

      // Fallback 1: QR-created shift fallback
      const fallback1Result = await this.qrShiftFallback(client, truckId, timestamp, correlationId);
      if (fallback1Result) {
        this.logSuccess('DRIVER_CAPTURE_FALLBACK1_SUCCESS', 'Driver captured via QR shift fallback', {
          correlation_id: correlationId,
          truck_id: truckId,
          driver_id: fallback1Result.driver_id,
          driver_name: fallback1Result.driver_name,
          shift_type: fallback1Result.shift_type,
          capture_method: 'qr_fallback'
        });
        return fallback1Result;
      }

      // Fallback 2: Database function fallback
      const fallback2Result = await this.databaseFunctionFallback(client, truckId, timestamp, correlationId);
      if (fallback2Result) {
        this.logSuccess('DRIVER_CAPTURE_FALLBACK2_SUCCESS', 'Driver captured via database function', {
          correlation_id: correlationId,
          truck_id: truckId,
          driver_id: fallback2Result.driver_id,
          driver_name: fallback2Result.driver_name,
          shift_type: fallback2Result.shift_type,
          capture_method: 'database_function'
        });
        return fallback2Result;
      }

      // Fallback 3: Extended time range search
      const fallback3Result = await this.extendedTimeRangeSearch(client, truckId, timestamp, correlationId);
      if (fallback3Result) {
        this.logSuccess('DRIVER_CAPTURE_FALLBACK3_SUCCESS', 'Driver captured via extended time range', {
          correlation_id: correlationId,
          truck_id: truckId,
          driver_id: fallback3Result.driver_id,
          driver_name: fallback3Result.driver_name,
          shift_type: fallback3Result.shift_type,
          capture_method: 'extended_range'
        });
        return fallback3Result;
      }

      // All methods failed
      this.logFailure('DRIVER_CAPTURE_ALL_FAILED', 'All driver capture methods failed', {
        correlation_id: correlationId,
        truck_id: truckId,
        timestamp: timestamp.toISOString(),
        attempted_methods: ['primary', 'qr_fallback', 'database_function', 'extended_range']
      });

      return null;

    } catch (error) {
      this.logError('DRIVER_CAPTURE_ERROR', error, {
        correlation_id: correlationId,
        truck_id: truckId,
        timestamp: timestamp.toISOString()
      });
      throw error;
    }
  }

  /**
   * Primary driver capture method with enhanced query logic
   */
  static async primaryDriverCapture(client, truckId, timestamp, correlationId) {
    const timestampDate = timestamp.toISOString().split('T')[0];
    const timestampTime = timestamp.toISOString().split('T')[1].split('.')[0];

    try {
      const query = `
        SELECT
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type,
          ds.start_date,
          ds.end_date,
          ds.start_time,
          ds.end_time,
          ds.shift_date,
          ds.auto_created,
          ds.status
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = $1
          AND ds.status = 'active'
          AND d.status = 'active'
          AND (
            -- QR-created active shifts: if end_time is NULL, shift is active from start_date onwards
            (ds.start_date IS NOT NULL AND ds.auto_created = true AND ds.end_time IS NULL AND 
             $2::date >= ds.start_date) OR
            -- QR-created completed shifts: check if timestamp falls within date range
            (ds.start_date IS NOT NULL AND ds.auto_created = true AND ds.end_time IS NOT NULL AND 
             $2::date BETWEEN ds.start_date AND ds.end_date) OR
            -- Legacy manual shifts: check if timestamp matches shift date
            (ds.start_date IS NULL AND ds.shift_date IS NOT NULL AND ds.shift_date = $2::date) OR
            -- Manual shifts with start_date/end_date: check date range
            (ds.start_date IS NOT NULL AND ds.auto_created = false AND 
             $2::date BETWEEN ds.start_date AND COALESCE(ds.end_date, ds.start_date))
          )
        ORDER BY 
          ds.auto_created DESC,  -- Prioritize QR-created shifts
          ds.created_at DESC
        LIMIT 1
      `;

      const result = await client.query(query, [truckId, timestampDate]);

      if (result.rows.length > 0) {
        const driver = result.rows[0];
        
        // Enhanced time validation
        const timeMatches = this.validateShiftTiming(driver, timestampTime, correlationId);
        
        if (timeMatches) {
          return {
            driver_id: driver.driver_id,
            driver_name: driver.driver_name,
            employee_id: driver.employee_id,
            shift_id: driver.shift_id,
            shift_type: driver.shift_type
          };
        } else {
          this.logDebug('DRIVER_CAPTURE_TIME_MISMATCH', 'Driver found but time validation failed', {
            correlation_id: correlationId,
            truck_id: truckId,
            driver_id: driver.driver_id,
            shift_id: driver.shift_id,
            shift_type: driver.shift_type,
            start_time: driver.start_time,
            end_time: driver.end_time,
            current_time: timestampTime
          });
        }
      }

      return null;
    } catch (error) {
      this.logError('DRIVER_CAPTURE_PRIMARY_ERROR', error, {
        correlation_id: correlationId,
        truck_id: truckId
      });
      return null;
    }
  }

  /**
   * QR-created shift fallback method
   */
  static async qrShiftFallback(client, truckId, timestamp, correlationId) {
    const timestampDate = timestamp.toISOString().split('T')[0];

    try {
      const query = `
        SELECT
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = $1
          AND ds.status = 'active'
          AND d.status = 'active'
          AND ds.auto_created = true
          AND ds.start_date IS NOT NULL
          AND $2::date >= ds.start_date
          AND ds.end_time IS NULL  -- Still checked in
        ORDER BY ds.created_at DESC
        LIMIT 1
      `;

      const result = await client.query(query, [truckId, timestampDate]);

      if (result.rows.length > 0) {
        return result.rows[0];
      }

      return null;
    } catch (error) {
      this.logError('DRIVER_CAPTURE_QR_FALLBACK_ERROR', error, {
        correlation_id: correlationId,
        truck_id: truckId
      });
      return null;
    }
  }

  /**
   * Database function fallback method
   */
  static async databaseFunctionFallback(client, truckId, timestamp, correlationId) {
    try {
      const result = await client.query(`
        SELECT * FROM capture_active_driver_for_trip($1, $2)
      `, [truckId, timestamp]);

      if (result.rows.length > 0) {
        return result.rows[0];
      }

      return null;
    } catch (error) {
      this.logError('DRIVER_CAPTURE_DB_FUNCTION_ERROR', error, {
        correlation_id: correlationId,
        truck_id: truckId
      });
      return null;
    }
  }

  /**
   * Extended time range search for edge cases
   */
  static async extendedTimeRangeSearch(client, truckId, timestamp, correlationId) {
    const timestampDate = timestamp.toISOString().split('T')[0];
    
    try {
      // Search for shifts within a 24-hour window
      const query = `
        SELECT
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type,
          ds.start_date,
          ds.end_date,
          ds.start_time,
          ds.end_time
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = $1
          AND ds.status = 'active'
          AND d.status = 'active'
          AND (
            -- Check current date
            ($2::date BETWEEN ds.start_date AND COALESCE(ds.end_date, ds.start_date)) OR
            -- Check previous day (for overnight shifts)
            (($2::date - INTERVAL '1 day')::date BETWEEN ds.start_date AND COALESCE(ds.end_date, ds.start_date)) OR
            -- Check next day (for edge cases)
            (($2::date + INTERVAL '1 day')::date BETWEEN ds.start_date AND COALESCE(ds.end_date, ds.start_date))
          )
        ORDER BY 
          ABS(EXTRACT(EPOCH FROM (ds.start_date - $2::date))),
          ds.created_at DESC
        LIMIT 1
      `;

      const result = await client.query(query, [truckId, timestampDate]);

      if (result.rows.length > 0) {
        const driver = result.rows[0];
        return {
          driver_id: driver.driver_id,
          driver_name: driver.driver_name,
          employee_id: driver.employee_id,
          shift_id: driver.shift_id,
          shift_type: driver.shift_type
        };
      }

      return null;
    } catch (error) {
      this.logError('DRIVER_CAPTURE_EXTENDED_SEARCH_ERROR', error, {
        correlation_id: correlationId,
        truck_id: truckId
      });
      return null;
    }
  }

  /**
   * Enhanced shift timing validation
   */
  static validateShiftTiming(driver, currentTime, correlationId) {
    try {
      // If no end_time, assume shift is active (QR-created shifts)
      if (driver.auto_created && driver.end_time === null) {
        this.logDebug('DRIVER_CAPTURE_TIME_VALIDATION', 'QR-created active shift - no time validation needed', {
          correlation_id: correlationId,
          shift_id: driver.shift_id,
          auto_created: driver.auto_created,
          end_time: driver.end_time
        });
        return true;
      }

      // If no start_time or end_time, assume valid
      if (!driver.start_time || !driver.end_time) {
        return true;
      }

      const startTime = driver.start_time;
      const endTime = driver.end_time;
      let timeMatches = false;

      if (driver.shift_type === 'day') {
        // Day shifts: simple time range check
        timeMatches = currentTime >= startTime && currentTime <= endTime;
      } else if (driver.shift_type === 'night') {
        // Night shifts: handle overnight (e.g., 18:00 to 06:00)
        if (endTime < startTime) {
          // Overnight shift
          timeMatches = currentTime >= startTime || currentTime <= endTime;
        } else {
          // Same day night shift
          timeMatches = currentTime >= startTime && currentTime <= endTime;
        }
      } else {
        // Custom shifts: assume active if found
        timeMatches = true;
      }

      this.logDebug('DRIVER_CAPTURE_TIME_VALIDATION', 'Time validation completed', {
        correlation_id: correlationId,
        shift_id: driver.shift_id,
        shift_type: driver.shift_type,
        start_time: startTime,
        end_time: endTime,
        current_time: currentTime,
        time_matches: timeMatches,
        validation_logic: driver.shift_type === 'night' && endTime < startTime ? 'overnight' : 'standard'
      });

      return timeMatches;
    } catch (error) {
      this.logError('DRIVER_CAPTURE_TIME_VALIDATION_ERROR', error, {
        correlation_id: correlationId,
        shift_id: driver.shift_id
      });
      return false;
    }
  }

  /**
   * Validate driver capture accuracy (Requirement 5)
   */
  static async validateDriverCaptureAccuracy(client, truckId, timestamp, expectedDriverId = null) {
    const correlationId = `validation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const capturedDriver = await this.captureActiveDriverInfo(client, truckId, timestamp);
      
      const validationResult = {
        success: capturedDriver !== null,
        captured_driver: capturedDriver,
        expected_driver_id: expectedDriverId,
        matches_expected: expectedDriverId ? capturedDriver?.driver_id === expectedDriverId : null,
        timestamp: timestamp.toISOString(),
        correlation_id: correlationId
      };

      if (!validationResult.success) {
        this.logFailure('DRIVER_CAPTURE_VALIDATION_FAILED', 'Driver capture validation failed - no driver found', {
          correlation_id: correlationId,
          truck_id: truckId,
          timestamp: timestamp.toISOString(),
          expected_driver_id: expectedDriverId
        });
      } else if (expectedDriverId && !validationResult.matches_expected) {
        this.logFailure('DRIVER_CAPTURE_VALIDATION_MISMATCH', 'Driver capture validation failed - wrong driver', {
          correlation_id: correlationId,
          truck_id: truckId,
          captured_driver_id: capturedDriver.driver_id,
          expected_driver_id: expectedDriverId,
          timestamp: timestamp.toISOString()
        });
      } else {
        this.logSuccess('DRIVER_CAPTURE_VALIDATION_SUCCESS', 'Driver capture validation passed', {
          correlation_id: correlationId,
          truck_id: truckId,
          driver_id: capturedDriver.driver_id,
          driver_name: capturedDriver.driver_name,
          timestamp: timestamp.toISOString()
        });
      }

      return validationResult;
    } catch (error) {
      this.logError('DRIVER_CAPTURE_VALIDATION_ERROR', error, {
        correlation_id: correlationId,
        truck_id: truckId,
        timestamp: timestamp.toISOString()
      });
      throw error;
    }
  }

  /**
   * Get driver capture metrics for monitoring
   */
  static async getDriverCaptureMetrics(client, dateFrom, dateTo) {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_trips,
          COUNT(performed_by_driver_id) as trips_with_driver,
          COUNT(performed_by_shift_type) as trips_with_shift_type,
          COUNT(CASE WHEN performed_by_driver_id IS NOT NULL AND performed_by_shift_type IS NOT NULL THEN 1 END) as complete_driver_info,
          ROUND(
            (COUNT(performed_by_driver_id)::decimal / COUNT(*)) * 100, 2
          ) as driver_capture_rate,
          ROUND(
            (COUNT(performed_by_shift_type)::decimal / COUNT(*)) * 100, 2
          ) as shift_type_capture_rate
        FROM trip_logs
        WHERE created_at BETWEEN $1 AND $2
      `;

      const result = await client.query(query, [dateFrom, dateTo]);
      return result.rows[0];
    } catch (error) {
      this.logError('DRIVER_CAPTURE_METRICS_ERROR', error, {
        date_from: dateFrom,
        date_to: dateTo
      });
      throw error;
    }
  }

  // Logging methods
  static logDebug(context, message, data) {
    console.log(`[DEBUG] ${context}: ${message}`, JSON.stringify(data, null, 2));
    DataFlowLogger.logDataFlowSync('driver_capture', 'debug', message, data);
  }

  static logSuccess(context, message, data) {
    console.log(`[SUCCESS] ${context}: ${message}`, JSON.stringify(data, null, 2));
    DataFlowLogger.logDataFlowSync('driver_capture', 'success', message, data);
  }

  static logFailure(context, message, data) {
    console.log(`[FAILURE] ${context}: ${message}`, JSON.stringify(data, null, 2));
    DataFlowLogger.logDataFlowSync('driver_capture', 'failure', message, data);
  }

  static logError(context, error, data) {
    console.error(`[ERROR] ${context}: ${error.message}`, JSON.stringify(data, null, 2));
    DataFlowLogger.logDataFlowSync('driver_capture', 'error', error.message, { ...data, stack: error.stack });
  }
}

module.exports = DriverCaptureService;