{"enabled": true, "name": "Test-Implement-Cleanup Workflow", "description": "Monitors code changes to automatically test implementations, deploy successful changes to production code, clean up temporary files, and fix MCP server issues while tracing dependencies", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.js", "**/*.jsx", "**/*.json", "**/*.sql", "**/*.env", "server/**/*", "client/**/*", "database/**/*", "scripts/**/*"]}, "then": {"type": "askAgent", "prompt": "A file has been modified in the hauling QR trip system. Please:\n\n1. **Test the changes**: Run appropriate tests based on the modified files and check .env for database credentials/ports\n2. **Implement if successful**: If tests pass, implement changes in the actual production code\n3. **Clean up**: Remove any temporary test files after successful implementation\n4. **Trace dependencies**: Analyze both static and dynamic dependencies of modified files\n5. **Determine file purpose**: Identify the file type and its role in the system\n6. **Create task list**: Generate actionable tasks for the implementation\n7. **Fix MCP server**: If the MCP server is not working or executing properly, diagnose and fix it\n8. **Suggest improvements**: Provide thoughtful suggestions for better function implementation\n9. **Avoid file proliferation**: Only create new files if they're not connected to existing functions, otherwise modify existing files\n10. **Continue until success**: Do not stop until the implementation in actual code is successful\n\nFocus on the hauling QR trip system architecture with React frontend, Express backend, PostgreSQL database, and QR code tracking functionality. Use existing project structure and follow established patterns."}}