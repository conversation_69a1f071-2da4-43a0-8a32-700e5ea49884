/**
 * Automated Fix Service
 * 
 * Provides automated fixes for system health issues across three core modules:
 * - Shift Management
 * - Assignment Management
 * - Trip Monitoring
 * 
 * Integrates with existing database functions and monitoring scripts
 * to provide a unified interface for automated fixes.
 */

const { getClient } = require('../config/database');

class AutomatedFixService {
  constructor() {
    this.logger = console;
  }

  /**
   * Fix shift management issues
   * @returns {Promise<Object>} Result of the fix operation
   */
  async fixShiftManagement() {
    try {
      const client = await getClient();
      
      try {
        // Begin transaction
        await client.query('BEGIN');
        
        // Execute the schedule_auto_activation function
        await client.query('SELECT schedule_auto_activation()');
        
        // Get updated statistics
        const stats = await client.query(`
          SELECT
            COUNT(*) FILTER (WHERE status = 'active') as active_count,
            COUNT(*) FILTER (WHERE status = 'scheduled') as scheduled_count,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_count,
            COUNT(*) as total_count
          FROM driver_shifts
        `);
        
        // Log the fix operation
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details
          ) VALUES (
            'SHIFT_AUTO_FIX',
            'Automated fix for shift management issues',
            $1
          )
        `, [JSON.stringify({
          timestamp: new Date().toISOString(),
          statistics: stats.rows[0]
        })]);
        
        // Commit transaction
        await client.query('COMMIT');
        
        return {
          success: true,
          message: 'Shift management issues fixed successfully',
          details: 'Executed schedule_auto_activation() to correct shift statuses',
          affectedRecords: {
            active: stats.rows[0].active_count,
            scheduled: stats.rows[0].scheduled_count,
            completed: stats.rows[0].completed_count,
            total: stats.rows[0].total_count
          },
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        // Rollback transaction on error
        await client.query('ROLLBACK');
        throw error;
      } finally {
        // Release client back to pool
        client.release();
      }
    } catch (error) {
      this.logger.error('Error fixing shift management issues:', error);
      return {
        success: false,
        message: 'Failed to fix shift management issues',
        details: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Fix assignment management issues
   * @returns {Promise<Object>} Result of the fix operation
   */
  async fixAssignmentManagement() {
    try {
      const client = await getClient();
      
      try {
        // Begin transaction
        await client.query('BEGIN');
        
        // Synchronize assignments with active shifts
        const result = await client.query(`
          WITH active_shifts AS (
            SELECT 
              ds.truck_id,
              ds.driver_id,
              ds.shift_type
            FROM 
              driver_shifts ds
            WHERE 
              ds.status = 'active'
              AND ds.start_date <= CURRENT_DATE
              AND ds.end_date >= CURRENT_DATE
          ),
          updated_assignments AS (
            UPDATE assignments a
            SET
              driver_id = ash.driver_id,
              updated_at = CURRENT_TIMESTAMP
            FROM
              active_shifts ash
            WHERE
              a.truck_id = ash.truck_id
              AND (a.driver_id IS NULL OR a.driver_id != ash.driver_id)
            RETURNING a.id
          )
          SELECT COUNT(*) as updated_count FROM updated_assignments
        `);
        
        // Log the fix operation
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details
          ) VALUES (
            'ASSIGNMENT_AUTO_FIX',
            'Automated fix for assignment management issues',
            $1
          )
        `, [JSON.stringify({
          timestamp: new Date().toISOString(),
          updated_assignments: result.rows[0].updated_count
        })]);
        
        // Commit transaction
        await client.query('COMMIT');
        
        return {
          success: true,
          message: 'Assignment management issues fixed successfully',
          details: 'Synchronized assignments with active shifts',
          affectedRecords: {
            updated_assignments: result.rows[0].updated_count
          },
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        // Rollback transaction on error
        await client.query('ROLLBACK');
        throw error;
      } finally {
        // Release client back to pool
        client.release();
      }
    } catch (error) {
      this.logger.error('Error fixing assignment management issues:', error);
      return {
        success: false,
        message: 'Failed to fix assignment management issues',
        details: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Fix trip monitoring issues - specifically "No Active Shift" synchronization problems
   * @returns {Promise<Object>} Result of the fix operation
   */
  async fixTripMonitoring() {
    try {
      const client = await getClient();

      try {
        // Begin transaction
        await client.query('BEGIN');

        // Step 1: Identify assignments showing "No Active Shift" in Trip Monitoring
        const noActiveShiftQuery = `
          WITH trip_monitoring_view AS (
            SELECT
              a.id as assignment_id,
              a.truck_id,
              a.driver_id,
              dt.truck_number,
              d.full_name as assigned_driver,
              ds.id as found_shift_id,
              ds.shift_type,
              ds.status as shift_status,
              CASE
                WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
                  CONCAT('✅ ', ds.shift_type, ' Shift Active')
                WHEN ds.id IS NOT NULL AND ds.status = 'scheduled' THEN
                  CONCAT('📅 ', ds.shift_type, ' Shift Scheduled')
                ELSE '⚠️ No Active Shift'
              END as display_status
            FROM assignments a
            JOIN dump_trucks dt ON a.truck_id = dt.id
            LEFT JOIN drivers d ON a.driver_id = d.id
            LEFT JOIN driver_shifts ds ON (
              ds.truck_id = a.truck_id
              AND ds.status = 'active'
              AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
              AND (
                (ds.end_time < ds.start_time AND
                  (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
                OR
                (ds.end_time >= ds.start_time AND
                  CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
              )
            )
            WHERE a.status IN ('assigned', 'in_progress')
          )
          SELECT
            assignment_id,
            truck_id,
            truck_number,
            assigned_driver,
            display_status
          FROM trip_monitoring_view
          WHERE display_status = '⚠️ No Active Shift'
        `;

        const noActiveShiftResult = await client.query(noActiveShiftQuery);

        // Step 2: For each "No Active Shift" case, find the actual active shift
        let fixedShiftSyncCount = 0;
        const fixedAssignments = [];

        for (const assignment of noActiveShiftResult.rows) {
          // Find the actual active shift for this truck
          const actualShiftQuery = `
            SELECT
              ds.id,
              ds.driver_id,
              ds.shift_type,
              ds.status,
              d.full_name as driver_name
            FROM driver_shifts ds
            JOIN drivers d ON ds.driver_id = d.id
            WHERE ds.truck_id = $1
              AND ds.status = 'active'
              AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
              AND (
                (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND
                  (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
                OR
                (ds.shift_type = 'day' AND ds.end_time >= ds.start_time AND
                  CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
                OR
                (ds.shift_type = 'night' AND ds.end_time >= ds.start_time AND
                  CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
              )
            ORDER BY ds.created_at DESC
            LIMIT 1
          `;

          const actualShiftResult = await client.query(actualShiftQuery, [assignment.truck_id]);

          if (actualShiftResult.rows.length > 0) {
            const actualShift = actualShiftResult.rows[0];

            // Update assignment to match the active shift driver
            const updateResult = await client.query(`
              UPDATE assignments
              SET
                driver_id = $1,
                updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
                AND (driver_id IS NULL OR driver_id != $1)
              RETURNING id
            `, [actualShift.driver_id, assignment.assignment_id]);

            if (updateResult.rows.length > 0) {
              fixedShiftSyncCount++;
              fixedAssignments.push({
                assignment_id: assignment.assignment_id,
                truck_number: assignment.truck_number,
                previous_driver: assignment.assigned_driver,
                new_driver: actualShift.driver_name,
                shift_type: actualShift.shift_type
              });
            }
          }
        }

        // Step 3: Also fix any invalid trip statuses (legacy functionality)
        const invalidStatusResult = await client.query(`
          WITH invalid_trips AS (
            SELECT id, status
            FROM trip_logs
            WHERE status NOT IN ('assigned', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed', 'cancelled', 'stopped')
          ),
          fixed_trips AS (
            UPDATE trip_logs
            SET
              status = 'assigned',
              updated_at = CURRENT_TIMESTAMP
            FROM
              invalid_trips
            WHERE
              trip_logs.id = invalid_trips.id
            RETURNING trip_logs.id
          )
          SELECT COUNT(*) as fixed_count FROM fixed_trips
        `);
        
        // Log the fix operation
        await client.query(`
          INSERT INTO system_logs (
            log_type,
            message,
            details
          ) VALUES (
            'TRIP_AUTO_FIX',
            'Automated fix for trip monitoring shift synchronization issues',
            $1
          )
        `, [JSON.stringify({
          timestamp: new Date().toISOString(),
          fixed_shift_sync_issues: fixedShiftSyncCount,
          fixed_invalid_statuses: invalidStatusResult.rows[0].fixed_count,
          fixed_assignments: fixedAssignments,
          no_active_shift_count: noActiveShiftResult.rows.length
        })]);

        // Commit transaction
        await client.query('COMMIT');

        return {
          success: true,
          message: 'Trip monitoring issues fixed successfully',
          details: `Fixed ${fixedShiftSyncCount} "No Active Shift" synchronization issues and ${invalidStatusResult.rows[0].fixed_count} invalid trip statuses`,
          affectedRecords: {
            fixed_shift_sync_issues: fixedShiftSyncCount,
            fixed_invalid_statuses: invalidStatusResult.rows[0].fixed_count,
            no_active_shift_cases_found: noActiveShiftResult.rows.length,
            fixed_assignments: fixedAssignments
          },
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        // Rollback transaction on error
        await client.query('ROLLBACK');
        throw error;
      } finally {
        // Release client back to pool
        client.release();
      }
    } catch (error) {
      this.logger.error('Error fixing trip monitoring issues:', error);
      return {
        success: false,
        message: 'Failed to fix trip monitoring issues',
        details: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = AutomatedFixService;