# Fleet Resource Monitor Design Document

## Overview

The Fleet Resource Monitor is a real-time dashboard page that provides comprehensive visibility into driver and dump truck resource allocation across three levels: total available resources (from Driver/Truck Management), assigned resources (from Enhanced Shift Management), and actually active resources. The system identifies unassigned resources, absenteeism, equipment delays, and monitors trucks currently on routes between loading and unloading locations.

This design leverages the existing Driver Management (`drivers` table), Truck Management (`dump_trucks` table), Enhanced Shift Management (`driver_shifts` table), and Assignment Management (`assignments` table) to provide complete resource tracking with real-time WebSocket updates.

## Architecture

### System Integration Points

```mermaid
graph TB
    A[Fleet Resource Monitor Page] --> B[Fleet Resource API]
    B --> C[Driver Management]
    B --> D[Truck Management]
    B --> E[Enhanced Shift Management]
    B --> F[Assignment Management]
    B --> G[Trip Monitoring]
    B --> H[WebSocket Service]
    
    C --> I[(drivers table)]
    D --> J[(dump_trucks table)]
    E --> K[(driver_shifts table)]
    F --> L[(assignments table)]
    G --> M[(trip_logs table)]
    
    H --> N[Real-time Updates]
    N --> A
    
    O[Loading Locations] --> B
```

### Data Flow Architecture

1. **Data Collection Layer**: Aggregates data from Enhanced Shift Management, Assignment Management, and Trip Monitoring
2. **Business Logic Layer**: Calculates resource metrics, identifies discrepancies, and determines truck locations
3. **Real-time Layer**: WebSocket connections for live updates
4. **Presentation Layer**: React component with responsive mobile-first design

## Components and Interfaces

### Frontend Components

#### 1. FleetResourceMonitor (Main Component)
```javascript
// Location: client/src/pages/fleet/FleetResourceMonitor.jsx
const FleetResourceMonitor = () => {
  // Main container component with real-time data fetching
  // Manages WebSocket connections and state updates
}
```

**Key Features:**
- Real-time WebSocket connection management
- Automatic data refresh every 30 seconds
- Mobile-responsive layout with touch-friendly controls
- Error handling and offline state management

#### 2. ResourceSummaryCards
```javascript
// Location: client/src/components/fleet/ResourceSummaryCards.jsx
const ResourceSummaryCards = ({ driverStats, truckStats, alertLevel }) => {
  // Displays high-level resource counts with visual indicators
}
```

**Card Types:**
- **Driver Summary**: Total Available vs. Assigned to Active Shifts vs. Unassigned
- **Truck Summary**: Total Available vs. Assigned to Active Shifts vs. Unassigned
- **On Route Summary**: Trucks currently in transit
- **Alert Summary**: Resource shortages and underutilization

#### 3. LoadingLocationBreakdown
```javascript
// Location: client/src/components/fleet/LoadingLocationBreakdown.jsx
const LoadingLocationBreakdown = ({ locationData }) => {
  // Shows truck assignments by loading location
}
```

**Features:**
- Expandable sections for each loading location
- Truck numbers (DT-100, DT-102) with status indicators
- Quick count display for manager queries

#### 4. ResourceDetailTables
```javascript
// Location: client/src/components/fleet/ResourceDetailTables.jsx
const ResourceDetailTables = ({ drivers, trucks, onRouteData }) => {
  // Detailed tables showing individual resource status
}
```

**Table Types:**
- **Unassigned Drivers**: Names, Employee IDs, Available for Assignment
- **Unassigned Trucks**: Truck Numbers, Available for Assignment
- **Trucks On Route**: Truck Number, Loading Location, Unloading Location, Trip Start Time

#### 5. HistoricalTrends
```javascript
// Location: client/src/components/fleet/HistoricalTrends.jsx
const HistoricalTrends = ({ utilizationData }) => {
  // Charts showing resource utilization patterns
}
```

**Chart Types:**
- Daily utilization trends (Chart.js line charts)
- Peak utilization periods highlighting
- 7-day historical comparison

### Backend API Endpoints

#### 1. Fleet Resource Summary API
```javascript
// Route: GET /api/fleet-resources/summary
// Location: server/routes/fleet-resources.js

router.get('/summary', auth, async (req, res) => {
  // Returns aggregated resource counts and status
});
```

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "drivers": {
      "total_available": 30,
      "assigned_to_active_shifts": 25,
      "unassigned": 5,
      "unassigned_details": [
        {
          "driver_id": 456,
          "full_name": "Mike Johnson",
          "employee_id": "EMP005",
          "status": "available",
          "last_shift": "2025-01-15T16:00:00Z"
        }
      ]
    },
    "trucks": {
      "total_available": 25,
      "assigned_to_active_shifts": 20,
      "unassigned": 5,
      "on_route": 12,
      "unassigned_details": [
        {
          "truck_id": 890,
          "truck_number": "DT-105",
          "status": "available",
          "last_maintenance": "2025-01-10T10:00:00Z"
        }
      ]
    },
    "alerts": {
      "driver_shortage": false,
      "truck_shortage": false,
      "underutilization": {
        "unassigned_drivers": 5,
        "unassigned_trucks": 5
      }
    }
  }
}
```

#### 2. Loading Location Breakdown API
```javascript
// Route: GET /api/fleet-resources/loading-locations
// Location: server/routes/fleet-resources.js

router.get('/loading-locations', auth, async (req, res) => {
  // Returns truck assignments grouped by loading location
});
```

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "locations": [
      {
        "location_id": 1,
        "location_name": "Main Quarry",
        "assigned_trucks": [
          {
            "truck_id": 101,
            "truck_number": "DT-100",
            "status": "on_route",
            "driver_name": "John Doe",
            "current_trip": {
              "unloading_location": "Construction Site A",
              "trip_start": "2025-01-16T09:15:00Z",
              "estimated_return": "2025-01-16T10:30:00Z"
            }
          },
          {
            "truck_id": 102,
            "truck_number": "DT-102",
            "status": "at_location",
            "driver_name": "Jane Smith"
          }
        ],
        "total_assigned": 8,
        "currently_at_location": 3,
        "on_route": 5
      }
    ]
  }
}
```

#### 3. Historical Utilization API
```javascript
// Route: GET /api/fleet-resources/utilization-history
// Location: server/routes/fleet-resources.js

router.get('/utilization-history', auth, async (req, res) => {
  // Returns historical utilization data for trends
});
```

### Database Queries

#### 1. Three-Tier Resource Tracking
```sql
-- Get complete driver resource breakdown
WITH driver_totals AS (
  SELECT COUNT(*) as total_drivers FROM drivers WHERE status = 'active'
),
shift_assignments AS (
  SELECT 
    COUNT(CASE WHEN ds.status = 'active' THEN 1 END) as active_drivers,
    COUNT(CASE WHEN ds.status = 'scheduled' THEN 1 END) as scheduled_drivers,
    COUNT(DISTINCT ds.driver_id) as assigned_drivers
  FROM driver_shifts ds
  WHERE ds.start_date <= CURRENT_DATE 
    AND ds.end_date >= CURRENT_DATE
    AND ds.status IN ('active', 'scheduled')
)
SELECT 
  dt.total_drivers,
  sa.assigned_drivers,
  sa.active_drivers,
  (dt.total_drivers - sa.assigned_drivers) as unassigned_drivers,
  (sa.assigned_drivers - sa.active_drivers) as not_timed_in
FROM driver_totals dt, shift_assignments sa;
```

#### 2. Truck Location and Status Tracking
```sql
-- Get trucks by loading location with current status
SELECT 
  ll.id as location_id,
  ll.name as location_name,
  dt.id as truck_id,
  dt.truck_number,
  d.full_name as driver_name,
  ds.status as shift_status,
  CASE 
    WHEN tl.status = 'in_progress' THEN 'on_route'
    WHEN ds.status = 'active' THEN 'at_location'
    ELSE 'not_operating'
  END as truck_status,
  ul.name as unloading_location,
  tl.created_at as trip_start
FROM driver_shifts ds
JOIN dump_trucks dt ON ds.truck_id = dt.id
JOIN drivers d ON ds.driver_id = d.id
JOIN assignments a ON (a.truck_id = dt.id AND a.driver_id = d.id)
JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN trip_logs tl ON (tl.truck_id = dt.id AND tl.status = 'in_progress')
WHERE ds.start_date <= CURRENT_DATE 
  AND ds.end_date >= CURRENT_DATE
  AND ds.status IN ('active', 'scheduled')
ORDER BY ll.name, dt.truck_number;
```

#### 3. Unassigned Resources and Delay Detection
```sql
-- Get unassigned drivers (not in any current shifts)
SELECT 
  d.id as driver_id,
  d.full_name,
  d.employee_id,
  d.status,
  MAX(ds.end_date) as last_shift_date
FROM drivers d
LEFT JOIN driver_shifts ds ON d.id = ds.driver_id
WHERE d.status = 'active'
  AND d.id NOT IN (
    SELECT DISTINCT driver_id 
    FROM driver_shifts 
    WHERE start_date <= CURRENT_DATE 
      AND end_date >= CURRENT_DATE
      AND status IN ('active', 'scheduled')
  )
GROUP BY d.id, d.full_name, d.employee_id, d.status
ORDER BY d.full_name;

-- Identify drivers who should be active but haven't timed in
SELECT 
  ds.id as shift_id,
  d.id as driver_id,
  d.full_name,
  d.employee_id,
  ds.start_time as expected_time,
  EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - (CURRENT_DATE + ds.start_time::time))) / 60 as delay_minutes
FROM driver_shifts ds
JOIN drivers d ON ds.driver_id = d.id
WHERE ds.start_date <= CURRENT_DATE 
  AND ds.end_date >= CURRENT_DATE
  AND ds.status = 'scheduled'
  AND (CURRENT_DATE + ds.start_time::time) < CURRENT_TIMESTAMP
ORDER BY delay_minutes DESC;
```

## Data Models

### FleetResourceSummary Model
```javascript
// Location: server/models/FleetResourceSummary.js
class FleetResourceSummary {
  constructor() {
    this.drivers = {
      assigned_in_shifts: 0,
      currently_active: 0,
      not_timed_in: 0,
      not_timed_in_details: []
    };
    this.trucks = {
      assigned_in_shifts: 0,
      currently_operating: 0,
      not_started: 0,
      on_route: 0,
      not_started_details: []
    };
    this.alerts = {
      driver_shortage: false,
      truck_shortage: false,
      critical_delays: 0
    };
  }

  calculateAlerts() {
    // Business logic for determining alert levels
    this.alerts.driver_shortage = this.drivers.not_timed_in > (this.drivers.assigned_in_shifts * 0.1);
    this.alerts.truck_shortage = this.trucks.not_started > (this.trucks.assigned_in_shifts * 0.1);
    this.alerts.critical_delays = this.drivers.not_timed_in_details.filter(d => d.delay_minutes > 60).length;
  }
}
```

### LoadingLocationData Model
```javascript
// Location: server/models/LoadingLocationData.js
class LoadingLocationData {
  constructor(locationId, locationName) {
    this.location_id = locationId;
    this.location_name = locationName;
    this.assigned_trucks = [];
    this.total_assigned = 0;
    this.currently_at_location = 0;
    this.on_route = 0;
  }

  addTruck(truckData) {
    this.assigned_trucks.push(truckData);
    this.total_assigned++;
    
    if (truckData.status === 'at_location') {
      this.currently_at_location++;
    } else if (truckData.status === 'on_route') {
      this.on_route++;
    }
  }
}
```

## Error Handling

### Frontend Error Handling
```javascript
// Error boundary for the Fleet Resource Monitor
class FleetResourceErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Fleet Resource Monitor Error:', error, errorInfo);
    // Log to monitoring service
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-4">
            Unable to Load Fleet Resource Monitor
          </h2>
          <p className="text-gray-600 mb-4">
            There was an error loading the resource data. Please try refreshing the page.
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Refresh Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### Backend Error Handling
```javascript
// Centralized error handling for fleet resource APIs
const handleFleetResourceError = (error, operation) => {
  console.error(`Fleet Resource ${operation} Error:`, error);
  
  if (error.code === '23503') {
    return {
      success: false,
      error: {
        code: 'FOREIGN_KEY_VIOLATION',
        message: 'Referenced resource not found',
        operation
      }
    };
  }
  
  if (error.code === 'ECONNREFUSED') {
    return {
      success: false,
      error: {
        code: 'DATABASE_CONNECTION_ERROR',
        message: 'Unable to connect to database',
        operation
      }
    };
  }
  
  return {
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
      operation
    }
  };
};
```

## Testing Strategy

### Unit Tests
```javascript
// Location: server/tests/fleet-resources.test.js
describe('Fleet Resource API', () => {
  test('should calculate driver resource summary correctly', async () => {
    // Test driver assignment vs. active calculations
  });
  
  test('should identify trucks not started correctly', async () => {
    // Test truck status detection logic
  });
  
  test('should group trucks by loading location', async () => {
    // Test loading location breakdown logic
  });
});
```

### Integration Tests
```javascript
// Location: client/src/tests/FleetResourceMonitor.test.js
describe('Fleet Resource Monitor Component', () => {
  test('should display resource summary cards', () => {
    // Test component rendering with mock data
  });
  
  test('should handle WebSocket updates', () => {
    // Test real-time update functionality
  });
  
  test('should show loading location breakdown', () => {
    // Test loading location display
  });
});
```

### Mobile Testing
- **Touch Interaction**: Verify all controls work with touch input
- **Responsive Layout**: Test on various screen sizes (320px to 1200px)
- **Performance**: Ensure smooth scrolling and quick data updates
- **Offline Handling**: Test behavior when network connection is lost

### WebSocket Testing
```javascript
// Test WebSocket connection and real-time updates
describe('Fleet Resource WebSocket', () => {
  test('should connect and receive updates', () => {
    // Test WebSocket connection establishment
  });
  
  test('should handle connection failures gracefully', () => {
    // Test reconnection logic
  });
  
  test('should update UI when resource status changes', () => {
    // Test real-time UI updates
  });
});
```

## Performance Considerations

### Database Optimization
- **Indexes**: Create composite indexes on `driver_shifts(start_date, end_date, status)` and `assignments(truck_id, driver_id, status)`
- **Query Optimization**: Use materialized views for complex aggregations if needed
- **Connection Pooling**: Leverage existing PostgreSQL connection pool

### Frontend Optimization
- **Data Caching**: Cache resource data for 30 seconds to reduce API calls
- **Lazy Loading**: Load detailed tables only when expanded
- **Virtual Scrolling**: For large lists of drivers/trucks
- **Debounced Updates**: Batch WebSocket updates to prevent UI thrashing

### Real-time Updates
- **WebSocket Efficiency**: Send only changed data, not full datasets
- **Update Batching**: Group multiple status changes into single updates
- **Connection Management**: Automatic reconnection with exponential backoff

## Security Considerations

### Authentication & Authorization
- **JWT Validation**: All API endpoints require valid JWT tokens
- **Role-Based Access**: Restrict access based on user roles (fleet managers, dispatchers)
- **Data Filtering**: Users only see resources they have permission to manage

### Data Protection
- **Input Validation**: Validate all query parameters and request data
- **SQL Injection Prevention**: Use parameterized queries exclusively
- **Rate Limiting**: Apply rate limits to prevent API abuse
- **Audit Logging**: Log all resource monitoring activities

### WebSocket Security
- **Token Validation**: Validate JWT tokens for WebSocket connections
- **Connection Limits**: Limit concurrent WebSocket connections per user
- **Data Sanitization**: Sanitize all data before broadcasting updates