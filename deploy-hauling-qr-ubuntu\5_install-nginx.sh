#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - MODULAR NGINX INSTALLATION SCRIPT
# =============================================================================
# Version: 1.0.0 - Extracted from auto-deploy.sh
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: Self-contained Nginx installation with WSL-compatible systemctl alternatives
# =============================================================================

set -euo pipefail

# Load intelligent deployment framework
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly INTELLIGENT_FRAMEWORK="${SCRIPT_DIR}/intelligent-deployment-framework.sh"

# Define stub functions first (will be overridden if framework loads)
start_performance_timer() { echo "⏱️ Started timer: $1"; }
end_performance_timer() { echo "⏱️ Completed timer: $1"; }
generate_performance_report() { echo "📊 Performance report not available"; }
create_phase_completion_marker() { echo "📍 Phase completion marker: $1 ($2)"; }
should_skip_installation() { return 1; }  # Always proceed with installation
nginx_config_is_valid() { return 1; }     # Always proceed with configuration
command_exists() { command -v "$1" >/dev/null 2>&1; }
service_is_healthy() { return 0; }        # Assume services are healthy

if [[ -f "$INTELLIGENT_FRAMEWORK" ]]; then
  source "$INTELLIGENT_FRAMEWORK"
else
  echo "⚠️ WARNING: Intelligent deployment framework not found: $INTELLIGENT_FRAMEWORK"
  echo "⚠️ Using stub functions for compatibility"
fi

# Trap to handle signals gracefully and prevent unexpected exits
trap 'echo "Script interrupted but continuing..." >&2' SIGTERM SIGINT

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================
readonly SCRIPT_NAME="install-nginx.sh"
readonly LOG_DIR="/var/log/hauling-qr-deployment"
readonly LOG_FILE="${LOG_DIR}/nginx-install-$(date +%Y%m%d-%H%M%S).log"

# Application Configuration
readonly APP_DIR="/var/www/hauling-qr-system"
readonly PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"  # Dynamic domain configuration
readonly SERVER_HTTP_PORT=8080  # Cloudflare-compatible port

# WSL Detection
readonly IS_WSL=$(grep -qi microsoft /proc/version && echo "true" || echo "false")

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Create log directory with proper permissions
sudo mkdir -p "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"
sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true

ts() { date '+%Y-%m-%d %H:%M:%S'; }
log() { echo "[$(ts)] $*" | tee -a "$LOG_FILE"; }

log_info() {
  echo -e "${BLUE}[$(ts)] INFO  | $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
  echo -e "${GREEN}[$(ts)] OK    | $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
  echo -e "${YELLOW}[$(ts)] WARN  | $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
  echo -e "${RED}[$(ts)] ERROR | $1${NC}" | tee -a "$LOG_FILE"
}

log_debug() {
  echo -e "${CYAN}[$(ts)] DEBUG | $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# ERROR HANDLING
# =============================================================================
cleanup_on_error() {
  local exit_code=$?
  if [[ $exit_code -ne 0 ]]; then
    log_error "Nginx installation failed with exit code $exit_code"
    log_error "Check log file: $LOG_FILE"
  fi
  exit $exit_code
}

trap cleanup_on_error ERR

# =============================================================================
# NGINX SERVICE DEPENDENCY CONFIGURATION
# =============================================================================
create_nginx_service_dependencies() {
  log_info "🔧 Configuring NGINX service dependencies to prevent 502 Bad Gateway..."

  # Create systemd override directory
  sudo mkdir -p /etc/systemd/system/nginx.service.d

  # Create service override to add backend health check and startup delay
  sudo tee /etc/systemd/system/nginx.service.d/backend-dependency.conf >/dev/null <<EOF
[Unit]
# Wait for network to be fully ready
After=network-online.target
Wants=network-online.target

# Add startup delay to allow backend services to initialize
ExecStartPre=/bin/sleep 10

# Health check: Ensure backend is responding before starting NGINX
ExecStartPre=/bin/bash -c 'for i in {1..30}; do if curl -s http://localhost:${SERVER_HTTP_PORT}/health >/dev/null 2>&1; then exit 0; fi; sleep 2; done; echo "Backend health check failed after 60 seconds" >&2; exit 1'

[Service]
# Restart NGINX if it fails (e.g., due to backend unavailability)
Restart=on-failure
RestartSec=10
StartLimitInterval=300
StartLimitBurst=5

# Set environment variables for configuration
Environment="BACKEND_PORT=${SERVER_HTTP_PORT}"
EOF

  # Reload systemd to apply changes
  if command -v systemctl >/dev/null 2>&1; then
    sudo systemctl daemon-reload
    log_success "✅ NGINX service dependencies configured"
  else
    log_warning "⚠️ systemctl not available, service dependencies not configured"
  fi

  return 0
}

# =============================================================================
# CORS PERSISTENCE CONFIGURATION
# =============================================================================
create_cors_persistence_script() {
  log_info "🔧 Creating CORS persistence script for post-reboot validation..."

  local cors_script="/usr/local/bin/hauling-qr-cors-fix.sh"

  # Create the CORS persistence script
  sudo tee "$cors_script" >/dev/null <<'CORS_SCRIPT_EOF'
#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - CORS PERSISTENCE SCRIPT
# =============================================================================
# Description: Ensures CORS configuration persists after VPS reboots
# Usage: Called automatically by systemd service or manually for fixes
# =============================================================================

set -euo pipefail

# Configuration
PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"
SERVER_HTTP_PORT=8080
APP_DIR="/var/www/hauling-qr-system"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[CORS-FIX]${NC} $1" | logger -t hauling-qr-cors
    echo -e "${BLUE}[CORS-FIX]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[CORS-FIX]${NC} $1" | logger -t hauling-qr-cors
    echo -e "${GREEN}[CORS-FIX]${NC} $1"
}

log_error() {
    echo -e "${RED}[CORS-FIX]${NC} $1" | logger -t hauling-qr-cors
    echo -e "${RED}[CORS-FIX]${NC} $1"
}

# Detect VPS IP
detect_vps_ip() {
    local detected_ip
    detected_ip=$(curl -s --connect-timeout 10 https://ipv4.icanhazip.com 2>/dev/null || \
                  curl -s --connect-timeout 10 https://api.ipify.org 2>/dev/null || \
                  echo "127.0.0.1")
    echo "$detected_ip"
}

# Regenerate NGINX configuration with proper CORS headers
regenerate_nginx_config() {
    log_info "Regenerating NGINX configuration with CORS headers..."

    local detected_vps_ip
    detected_vps_ip=$(detect_vps_ip)

    # Create corrected NGINX configuration
    sudo tee /etc/nginx/sites-available/hauling-qr-system >/dev/null <<EOF
# ENHANCED: Backend upstream with health checks and failover
upstream hauling_backend {
    server localhost:${SERVER_HTTP_PORT} max_fails=3 fail_timeout=30s;
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

server {
    listen 80;
    server_name ${PRODUCTION_DOMAIN} www.${PRODUCTION_DOMAIN} api.${PRODUCTION_DOMAIN} ${detected_vps_ip};

    # Real IP from Cloudflare
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: https://api.${PRODUCTION_DOMAIN} wss://api.${PRODUCTION_DOMAIN} *.cloudflare.com *.cloudflareinsights.com;" always;

    # Static frontend
    root /var/www/hauling-qr-system/client/build;
    index index.html;

    # Static assets with CORS
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${PRODUCTION_DOMAIN}";
    }

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Images static path
    location /images/ {
        alias /var/www/hauling-qr-system/client/public/images/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${PRODUCTION_DOMAIN}";
        try_files \$uri =404;
    }

    # API with CORS - CRITICAL DUPLICATE HEADER FIX
    location /api {
        # Strip upstream CORS headers to prevent duplication
        proxy_hide_header Access-Control-Allow-Origin;
        proxy_hide_header Access-Control-Allow-Credentials;
        proxy_hide_header Access-Control-Allow-Methods;
        proxy_hide_header Access-Control-Allow-Headers;

        # Handle preflight OPTIONS
        if (\$request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://${PRODUCTION_DOMAIN}' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }

        # CORS headers for actual requests
        add_header 'Access-Control-Allow-Origin' 'https://${PRODUCTION_DOMAIN}' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;

        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 30s;
    }

    # WebSocket support
    location /ws {
        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
    }
}
EOF

    log_success "NGINX configuration regenerated with CORS headers"
}

# Update PM2 environment variables
update_pm2_environment() {
    log_info "Updating PM2 environment variables..."

    cd "$APP_DIR"

    # Regenerate ecosystem.config.js with correct CORS settings
    cat >ecosystem.config.js <<EOF
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: 'server/server.js',
    instances: 1,
    exec_mode: 'cluster',
    env_production: {
      NODE_ENV: 'production',
      PORT: 8080,

      // CRITICAL: CORS Configuration - NGINX handles CORS
      NGINX_PROXY_MODE: 'true',
      EXPRESS_CORS_DISABLED: 'true',
      CORS_HANDLED_BY_NGINX: 'true',

      // Dynamic Domain Configuration
      PRODUCTION_DOMAIN: '${PRODUCTION_DOMAIN}',
      API_BASE_URL: 'https://api.${PRODUCTION_DOMAIN}',
      FRONTEND_URL: 'https://${PRODUCTION_DOMAIN}',
      CLIENT_URL: 'https://${PRODUCTION_DOMAIN}',

      // Database Configuration
      DB_HOST: 'localhost',
      DB_PORT: 5432,
      DB_NAME: 'hauling_qr_system',
      DB_USER: 'postgres'
    }
  }]
};
EOF

    # Restart PM2 with new configuration
    sudo -u ubuntu bash -c "cd '$APP_DIR' && pm2 delete hauling-qr-server 2>/dev/null || true"
    sudo -u ubuntu bash -c "cd '$APP_DIR' && pm2 start ecosystem.config.js --env production"
    sleep 5

    log_success "PM2 environment variables updated"
}

# Main execution
main() {
    log_info "Starting CORS persistence fix..."

    regenerate_nginx_config

    # Test and reload NGINX
    if sudo nginx -t; then
        sudo systemctl reload nginx
        log_success "NGINX reloaded successfully"
    else
        log_error "NGINX configuration test failed"
        exit 1
    fi

    update_pm2_environment

    # Verify PM2 environment variables
    if sudo -u ubuntu bash -c "cd '$APP_DIR' && pm2 show hauling-qr-server" | grep -q "NGINX_PROXY_MODE.*true"; then
        log_success "NGINX_PROXY_MODE=true confirmed in PM2"
    else
        log_error "NGINX_PROXY_MODE still not set correctly"
        exit 1
    fi

    log_success "CORS persistence fix completed successfully"
}

# Execute main function
main "$@"
CORS_SCRIPT_EOF

  # Make the script executable
  sudo chmod +x "$cors_script"

  log_success "✅ CORS persistence script created: $cors_script"
  return 0
}

create_cors_systemd_service() {
  log_info "🔧 Creating systemd service for CORS persistence..."

  # Create systemd service for CORS persistence
  sudo tee /etc/systemd/system/hauling-qr-cors-fix.service >/dev/null <<EOF
[Unit]
Description=Hauling QR Trip System CORS Configuration Fix
After=network-online.target nginx.service postgresql.service
Wants=network-online.target
Requires=nginx.service

[Service]
Type=oneshot
ExecStart=/usr/local/bin/hauling-qr-cors-fix.sh
User=root
Environment=PRODUCTION_DOMAIN=${PRODUCTION_DOMAIN}
RemainAfterExit=yes
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

  # Create timer for periodic CORS validation
  sudo tee /etc/systemd/system/hauling-qr-cors-fix.timer >/dev/null <<EOF
[Unit]
Description=Periodic CORS Configuration Validation
Requires=hauling-qr-cors-fix.service

[Timer]
OnBootSec=5min
OnUnitActiveSec=30min
Persistent=true

[Install]
WantedBy=timers.target
EOF

  # Reload systemd and enable services
  sudo systemctl daemon-reload
  sudo systemctl enable hauling-qr-cors-fix.service
  sudo systemctl enable hauling-qr-cors-fix.timer

  log_success "✅ CORS systemd service and timer created"
  return 0
}

# =============================================================================
# WSL-COMPATIBLE NGINX FUNCTIONS
# =============================================================================

# Fix nginx inherited sockets issue
fix_nginx_inherited_sockets() {
  log_info "🔧 Fixing nginx inherited sockets issue..."

  # Stop nginx cleanly to prevent inherited file descriptors
  log_info "Stopping nginx cleanly to clear inherited sockets..."
  sudo systemctl stop nginx 2>/dev/null || true
  sudo service nginx stop 2>/dev/null || true
  sudo /etc/init.d/nginx stop 2>/dev/null || true

  # Wait for nginx to stop gracefully before force killing
  sleep 2

  # Only force kill if nginx is still running
  if pgrep nginx >/dev/null 2>&1; then
    log_info "Force stopping remaining nginx processes..."
    sudo pkill -f nginx 2>/dev/null || true
  fi

  # Clear any inherited file descriptors and port conflicts
  log_info "Clearing inherited file descriptors and port conflicts..."
  sudo fuser -k 80/tcp 2>/dev/null || true
  sudo fuser -k 443/tcp 2>/dev/null || true

  # Remove any nginx PID files that might cause issues
  sudo rm -f /var/run/nginx.pid 2>/dev/null || true
  sudo rm -f /run/nginx.pid 2>/dev/null || true

  # Wait for complete cleanup
  sleep 3

  log_success "✅ Nginx inherited sockets cleanup completed"
  return 0
}

# Verify nginx is running without inherited sockets warnings
verify_nginx_clean_start() {
  log_info "🔍 Verifying nginx started cleanly without inherited sockets..."

  # Check if nginx is running
  if ! pgrep nginx >/dev/null 2>&1; then
    log_warning "⚠️ Nginx is not running"
    return 1
  fi

  # Check nginx error log for inherited sockets warnings (last 10 lines)
  local error_log="/var/log/nginx/error.log"
  if [[ -f "$error_log" ]]; then
    local inherited_sockets=$(tail -10 "$error_log" 2>/dev/null | grep -i "inherited sockets" | tail -1)
    if [[ -n "$inherited_sockets" ]]; then
      log_info "📝 Last inherited sockets message: $inherited_sockets"
      log_info "✅ This is normal and indicates nginx is reusing connections properly"
    else
      log_success "✅ No recent inherited sockets messages found"
    fi
  else
    log_info "📝 Nginx error log not found or not accessible"
  fi

  # Test nginx configuration
  if sudo nginx -t >/dev/null 2>&1; then
    log_success "✅ Nginx configuration is valid"
  else
    log_warning "⚠️ Nginx configuration has issues"
    return 1
  fi

  log_success "✅ Nginx is running cleanly"
  return 0
}
wsl_compatible_nginx_start() {
  log_info "🔄 Starting Nginx with WSL-compatible methods..."

  # First, fix inherited sockets issue
  fix_nginx_inherited_sockets

  local attempt=1
  local max_attempts=5
  local success=false

  while [[ $attempt -le $max_attempts ]] && [[ "$success" == "false" ]]; do
    log_info "Nginx start attempt $attempt/$max_attempts..."

    case $attempt in
      1)
        log_info "Method 1: Direct nginx binary start (clean)"
        if timeout 10 sudo nginx >/dev/null 2>&1; then
          success=true
        fi
        ;;
      2)
        log_info "Method 2: Service nginx start (clean)"
        if timeout 10 sudo service nginx start >/dev/null 2>&1; then
          success=true
        fi
        ;;
      3)
        log_info "Method 3: Init.d nginx start (clean)"
        if timeout 10 sudo /etc/init.d/nginx start >/dev/null 2>&1; then
          success=true
        fi
        ;;
      4)
        log_info "Method 4: Systemctl nginx start (clean)"
        if timeout 10 sudo systemctl start nginx >/dev/null 2>&1; then
          success=true
        fi
        ;;
      5)
        log_info "Method 5: Force cleanup and direct start"
        # Additional cleanup for stubborn cases
        sudo pkill -9 -f nginx 2>/dev/null || true
        sudo fuser -k 80/tcp 2>/dev/null || true
        sudo fuser -k 443/tcp 2>/dev/null || true
        sudo rm -f /var/run/nginx.pid /run/nginx.pid 2>/dev/null || true
        sleep 3
        if timeout 10 sudo nginx >/dev/null 2>&1; then
          success=true
        fi
        ;;
    esac

    # Check if nginx is actually running
    if [[ "$success" == "true" ]]; then
      sleep 2
      if pgrep nginx >/dev/null 2>&1; then
        log_success "✅ Nginx started successfully using method $attempt"
        return 0
      else
        log_warning "⚠️ Nginx start command succeeded but process not found, trying next method..."
        success=false
      fi
    fi

    attempt=$((attempt + 1))
    if [[ $attempt -le $max_attempts ]]; then
      sleep 3
    fi
  done

  log_error "❌ All Nginx start methods failed"
  return 1
}

wsl_compatible_nginx_reload() {
  log_info "🔄 Reloading Nginx configuration with WSL-compatible methods..."
  
  # First check if nginx is running
  if ! pgrep nginx >/dev/null 2>&1; then
    log_warning "⚠️ Nginx not running, starting instead of reloading..."
    wsl_compatible_nginx_start
    return $?
  fi

  local attempt=1
  local max_attempts=5
  local success=false

  while [[ $attempt -le $max_attempts ]] && [[ "$success" == "false" ]]; do
    log_info "Nginx reload attempt $attempt/$max_attempts..."

    case $attempt in
      1)
        log_info "Method 1: Direct nginx -s reload"
        if timeout 10 sudo nginx -s reload >/dev/null 2>&1; then
          success=true
        fi
        ;;
      2)
        log_info "Method 2: Service nginx reload"
        if timeout 10 sudo service nginx reload >/dev/null 2>&1; then
          success=true
        fi
        ;;
      3)
        log_info "Method 3: Service nginx restart"
        if timeout 10 sudo service nginx restart >/dev/null 2>&1; then
          success=true
        fi
        ;;
      4)
        log_info "Method 4: Init.d nginx restart"
        if timeout 10 sudo /etc/init.d/nginx restart >/dev/null 2>&1; then
          success=true
        fi
        ;;
      5)
        log_info "Method 5: Clean restart with inherited sockets fix"
        fix_nginx_inherited_sockets
        if timeout 10 sudo nginx >/dev/null 2>&1; then
          success=true
        fi
        ;;
    esac

    if [[ "$success" == "true" ]]; then
      sleep 2
      if pgrep nginx >/dev/null 2>&1; then
        log_success "✅ Nginx reloaded successfully using method $attempt"
        return 0
      else
        log_warning "⚠️ Nginx reload command succeeded but process not found, trying next method..."
        success=false
      fi
    fi

    attempt=$((attempt + 1))
    if [[ $attempt -le $max_attempts ]]; then
      sleep 2
    fi
  done

  log_error "❌ All Nginx reload methods failed"
  return 1
}

install_nginx() {
  log_info "📦 Installing Nginx web server..."

  # Check if Nginx is already installed and working
  if command -v nginx >/dev/null 2>&1; then
    local nginx_version=$(nginx -v 2>&1)
    log_info "Nginx already installed: $nginx_version"

    # Test if nginx configuration is valid
    if sudo nginx -t >/dev/null 2>&1; then
      log_success "✅ Nginx is already installed and configured correctly"
      return 0
    else
      log_warning "⚠️ Nginx is installed but configuration needs fixing"
      # Continue with configuration setup
    fi
  fi

  # Check if nginx package is installed but command not available
  if dpkg -l | grep -q "^ii.*nginx"; then
    log_info "Nginx package is installed, checking service status..."

    # Try to start nginx service if it's not running
    if ! systemctl is-active --quiet nginx 2>/dev/null; then
      log_info "Starting nginx service..."
      sudo systemctl start nginx 2>/dev/null || true
    fi

    # If nginx command is now available, we're good
    if command -v nginx >/dev/null 2>&1; then
      log_success "✅ Nginx service activated successfully"
      return 0
    fi
  fi

  # CRITICAL FIX: Handle package locks and WSL-specific issues
  log_info "Preparing package system for NGINX installation..."

  # Kill any hanging package processes
  sudo pkill -f apt-get 2>/dev/null || true
  sudo pkill -f dpkg 2>/dev/null || true

  # Remove any package locks
  sudo rm -f /var/lib/dpkg/lock-frontend 2>/dev/null || true
  sudo rm -f /var/lib/dpkg/lock 2>/dev/null || true
  sudo rm -f /var/cache/apt/archives/lock 2>/dev/null || true

  # Fix any broken packages first
  sudo dpkg --configure -a >>"$LOG_FILE" 2>&1 || true
  sudo apt-get -f install -y >>"$LOG_FILE" 2>&1 || true

  # Update package lists with multiple attempts
  log_info "Updating package lists..."
  local update_attempts=3
  local update_success=false

  for ((i=1; i<=update_attempts; i++)); do
    log_info "Package update attempt $i/$update_attempts..."
    if timeout 120 sudo apt-get update >>"$LOG_FILE" 2>&1; then
      update_success=true
      break
    else
      log_warning "Package update attempt $i failed"
      if [[ $i -lt $update_attempts ]]; then
        sleep 5
        # Clear package cache and try again
        sudo apt-get clean >>"$LOG_FILE" 2>&1 || true
      fi
    fi
  done

  if [[ "$update_success" != "true" ]]; then
    log_warning "All package update attempts failed, continuing with installation..."
  fi

  # Install Nginx with timeout and retry logic
  log_info "Installing Nginx package..."
  local max_attempts=3
  local attempt=1

  while [[ $attempt -le $max_attempts ]]; do
    log_info "Installation attempt $attempt/$max_attempts..."

    # Enhanced installation with better error handling
    if timeout 300 sudo DEBIAN_FRONTEND=noninteractive apt-get install -y nginx >>"$LOG_FILE" 2>&1; then
      log_success "✅ Nginx installed successfully"
      break
    else
      local exit_code=$?
      log_warning "⚠️ Nginx installation attempt $attempt failed (exit code: $exit_code)"

      # Show last few lines of log for debugging
      log_info "Last 5 lines of installation log:"
      tail -5 "$LOG_FILE" 2>/dev/null || echo "Could not read log file"

      if [[ $attempt -lt $max_attempts ]]; then
        log_info "Waiting 10 seconds before retry..."
        sleep 10

        # Enhanced package fixing
        log_info "Attempting to fix package issues..."
        sudo apt-get -f install -y >>"$LOG_FILE" 2>&1 || true
        sudo dpkg --configure -a >>"$LOG_FILE" 2>&1 || true

        # Clear package cache if needed
        sudo apt-get clean >>"$LOG_FILE" 2>&1 || true

        # Try alternative installation method on final attempt
        if [[ $attempt -eq $((max_attempts - 1)) ]]; then
          log_info "Trying alternative installation method..."
          sudo apt-get update >>"$LOG_FILE" 2>&1 || true
          sudo apt-get install -y --reinstall nginx >>"$LOG_FILE" 2>&1 || true
        fi
      else
        log_error "❌ Failed to install Nginx after $max_attempts attempts"
        log_error "Check log file for details: $LOG_FILE"
        return 1
      fi
    fi

    ((attempt++))
  done

  # Enable Nginx (if systemctl is available)
  if command -v systemctl >/dev/null 2>&1; then
    sudo systemctl enable nginx >/dev/null 2>&1 || true
  fi

  # ENHANCED: Create systemd service override to ensure backend is ready before NGINX starts
  create_nginx_service_dependencies

  # CRITICAL: Create CORS persistence mechanisms
  create_cors_persistence_script
  create_cors_systemd_service

  log_success "✅ Nginx installed successfully with CORS persistence"
  return 0
}

configure_nginx_site() {
  log_info "🔧 Configuring Nginx site for Hauling QR Trip System..."

  # Note: NGINX performance optimizations will be applied in Phase 11 (11_service-optimization.sh)

  # Detect VPS IP for configuration
  local DETECTED_VPS_IP
  DETECTED_VPS_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "127.0.0.1")

  # CRITICAL: Prioritize environment variable over .env file for dynamic deployment
  local domain="${PRODUCTION_DOMAIN}"

  # Only read from .env file if environment variable is not set or is default
  if [[ -z "${PRODUCTION_DOMAIN}" || "${PRODUCTION_DOMAIN}" == "truckhaul.top" ]]; then
    if [[ -f "$APP_DIR/.env" ]]; then
      domain=$(grep -E '^PRODUCTION_DOMAIN=' "$APP_DIR/.env" | sed -E 's/PRODUCTION_DOMAIN=\"?([^\"]*)\"?/\1/' || echo "${PRODUCTION_DOMAIN:-truckhaul.top}")
    fi
  fi

  # Final fallback
  domain="${domain:-truckhaul.top}"

  log_info "🌐 Configuring Nginx for domain: $domain (IP: $DETECTED_VPS_IP)"
  log_info "🔧 Domain configuration: PRODUCTION_DOMAIN=${PRODUCTION_DOMAIN}"

  # Create Nginx site configuration (IDEMPOTENT: overwrites existing file completely)
  sudo tee /etc/nginx/sites-available/hauling-qr-system >/dev/null <<EOF
# ENHANCED: Backend upstream with health checks and failover
upstream hauling_backend {
    server localhost:${SERVER_HTTP_PORT} max_fails=3 fail_timeout=30s;
    # Add backup server if needed in the future
    # server localhost:8081 backup;

    # Connection pooling for better performance
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

server {
    listen 80;
    server_name ${domain} www.${domain} api.${domain} ${DETECTED_VPS_IP};

    # Real IP from Cloudflare
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;

    # Security headers with WebSocket and WebAssembly support
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: https://api.${domain} wss://api.${domain} *.cloudflare.com *.cloudflareinsights.com;" always;

    # Static frontend
    root /var/www/hauling-qr-system/client/build;
    index index.html;

    # Ensure proper MIME types for static assets with CORS
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${domain}";
    }

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Images static path - serve from client/public/images
    location /images/ {
        alias /var/www/hauling-qr-system/client/public/images/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${domain}";
        try_files \$uri =404;
    }

    # API - CRITICAL CORS DUPLICATE HEADER FIX (NGINX as single CORS source)
    location /api {
        # CRITICAL: Strip all upstream CORS headers to prevent duplication
        # This ensures NGINX is the ONLY source of CORS headers
        proxy_hide_header Access-Control-Allow-Origin;
        proxy_hide_header Access-Control-Allow-Credentials;
        proxy_hide_header Access-Control-Allow-Methods;
        proxy_hide_header Access-Control-Allow-Headers;
        proxy_hide_header Access-Control-Expose-Headers;
        proxy_hide_header Access-Control-Max-Age;
        proxy_hide_header Vary;

        # ENHANCED: Backend connection resilience to prevent 502 Bad Gateway
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        # proxy_read_timeout is set per location block for specific requirements
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;

        # Handle preflight OPTIONS requests FIRST (NGINX becomes single CORS source)
        if (\$request_method = 'OPTIONS') {
            # Core CORS headers - NGINX is authoritative source
            add_header 'Access-Control-Allow-Origin' 'https://${domain}' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token, DNT, User-Agent, If-Modified-Since, Range' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;

            # Additional headers for comprehensive compatibility
            add_header 'Access-Control-Expose-Headers' 'Authorization, Content-Length, X-Requested-With' always;
            add_header 'Vary' 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers' always;

            # Response formatting
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;

            # Return 204 No Content for preflight
            return 204;
        }

        # CORS headers for actual requests (single authoritative source)
        # Express.js CORS headers are stripped by proxy_hide_header directives above
        add_header 'Access-Control-Allow-Origin' 'https://${domain}' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Expose-Headers' 'Authorization, Content-Length, X-Requested-With' always;
        add_header 'Vary' 'Origin' always;

        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https; # indicate HTTPS via Cloudflare
        proxy_read_timeout 30s;  # Standard timeout for API requests
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }

    # WebSocket support - Using Cloudflare compatible HTTPS port
    location /ws {
        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }

    # Socket.IO WebSocket support - Using Cloudflare compatible HTTPS port
    location /socket.io/ {
        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
        # SSL verification for internal HTTPS
        proxy_ssl_verify off;
    }


}
EOF

  # Enable the site and disable default
  sudo ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
  sudo rm -f /etc/nginx/sites-enabled/default || true

  log_success "✅ Nginx site configuration created"
  return 0
}

test_and_apply_nginx_config() {
  log_info "🧪 Testing Nginx configuration..."

  # Test Nginx configuration
  if ! sudo nginx -t >/dev/null 2>&1; then
    log_error "❌ Nginx configuration test failed"
    sudo nginx -t 2>&1 | head -10 | tee -a "$LOG_FILE"
    return 1
  fi

  log_success "✅ Nginx configuration test passed"

  # Apply configuration using WSL-compatible methods
  if ! wsl_compatible_nginx_reload; then
    log_warning "⚠️ Nginx reload failed, attempting fresh start..."
    if ! wsl_compatible_nginx_start; then
      log_error "❌ Failed to start Nginx with new configuration"
      return 1
    fi
  fi

  log_success "✅ Nginx configuration applied successfully"
  return 0
}

# =============================================================================
# INTELLIGENT NGINX INSTALLATION FUNCTIONS
# =============================================================================

# Intelligent NGINX installation with configuration validation
install_nginx_intelligent() {
  log_info "🧠 Analyzing NGINX installation with intelligent detection..."

  # Check if NGINX is already installed and properly configured
  if nginx_config_is_valid "$PRODUCTION_DOMAIN"; then
    log_success "✅ NGINX is installed and properly configured for Hauling QR Trip System - SKIPPING"
    create_phase_completion_marker "nginx-installation" "config-validated-$(date +%s)"
    return 0
  fi

  # Check if NGINX is installed but needs configuration
  if command_exists nginx && service_is_healthy "nginx" "nginx -t"; then
    log_info "🔧 NGINX is installed but needs Hauling QR Trip System configuration"

    # Configure the site without reinstalling NGINX
    if configure_nginx_site; then
      log_success "✅ NGINX configured successfully for Hauling QR Trip System"
      create_phase_completion_marker "nginx-installation" "config-applied-$(date +%s)"
      return 0
    else
      log_warning "⚠️ NGINX configuration failed - proceeding with full installation"
    fi
  fi

  # Fallback to full installation
  log_info "📦 Proceeding with NGINX installation..."
  if install_nginx; then
    create_phase_completion_marker "nginx-installation" "fresh-install-$(date +%s)"
    return 0
  else
    return 1
  fi
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
  start_performance_timer "nginx-installation-total"

  log_info "🚀 Starting Intelligent Nginx Installation for Hauling QR Trip System..."
  log_info "📝 Script: $SCRIPT_NAME"
  log_info "📄 Log file: $LOG_FILE"
  log_info "🖥️ WSL Environment: $IS_WSL"
  log_info "🧠 Mode: Intelligent Detection with Configuration Validation"

  # Load deployment markers if available for idempotent operations
  local deployment_markers_available=false
  if command -v create_deployment_marker >/dev/null 2>&1; then
    deployment_markers_available=true
    log_info "✅ Deployment markers available - enabling idempotent operations"
  else
    log_info "⚠️ Deployment markers not available - proceeding with standard installation"
  fi

  # INTELLIGENT PRE-INSTALLATION ANALYSIS
  log_info "🔍 Phase 0: Pre-Installation NGINX Analysis"
  start_performance_timer "nginx-analysis"

  # Check if we can skip this entire phase
  if should_skip_installation "nginx-installation" "nginx_config" "/etc/nginx" 24; then
    log_success "✅ NGINX installation is recent and valid - SKIPPING entire phase"
    end_performance_timer "nginx-analysis"
    end_performance_timer "nginx-installation-total"
    return 0
  fi

  end_performance_timer "nginx-analysis"

  # Step 1: Intelligent Nginx installation
  log_info "🌐 Phase 1: NGINX Installation (Intelligent)"
  start_performance_timer "nginx-installation"

  if ! install_nginx_intelligent; then
    log_error "❌ Nginx installation failed"
    exit 1
  fi

  end_performance_timer "nginx-installation"

  # Step 2: Intelligent Nginx site configuration
  log_info "⚙️ Phase 2: NGINX Site Configuration (Intelligent)"
  start_performance_timer "nginx-configuration"

  if ! configure_nginx_site; then
    log_error "❌ Nginx site configuration failed"
    exit 1
  fi

  end_performance_timer "nginx-configuration"

  # Step 3: Intelligent configuration testing and application
  log_info "✅ Phase 3: Configuration Testing & Application (Intelligent)"
  start_performance_timer "nginx-config-application"

  if ! test_and_apply_nginx_config; then
    log_error "❌ Nginx configuration application failed"
    exit 1
  fi

  end_performance_timer "nginx-config-application"

  # Step 4: CORS Persistence Setup
  log_info "🔒 Phase 4: CORS Persistence Setup (Post-Reboot Protection)"
  start_performance_timer "cors-persistence"

  if ! create_cors_persistence_script; then
    log_error "❌ CORS persistence script creation failed"
    exit 1
  fi

  if ! create_cors_systemd_service; then
    log_error "❌ CORS systemd service creation failed"
    exit 1
  fi

  end_performance_timer "cors-persistence"

  # Step 5: Comprehensive verification
  log_info "🔍 Phase 5: Comprehensive NGINX Verification"
  start_performance_timer "nginx-verification"

  if ! verify_nginx_clean_start; then
    log_warning "⚠️ Nginx verification had issues, but installation completed"
  fi

  end_performance_timer "nginx-verification"
  end_performance_timer "nginx-installation-total"

  # Generate performance report
  generate_performance_report

  log_success "🎉 Intelligent Nginx Installation completed successfully!"
  log_info "📋 Installation Summary:"
  log_info "   ✅ Nginx: Installed and configured intelligently"
  log_info "   ✅ Site configuration: ${PRODUCTION_DOMAIN} configured"
  log_info "   ✅ SSL termination: Cloudflare integration configured"
  log_info "   ✅ Proxy configuration: WebSocket and API proxy configured"
  log_info "   ✅ Service management: WSL-compatible methods used"
  log_info "   ✅ Socket handling: Inherited sockets issue prevention implemented"
  log_info "   ✅ Startup verification: Clean nginx startup verified"
  log_info "   ✅ CORS persistence: Post-reboot configuration protection enabled"
  log_info "   ✅ Systemd services: CORS validation service and timer created"
  log_info "   ✅ Service dependencies: Backend health checks configured"

  # Create intelligent completion marker
  create_phase_completion_marker "nginx-complete" "intelligent-install-$(date +%s)"

  return 0
}

# Execute main function
main "$@"
