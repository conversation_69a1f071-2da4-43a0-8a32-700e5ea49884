/**
 * Shift Type Detection Utility
 * 
 * Provides consistent shift type detection logic across the application
 * Based on business rules defined in requirements:
 * - Day shift: 06:00 AM to 06:00 PM (6:00 to 18:00 inclusive)
 * - Night shift: 06:01 PM to 05:59 AM (18:01 to 05:59)
 */

class ShiftTypeDetector {
  /**
   * Detect shift type based on current time or provided timestamp
   * @param {Date|string} timestamp - Optional timestamp, defaults to current time
   * @returns {string} - 'day' or 'night'
   */
  static detectShiftType(timestamp = null) {
    const currentTimestamp = timestamp ? new Date(timestamp) : new Date();
    
    // Check for invalid date
    if (isNaN(currentTimestamp.getTime())) {
      throw new Error('Invalid timestamp provided to detectShiftType');
    }
    
    // Use local hours and minutes to match the server's timezone
    const currentHour = currentTimestamp.getHours();
    const currentMinute = currentTimestamp.getMinutes();
    
    // Day shift: 06:00 AM to 06:00 PM (hours 6-17, and hour 18 only if minutes and seconds are 00)
    if (currentHour >= 6 && currentHour < 18) {
      return 'day';
    } else if (currentHour === 18 && currentMinute === 0) {
      // Exactly 18:00:00 (6:00 PM) is still day shift
      const currentSecond = currentTimestamp.getSeconds();
      if (currentSecond === 0) {
        return 'day';
      } else {
        return 'night';
      }
    } else {
      // Night shift: 18:01 onwards, 19:00-23:59, or 00:00-05:59
      return 'night';
    }
  }

  /**
   * Check if a given time falls within day shift hours
   * @param {Date|string} timestamp - Timestamp to check
   * @returns {boolean} - true if day shift, false if night shift
   */
  static isDayShift(timestamp = null) {
    return this.detectShiftType(timestamp) === 'day';
  }

  /**
   * Check if a given time falls within night shift hours
   * @param {Date|string} timestamp - Timestamp to check
   * @returns {boolean} - true if night shift, false if day shift
   */
  static isNightShift(timestamp = null) {
    return this.detectShiftType(timestamp) === 'night';
  }

  /**
   * Get shift type display name
   * @param {string} shiftType - 'day' or 'night'
   * @returns {string} - Display name
   */
  static getShiftTypeDisplay(shiftType) {
    const types = {
      'day': 'Day Shift (06:00 AM - 06:00 PM)',
      'night': 'Night Shift (06:01 PM - 05:59 AM)',
      'custom': 'Custom Shift'
    };
    
    return types[shiftType] || shiftType;
  }

  /**
   * Get shift boundaries for a given shift type
   * @param {string} shiftType - 'day' or 'night'
   * @returns {object} - Object with start and end times
   */
  static getShiftBoundaries(shiftType) {
    if (shiftType === 'day') {
      return {
        start: '06:00:00',
        end: '18:00:00',
        description: 'Day shift: 06:00 AM to 06:00 PM'
      };
    } else if (shiftType === 'night') {
      return {
        start: '18:01:00',
        end: '05:59:00',
        description: 'Night shift: 06:01 PM to 05:59 AM (next day)',
        overnight: true
      };
    } else {
      return {
        start: null,
        end: null,
        description: 'Custom shift with flexible timing'
      };
    }
  }

  /**
   * Validate if a time range matches the expected shift type
   * @param {string} startTime - Start time in HH:MM:SS format
   * @param {string} endTime - End time in HH:MM:SS format
   * @param {string} expectedShiftType - Expected shift type
   * @returns {object} - Validation result with isValid and message
   */
  static validateShiftTiming(startTime, endTime, expectedShiftType) {
    const boundaries = this.getShiftBoundaries(expectedShiftType);
    
    if (expectedShiftType === 'custom') {
      return {
        isValid: true,
        message: 'Custom shifts allow flexible timing'
      };
    }

    // For day shifts, validate that times fall within day boundaries
    if (expectedShiftType === 'day') {
      const startHour = parseInt(startTime.split(':')[0]);
      const endHour = parseInt(endTime.split(':')[0]);
      const endMinute = parseInt(endTime.split(':')[1]);
      
      // Day shift: start must be >= 06:00 and end must be <= 18:00
      // Also, end time must be after start time (no overnight shifts for day)
      const validStart = startHour >= 6;
      const validEnd = endHour < 18 || (endHour === 18 && endMinute === 0);
      const noOvernight = endHour > startHour || (endHour === startHour && endMinute >= parseInt(startTime.split(':')[1]));
      
      if (validStart && validEnd && noOvernight) {
        return {
          isValid: true,
          message: 'Shift timing matches day shift boundaries'
        };
      } else {
        return {
          isValid: false,
          message: 'Day shift must be between 06:00 AM and 06:00 PM with no overnight span'
        };
      }
    }

    // For night shifts, validate overnight pattern
    if (expectedShiftType === 'night') {
      const startHour = parseInt(startTime.split(':')[0]);
      const startMinute = parseInt(startTime.split(':')[1]);
      const endHour = parseInt(endTime.split(':')[0]);
      
      // Night shift can start after 18:00 (18:01+) or end before 06:00
      const validStart = startHour > 18 || (startHour === 18 && startMinute > 0);
      const validEnd = endHour <= 6;
      
      if (validStart || validEnd) {
        return {
          isValid: true,
          message: 'Shift timing matches night shift boundaries'
        };
      } else {
        return {
          isValid: false,
          message: 'Night shift must start after 06:00 PM or end before 06:00 AM'
        };
      }
    }

    return {
      isValid: false,
      message: 'Unknown shift type'
    };
  }

  /**
   * Get current shift type with detailed information
   * @param {Date|string} timestamp - Optional timestamp
   * @returns {object} - Detailed shift information
   */
  static getCurrentShiftInfo(timestamp = null) {
    const currentTimestamp = timestamp ? new Date(timestamp) : new Date();
    const shiftType = this.detectShiftType(currentTimestamp);
    const boundaries = this.getShiftBoundaries(shiftType);
    
    return {
      shiftType,
      timestamp: currentTimestamp.toISOString(),
      currentTime: currentTimestamp.toISOString().split('T')[1].split('.')[0],
      currentDate: currentTimestamp.toISOString().split('T')[0],
      boundaries,
      display: this.getShiftTypeDisplay(shiftType)
    };
  }
}

module.exports = ShiftTypeDetector;