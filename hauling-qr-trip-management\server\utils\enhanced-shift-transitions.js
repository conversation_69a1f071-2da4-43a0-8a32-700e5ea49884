/**
 * Enhanced Shift Transition System
 * Purpose: Implement automatic shift status transitions with edge case handling
 * Issues Addressed: ISSUE 1 - Missing Automatic Shift Status Transition
 * 
 * Features:
 * 1. Automatic activation (scheduled → active)
 * 2. Automatic completion (active → completed)
 * 3. Edge case handling: overlapping shifts, missed activations, midnight crossings
 * 4. Integration with existing captureActiveDriverInfo function
 * 5. Date range and recurrence pattern support
 * 6. Performance optimization with <300ms targets
 */

const { getClient } = require('../config/database');

class EnhancedShiftTransitionManager {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.transitionInterval = 60000; // 1 minute
    this.logger = console; // Can be replaced with proper logger
  }

  /**
   * Start the automatic shift transition system
   */
  start() {
    if (this.isRunning) {
      this.logger.warn('Shift transition system is already running');
      return;
    }

    this.logger.info('Starting enhanced shift transition system');
    this.isRunning = true;
    
    // Run immediately, then every minute
    this.runTransitions();
    this.intervalId = setInterval(() => {
      this.runTransitions();
    }, this.transitionInterval);
  }

  /**
   * Stop the automatic shift transition system
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('Stopping enhanced shift transition system');
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Run all shift transitions
   */
  async runTransitions() {
    if (!this.isRunning) {
      return;
    }

    const startTime = Date.now();
    let client;

    try {
      client = await getClient();
      
      // Run transitions in sequence
      const activationResults = await this.processShiftActivations(client);
      const completionResults = await this.processShiftCompletions(client);
      
      const duration = Date.now() - startTime;
      
      if (activationResults.activated > 0 || completionResults.completed > 0) {
        this.logger.info('Shift transitions completed', {
          activated: activationResults.activated,
          completed: completionResults.completed,
          duration_ms: duration
        });
      }

      // Performance monitoring - ensure <300ms target
      if (duration > 300) {
        this.logger.warn('Shift transition performance exceeded target', {
          duration_ms: duration
        });
      }

    } catch (error) {
      this.logger.error('Shift transition error:', error);
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Process shift activations with enhanced logic
   */
  async processShiftActivations(client) {
    const activationQuery = `
      WITH shifts_to_activate AS (
        SELECT
          ds.id,
          ds.truck_id,
          ds.driver_id,
          ds.shift_type,
          ds.recurrence_pattern,
          ds.start_time,
          ds.end_time,
          ds.start_date,
          ds.end_date,
          d.full_name as driver_name,
          dt.truck_number
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        WHERE ds.status = 'scheduled'
          AND (
            -- Single date shifts (using start_date = end_date)
            (ds.recurrence_pattern = 'single' AND ds.start_date = CURRENT_DATE AND ds.start_date = ds.end_date)
            OR
            -- Date range shifts with recurrence patterns
            (ds.recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
              AND (
                -- Daily: every day in range
                (ds.recurrence_pattern = 'daily')
                OR
                -- Weekly: same day of week as start_date
                (ds.recurrence_pattern = 'weekly' AND EXTRACT(DOW FROM CURRENT_DATE) = EXTRACT(DOW FROM ds.start_date))
                OR
                -- Weekdays: Monday to Friday
                (ds.recurrence_pattern = 'weekdays' AND EXTRACT(DOW FROM CURRENT_DATE) BETWEEN 1 AND 5)
                OR
                -- Weekends: Saturday and Sunday
                (ds.recurrence_pattern = 'weekends' AND EXTRACT(DOW FROM CURRENT_DATE) IN (0, 6))
                OR
                -- Custom: handled by application logic
                (ds.recurrence_pattern = 'custom')
              )
            )
          )
          AND CURRENT_TIME >= ds.start_time
          AND (
            -- For regular shifts (same day): check if current time is before end time
            (ds.end_time >= ds.start_time AND CURRENT_TIME < ds.end_time)
            OR
            -- For overnight shifts: active from start time until end time next day
            (ds.end_time < ds.start_time AND
             ((CURRENT_DATE = ds.start_date AND CURRENT_TIME >= ds.start_time) OR
              (CURRENT_DATE = ds.start_date + INTERVAL '1 day' AND CURRENT_TIME < ds.end_time)))
          )
      )
      SELECT * FROM shifts_to_activate
      ORDER BY truck_id, start_time
    `;

    const shiftsToActivate = await client.query(activationQuery);
    let activatedCount = 0;

    for (const shift of shiftsToActivate.rows) {
      try {
        await client.query('BEGIN');

        // Handle overlapping shifts - deactivate any currently active shifts for this truck
        const deactivationResult = await client.query(`
          UPDATE driver_shifts 
          SET status = 'completed', 
              updated_at = CURRENT_TIMESTAMP
          WHERE truck_id = $1 
            AND status = 'active'
            AND id != $2
          RETURNING id
        `, [shift.truck_id, shift.id]);

        if (deactivationResult.rows.length > 0) {
          this.logger.info('Deactivated overlapping shifts', {
            truck_id: shift.truck_id,
            deactivated_shift_ids: deactivationResult.rows.map(r => r.id),
            new_shift_id: shift.id
          });
        }

        // Activate the new shift
        await client.query(`
          UPDATE driver_shifts 
          SET status = 'active', 
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [shift.id]);

        await client.query('COMMIT');
        activatedCount++;

        this.logger.info('Shift activated', {
          shift_id: shift.id,
          truck_number: shift.truck_number,
          driver_name: shift.driver_name,
          shift_type: shift.shift_type,
          recurrence_pattern: shift.recurrence_pattern
        });

      } catch (error) {
        await client.query('ROLLBACK');
        this.logger.error('Failed to activate shift', {
          shift_id: shift.id,
          error: error.message
        });
      }
    }

    return { activated: activatedCount };
  }

  /**
   * Process shift completions with enhanced logic
   * DISABLED: Automatic completion violates manual-only completion requirement
   */
  async processShiftCompletions(client) {
    // DISABLED: Automatic shift completion is not allowed per system requirements
    // Only manual completion through Settings page is permitted

    // Only log completion message if not suppressed
    if (process.env.SUPPRESS_SHIFT_COMPLETION_MESSAGES !== 'true') {
      this.logger.info('Automatic shift completion disabled - manual completion only');
    }
    return { completed: 0 };

    /* DISABLED CODE - Automatic completion logic removed
    // This code was automatically completing shifts based on time logic
    // which violates the manual-only completion requirement
    /*
    const completionQuery = `
      WITH shifts_to_complete AS (
        SELECT
          ds.id,
          ds.truck_id,
          ds.driver_id,
          ds.shift_type,
          ds.recurrence_pattern,
          ds.start_time,
          ds.end_time,
          ds.start_date,
          ds.end_date,
          d.full_name as driver_name,
          dt.truck_number
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        WHERE ds.status = 'active'
          AND (
            -- Single date shifts (using start_date = end_date)
            (ds.recurrence_pattern = 'single' AND ds.start_date = CURRENT_DATE AND ds.start_date = ds.end_date)
            OR
            -- Date range shifts that are active today
            (ds.recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date)
          )
          AND (
            -- For regular shifts (same day): check if current time is past end time
            (ds.end_time >= ds.start_time AND CURRENT_TIME > ds.end_time)
            OR
            -- For overnight shifts: only complete when we're on the next day AND past end time
            (ds.end_time < ds.start_time AND
             CURRENT_DATE = ds.start_date + INTERVAL '1 day' AND
             CURRENT_TIME > ds.end_time)
          )
      )
      SELECT * FROM shifts_to_complete
      ORDER BY truck_id, end_time
    `;

    const shiftsToComplete = await client.query(completionQuery);
    let completedCount = 0;

    for (const shift of shiftsToComplete.rows) {
      try {
        await client.query('BEGIN');

        // Complete the shift
        await client.query(`
          UPDATE driver_shifts
          SET status = 'completed',
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [shift.id]);

        await client.query('COMMIT');
        completedCount++;

        this.logger.info('Shift completed', {
          shift_id: shift.id,
          truck_number: shift.truck_number,
          driver_name: shift.driver_name,
          shift_type: shift.shift_type,
          recurrence_pattern: shift.recurrence_pattern
        });

      } catch (error) {
        await client.query('ROLLBACK');
        this.logger.error('Failed to complete shift', {
          shift_id: shift.id,
          error: error.message
        });
      }
    }

    return { completed: completedCount };
    */ // End of disabled automatic completion code
  }

  /**
   * Enhanced function to get current active driver (integrates with captureActiveDriverInfo)
   */
  async getCurrentActiveDriverEnhanced(truckId, timestamp = new Date()) {
    let client;
    
    try {
      client = await getClient();
      
      const driverQuery = `
        SELECT 
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type,
          COALESCE(ds.display_type, ds.shift_type) as display_type,
          ds.recurrence_pattern,
          ds.start_time,
          ds.end_time
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = $1
          AND ds.status = 'active'
          AND (
            -- Single date shifts (using start_date = end_date)
            (ds.recurrence_pattern = 'single' AND ds.start_date = $2::date AND ds.start_date = ds.end_date)
            OR
            -- Date range shifts with recurrence patterns
            (ds.recurrence_pattern != 'single' AND $2::date BETWEEN ds.start_date AND ds.end_date
              AND (
                (ds.recurrence_pattern = 'daily')
                OR
                (ds.recurrence_pattern = 'weekly' AND EXTRACT(DOW FROM $2::date) = EXTRACT(DOW FROM ds.start_date))
                OR
                (ds.recurrence_pattern = 'weekdays' AND EXTRACT(DOW FROM $2::date) BETWEEN 1 AND 5)
                OR
                (ds.recurrence_pattern = 'weekends' AND EXTRACT(DOW FROM $2::date) IN (0, 6))
                OR
                (ds.recurrence_pattern = 'custom')
              )
            )
          )
          AND $2::time BETWEEN ds.start_time AND 
              CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
              END
        ORDER BY ds.created_at DESC
        LIMIT 1
      `;

      const result = await client.query(driverQuery, [truckId, timestamp]);
      return result.rows[0] || null;

    } catch (error) {
      this.logger.error('Error getting current active driver', {
        truck_id: truckId,
        timestamp: timestamp.toISOString(),
        error: error.message
      });
      return null;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Manual shift activation (for testing or manual override)
   */
  async activateShift(shiftId) {
    let client;
    
    try {
      client = await getClient();
      await client.query('BEGIN');

      // Get shift details
      const shiftResult = await client.query(`
        SELECT truck_id, status FROM driver_shifts WHERE id = $1
      `, [shiftId]);

      if (shiftResult.rows.length === 0) {
        throw new Error('Shift not found');
      }

      const shift = shiftResult.rows[0];
      
      if (shift.status !== 'scheduled') {
        throw new Error(`Cannot activate shift with status: ${shift.status}`);
      }

      // Deactivate any currently active shifts for this truck
      await client.query(`
        UPDATE driver_shifts 
        SET status = 'completed', updated_at = CURRENT_TIMESTAMP
        WHERE truck_id = $1 AND status = 'active' AND id != $2
      `, [shift.truck_id, shiftId]);

      // Activate the shift
      await client.query(`
        UPDATE driver_shifts 
        SET status = 'active', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [shiftId]);

      await client.query('COMMIT');
      
      this.logger.info('Shift manually activated', { shift_id: shiftId });
      return { success: true, message: 'Shift activated successfully' };

    } catch (error) {
      if (client) {
        await client.query('ROLLBACK');
      }
      this.logger.error('Manual shift activation failed', {
        shift_id: shiftId,
        error: error.message
      });
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Get system status and statistics
   */
  async getSystemStatus() {
    let client;
    
    try {
      client = await getClient();
      
      const statusQuery = `
        SELECT 
          status,
          COUNT(*) as count
        FROM driver_shifts 
        WHERE (recurrence_pattern = 'single' AND start_date = CURRENT_DATE AND start_date = end_date) OR
              (recurrence_pattern != 'single' AND CURRENT_DATE BETWEEN start_date AND end_date)
        GROUP BY status
        ORDER BY status
      `;

      const statusResult = await client.query(statusQuery);
      
      const stats = {
        is_running: this.isRunning,
        transition_interval_ms: this.transitionInterval,
        shift_counts: {}
      };

      statusResult.rows.forEach(row => {
        stats.shift_counts[row.status] = parseInt(row.count);
      });

      return stats;

    } catch (error) {
      this.logger.error('Error getting system status', error);
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }
}

// Create singleton instance
const shiftTransitionManager = new EnhancedShiftTransitionManager();

module.exports = {
  EnhancedShiftTransitionManager,
  shiftTransitionManager
};
