/**
 * Enhanced Analytics Service
 * Provides comprehensive analytics for the Hauling QR Trip System
 */

import { api } from './api';

class AnalyticsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  // Cache management
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // Enhanced Trip Analytics
  async getTripAnalytics(timeRange = '7d') {
    const cacheKey = `trip_analytics_${timeRange}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get(`/analytics/trips?range=${timeRange}`);
      const analytics = {
        ...response.data,
        // Enhanced metrics
        returnTravelMetrics: this.calculateReturnTravelMetrics(response.data.trips || []),
        uncertaintyMetrics: this.calculateUncertaintyMetrics(response.data.trips || []),
        workflowEfficiency: this.calculateWorkflowEfficiency(response.data.trips || [])
      };
      
      this.setCachedData(cacheKey, analytics);
      return analytics;
    } catch (error) {
      console.error('Failed to fetch trip analytics:', error);
      throw error;
    }
  }

  // Return Travel Metrics
  calculateReturnTravelMetrics(trips) {
    const completedTrips = trips.filter(trip => trip.status === 'trip_completed');
    const tripsWithReturnTravel = completedTrips.filter(trip => {
      try {
        const notes = JSON.parse(trip.notes || '{}');
        return notes.workflow_type === 'enhanced_with_return_travel';
      } catch (e) {
        return false;
      }
    });

    const returnTravelDurations = tripsWithReturnTravel.map(trip => {
      try {
        const notes = JSON.parse(trip.notes || '{}');
        return notes.return_travel_duration_minutes || 0;
      } catch (e) {
        return 0;
      }
    }).filter(duration => duration > 0);

    return {
      totalTripsWithReturnTravel: tripsWithReturnTravel.length,
      percentageWithReturnTravel: completedTrips.length > 0 
        ? (tripsWithReturnTravel.length / completedTrips.length * 100).toFixed(1)
        : 0,
      averageReturnTravelTime: returnTravelDurations.length > 0
        ? (returnTravelDurations.reduce((sum, duration) => sum + duration, 0) / returnTravelDurations.length).toFixed(1)
        : 0,
      totalReturnTravelTime: returnTravelDurations.reduce((sum, duration) => sum + duration, 0),
      minReturnTravelTime: returnTravelDurations.length > 0 ? Math.min(...returnTravelDurations) : 0,
      maxReturnTravelTime: returnTravelDurations.length > 0 ? Math.max(...returnTravelDurations) : 0
    };
  }

  // Uncertainty Metrics (for dynamic assignments)
  calculateUncertaintyMetrics(trips) {
    const dynamicTrips = trips.filter(trip => {
      try {
        const notes = JSON.parse(trip.notes || '{}');
        return notes.assignment_type === 'auto_created' || notes.creation_method === 'dynamic_assignment';
      } catch (e) {
        return false;
      }
    });

    const confirmedLocations = trips.filter(trip => {
      try {
        const notes = JSON.parse(trip.notes || '{}');
        return notes.location_certainty === 'confirmed';
      } catch (e) {
        return true; // Default to confirmed for traditional trips
      }
    });

    return {
      totalDynamicTrips: dynamicTrips.length,
      percentageDynamic: trips.length > 0 
        ? (dynamicTrips.length / trips.length * 100).toFixed(1)
        : 0,
      confirmedLocationRate: trips.length > 0
        ? (confirmedLocations.length / trips.length * 100).toFixed(1)
        : 0,
      uncertaintyRate: trips.length > 0
        ? ((trips.length - confirmedLocations.length) / trips.length * 100).toFixed(1)
        : 0
    };
  }

  // Workflow Efficiency Metrics
  calculateWorkflowEfficiency(trips) {
    const completedTrips = trips.filter(trip => trip.status === 'trip_completed');
    
    const phaseCompletionTimes = completedTrips.map(trip => {
      const loadingStart = trip.loading_start_time ? new Date(trip.loading_start_time) : null;
      const loadingEnd = trip.loading_end_time ? new Date(trip.loading_end_time) : null;
      const unloadingStart = trip.unloading_start_time ? new Date(trip.unloading_start_time) : null;
      const unloadingEnd = trip.unloading_end_time ? new Date(trip.unloading_end_time) : null;
      const tripCompleted = trip.trip_completed_time ? new Date(trip.trip_completed_time) : null;

      return {
        loadingDuration: loadingStart && loadingEnd 
          ? (loadingEnd - loadingStart) / (1000 * 60) : 0,
        travelDuration: loadingEnd && unloadingStart 
          ? (unloadingStart - loadingEnd) / (1000 * 60) : 0,
        unloadingDuration: unloadingStart && unloadingEnd 
          ? (unloadingEnd - unloadingStart) / (1000 * 60) : 0,
        totalDuration: loadingStart && tripCompleted 
          ? (tripCompleted - loadingStart) / (1000 * 60) : 0
      };
    }).filter(times => times.totalDuration > 0);

    const averages = phaseCompletionTimes.reduce((acc, times) => ({
      loading: acc.loading + times.loadingDuration,
      travel: acc.travel + times.travelDuration,
      unloading: acc.unloading + times.unloadingDuration,
      total: acc.total + times.totalDuration
    }), { loading: 0, travel: 0, unloading: 0, total: 0 });

    const count = phaseCompletionTimes.length;

    return {
      averageLoadingTime: count > 0 ? (averages.loading / count).toFixed(1) : 0,
      averageTravelTime: count > 0 ? (averages.travel / count).toFixed(1) : 0,
      averageUnloadingTime: count > 0 ? (averages.unloading / count).toFixed(1) : 0,
      averageTotalTime: count > 0 ? (averages.total / count).toFixed(1) : 0,
      completionRate: trips.length > 0 
        ? (completedTrips.length / trips.length * 100).toFixed(1)
        : 0,
      totalTripsAnalyzed: count
    };
  }

  // Performance Analytics
  async getPerformanceAnalytics(timeRange = '24h') {
    const cacheKey = `performance_analytics_${timeRange}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get(`/analytics/performance?range=${timeRange}`);
      const analytics = {
        ...response.data,
        scanPerformance: this.calculateScanPerformance(response.data.scans || []),
        systemHealth: this.calculateSystemHealth(response.data.metrics || {})
      };
      
      this.setCachedData(cacheKey, analytics);
      return analytics;
    } catch (error) {
      console.error('Failed to fetch performance analytics:', error);
      throw error;
    }
  }

  // Scan Performance Metrics
  calculateScanPerformance(scans) {
    const processingTimes = scans.map(scan => scan.processing_time || 0).filter(time => time > 0);
    
    return {
      totalScans: scans.length,
      averageProcessingTime: processingTimes.length > 0
        ? (processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length).toFixed(0)
        : 0,
      fastScans: processingTimes.filter(time => time < 300).length, // < 300ms
      slowScans: processingTimes.filter(time => time > 1000).length, // > 1s
      performanceScore: processingTimes.length > 0
        ? Math.max(0, 100 - (processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length / 10)).toFixed(1)
        : 100
    };
  }

  // System Health Metrics
  calculateSystemHealth(metrics) {
    return {
      uptime: metrics.uptime || 0,
      memoryUsage: metrics.memory_usage || 0,
      cpuUsage: metrics.cpu_usage || 0,
      databaseConnections: metrics.db_connections || 0,
      errorRate: metrics.error_rate || 0,
      healthScore: this.calculateHealthScore(metrics)
    };
  }

  calculateHealthScore(metrics) {
    let score = 100;
    
    // Deduct points for high resource usage
    if (metrics.memory_usage > 80) score -= 20;
    else if (metrics.memory_usage > 60) score -= 10;
    
    if (metrics.cpu_usage > 80) score -= 20;
    else if (metrics.cpu_usage > 60) score -= 10;
    
    // Deduct points for errors
    if (metrics.error_rate > 5) score -= 30;
    else if (metrics.error_rate > 1) score -= 15;
    
    return Math.max(0, score);
  }

  // Real-time Analytics
  async getRealTimeAnalytics() {
    try {
      const response = await api.get('/analytics/realtime');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch real-time analytics:', error);
      throw error;
    }
  }

  // Export Analytics Data
  async exportAnalytics(format = 'csv', timeRange = '30d') {
    try {
      const response = await api.get(`/analytics/export?format=${format}&range=${timeRange}`, {
        responseType: 'blob'
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `analytics_${timeRange}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return true;
    } catch (error) {
      console.error('Failed to export analytics:', error);
      throw error;
    }
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
  }
}

export const analyticsService = new AnalyticsService();
export default analyticsService;
