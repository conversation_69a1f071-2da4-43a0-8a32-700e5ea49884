import React, { useState, useEffect } from 'react';
import { getApiBaseUrl } from '../../../../services/api';
import toast from 'react-hot-toast';

const ShiftPerformanceAnalytics = ({ dateRange, loading: parentLoading }) => {
  const [shiftData, setShiftData] = useState(null);
  const [comparisonData, setComparisonData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('performance');
  const [filters, setFilters] = useState({
    truck_id: '',
    shift_type: '',
    driver_id: ''
  });

  // Load shift performance data
  const loadShiftPerformanceData = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        start_date: dateRange.start,
        end_date: dateRange.end,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/analytics/shift-performance?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch shift performance data');
      }
      
      const result = await response.json();
      setShiftData(result.data);
      
    } catch (error) {
      console.error('Error loading shift performance data:', error);
      toast.error('Failed to load shift performance data');
    } finally {
      setLoading(false);
    }
  };

  // Load driver shift comparison data
  const loadDriverComparisonData = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        start_date: dateRange.start,
        end_date: dateRange.end,
        ...(filters.driver_id && { driver_id: filters.driver_id })
      });
      
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/analytics/driver-shift-comparison?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch driver comparison data');
      }
      
      const result = await response.json();
      setComparisonData(result.data);
      
    } catch (error) {
      console.error('Error loading driver comparison data:', error);
      toast.error('Failed to load driver comparison data');
    } finally {
      setLoading(false);
    }
  };

  // Load data when component mounts or dependencies change
  useEffect(() => {
    if (activeTab === 'performance') {
      loadShiftPerformanceData();
    } else if (activeTab === 'comparison') {
      loadDriverComparisonData();
    }
  }, [dateRange, activeTab, filters]);

  const getShiftTypeColor = (shiftType) => {
    const colors = {
      'day': 'text-yellow-600 bg-yellow-50',
      'night': 'text-blue-600 bg-blue-50',
      'custom': 'text-purple-600 bg-purple-50',
      'regular': 'text-gray-600 bg-gray-50'
    };
    return colors[shiftType] || 'text-gray-600 bg-gray-50';
  };

  const getPerformanceColor = (score) => {
    if (score >= 90) return 'text-green-600 bg-green-50';
    if (score >= 75) return 'text-yellow-600 bg-yellow-50';
    if (score >= 60) return 'text-orange-600 bg-orange-50';
    return 'text-red-600 bg-red-50';
  };

  if (parentLoading || loading) {
    return (
      <div className="animate-pulse bg-secondary-200 h-96 rounded-lg"></div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 overflow-hidden">
      <div className="px-6 py-4 bg-secondary-50 border-b border-secondary-200">
        <h3 className="text-lg font-medium text-secondary-900">
          🔄 Multi-Driver Shift Performance Analytics
        </h3>
        <p className="text-sm text-secondary-500 mt-1">
          Performance tracking across different driver shifts and schedules
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-secondary-200">
        <nav className="flex space-x-8 px-6">
          <button
            onClick={() => setActiveTab('performance')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'performance'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
            }`}
          >
            Shift Performance
          </button>
          <button
            onClick={() => setActiveTab('comparison')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'comparison'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
            }`}
          >
            Driver Comparison
          </button>
        </nav>
      </div>

      {/* Filters */}
      <div className="px-6 py-4 bg-gray-50 border-b border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Shift Type
            </label>
            <select
              value={filters.shift_type}
              onChange={(e) => setFilters(prev => ({ ...prev, shift_type: e.target.value }))}
              className="w-full px-3 py-2 border border-secondary-300 rounded-md text-sm"
            >
              <option value="">All Shifts</option>
              <option value="day">Day Shift</option>
              <option value="night">Night Shift</option>
              <option value="custom">Custom Shift</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Truck ID
            </label>
            <input
              type="text"
              value={filters.truck_id}
              onChange={(e) => setFilters(prev => ({ ...prev, truck_id: e.target.value }))}
              placeholder="Enter truck ID"
              className="w-full px-3 py-2 border border-secondary-300 rounded-md text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Driver ID
            </label>
            <input
              type="text"
              value={filters.driver_id}
              onChange={(e) => setFilters(prev => ({ ...prev, driver_id: e.target.value }))}
              placeholder="Enter driver ID"
              className="w-full px-3 py-2 border border-secondary-300 rounded-md text-sm"
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'performance' && shiftData && (
          <div>
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-600">
                  {shiftData.summary.uniqueDriverTruckShifts}
                </div>
                <div className="text-sm text-blue-700">Driver-Truck-Shift Combinations</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-green-600">
                  {shiftData.summary.trucksWithData}
                </div>
                <div className="text-sm text-green-700">Trucks with Data</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-purple-600">
                  {shiftData.summary.driversWithData}
                </div>
                <div className="text-sm text-purple-700">Drivers with Data</div>
              </div>
              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-yellow-600">
                  {shiftData.summary.overallCompletionRate}%
                </div>
                <div className="text-sm text-yellow-700">Overall Completion Rate</div>
              </div>
            </div>

            {/* Performance Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="bg-secondary-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Driver & Truck
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Shift Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Trips
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Completion Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Avg Duration
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Performance Score
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {shiftData.shiftPerformance.map((item, index) => (
                    <tr key={`${item.truckId}-${item.driverId}-${item.shiftType}`} className="hover:bg-secondary-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-secondary-900">
                            {item.driverName}
                          </div>
                          <div className="text-sm text-secondary-500">
                            {item.truckNumber} • {item.employeeId}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getShiftTypeColor(item.shiftType)}`}>
                          {item.shiftType}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {item.completedTrips}/{item.totalTrips}
                        </div>
                        <div className="text-xs text-secondary-500">
                          {item.shiftsWorked} shifts, {item.daysWorked} days
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {item.completionRate}%
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {item.avgTripDuration}m
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPerformanceColor(item.performanceScore)}`}>
                          {item.performanceScore}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'comparison' && comparisonData && (
          <div>
            <h4 className="text-lg font-medium text-secondary-900 mb-4">
              Driver Performance Across Different Shifts
            </h4>
            
            <div className="space-y-6">
              {comparisonData.driverComparisons.map((driver) => (
                <div key={driver.driverId} className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h5 className="text-lg font-medium text-secondary-900">
                        {driver.driverName}
                      </h5>
                      <p className="text-sm text-secondary-500">
                        Employee ID: {driver.employeeId}
                      </p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(driver.shiftPerformance).map(([shiftType, performance]) => (
                      <div key={shiftType} className="bg-white rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getShiftTypeColor(shiftType)}`}>
                            {shiftType}
                          </span>
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-secondary-500">Trips:</span>
                            <span className="text-secondary-900">
                              {performance.completedInShift}/{performance.tripsInShift}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-secondary-500">Completion:</span>
                            <span className="text-secondary-900">
                              {performance.completionRateInShift}%
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-secondary-500">Avg Duration:</span>
                            <span className="text-secondary-900">
                              {performance.avgDurationInShift}m
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-secondary-500">Exceptions:</span>
                            <span className="text-secondary-900">
                              {performance.exceptionRateInShift}%
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ShiftPerformanceAnalytics;
