const pool = require('../config/database');

// Available pages/routes that can be protected
const AVAILABLE_PAGES = {
  DASHBOARD: 'dashboard',
  USERS: 'users',
  TRIPS: 'trips',
  ASSIGNMENTS: 'assignments',
  SHIFTS: 'shifts',
  ANALYTICS: 'analytics',
  SETTINGS: 'settings'
};

/**
 * Get user permissions from database
 * @param {string} userRole - User's role
 * @returns {Object} - Object with page permissions
 */
const getUserPermissions = async (userRole) => {
  try {
    const query = `
      SELECT page_key, has_access 
      FROM role_permissions 
      WHERE role_name = $1
    `;
    
    const result = await pool.query(query, [userRole]);
    
    // Convert to object format
    const permissions = {};
    result.rows.forEach(row => {
      permissions[row.page_key] = row.has_access;
    });
    
    return permissions;
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    throw error;
  }
};

/**
 * Check if user has permission for a specific page
 * @param {string} userRole - User's role
 * @param {string} pageKey - Page key to check
 * @returns {boolean} - Whether user has permission
 */
const hasPermission = async (userRole, pageKey) => {
  try {
    // Admin always has all permissions
    if (userRole === 'admin') {
      return true;
    }

    const query = `
      SELECT has_access 
      FROM role_permissions 
      WHERE role_name = $1 AND page_key = $2
    `;
    
    const result = await pool.query(query, [userRole, pageKey]);
    
    if (result.rows.length === 0) {
      // If no specific permission found, default to false
      return false;
    }
    
    return result.rows[0].has_access;
  } catch (error) {
    console.error('Error checking permission:', error);
    return false; // Fail closed - deny access on error
  }
};

/**
 * Middleware to check if user has permission for a specific page
 * @param {string} requiredPermission - Required permission page key
 * @returns {Function} - Express middleware function
 */
const requirePermission = (requiredPermission) => {
  return async (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const userRole = req.user.role;
      
      // Check permission
      const hasAccess = await hasPermission(userRole, requiredPermission);
      
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: `Access denied. Required permission: ${requiredPermission}`,
          requiredPermission,
          userRole
        });
      }

      // User has permission, continue
      next();
    } catch (error) {
      console.error('Permission middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during permission check'
      });
    }
  };
};

/**
 * Middleware to check if user has any of the specified permissions
 * @param {string[]} requiredPermissions - Array of permission page keys
 * @returns {Function} - Express middleware function
 */
const requireAnyPermission = (requiredPermissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const userRole = req.user.role;
      
      // Check if user has any of the required permissions
      for (const permission of requiredPermissions) {
        const hasAccess = await hasPermission(userRole, permission);
        if (hasAccess) {
          return next(); // User has at least one required permission
        }
      }

      // User doesn't have any of the required permissions
      return res.status(403).json({
        success: false,
        message: `Access denied. Required permissions: ${requiredPermissions.join(' or ')}`,
        requiredPermissions,
        userRole
      });
    } catch (error) {
      console.error('Permission middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during permission check'
      });
    }
  };
};

/**
 * Middleware to check if user has all specified permissions
 * @param {string[]} requiredPermissions - Array of permission page keys
 * @returns {Function} - Express middleware function
 */
const requireAllPermissions = (requiredPermissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const userRole = req.user.role;
      
      // Check if user has all required permissions
      for (const permission of requiredPermissions) {
        const hasAccess = await hasPermission(userRole, permission);
        if (!hasAccess) {
          return res.status(403).json({
            success: false,
            message: `Access denied. Missing permission: ${permission}`,
            requiredPermissions,
            userRole
          });
        }
      }

      // User has all required permissions
      next();
    } catch (error) {
      console.error('Permission middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during permission check'
      });
    }
  };
};

/**
 * Middleware to add user permissions to request object
 * Useful for endpoints that need to check multiple permissions
 */
const addUserPermissions = async (req, res, next) => {
  try {
    if (!req.user) {
      return next(); // Skip if not authenticated
    }

    const userRole = req.user.role;
    const permissions = await getUserPermissions(userRole);
    
    // Add permissions to request object
    req.userPermissions = permissions;
    req.hasPermission = (pageKey) => {
      if (userRole === 'admin') return true;
      return permissions[pageKey] === true;
    };

    next();
  } catch (error) {
    console.error('Error adding user permissions:', error);
    // Don't fail the request, just continue without permissions
    next();
  }
};

/**
 * Admin-only middleware (shorthand for settings permission)
 */
const requireAdmin = requirePermission(AVAILABLE_PAGES.SETTINGS);

module.exports = {
  AVAILABLE_PAGES,
  getUserPermissions,
  hasPermission,
  requirePermission,
  requireAnyPermission,
  requireAllPermissions,
  addUserPermissions,
  requireAdmin
};