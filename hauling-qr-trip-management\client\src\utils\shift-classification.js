/**
 * Shift Classification Utilities
 * Purpose: Intelligent shift type classification based on time patterns
 * Issues Addressed: ENHANCEMENT - Custom Shift Time Classification
 * 
 * Features:
 * 1. Time-based pattern recognition for day/night/custom classification
 * 2. Standard shift pattern matching with tolerance
 * 3. Business rules integration for user overrides
 * 4. Frontend form integration for intelligent suggestions
 */

import { useState, useEffect } from 'react';

/**
 * Classify shift type based on start and end times
 * @param {string} startTime - Time in HH:MM format
 * @param {string} endTime - Time in HH:MM format
 * @returns {object} Classification result with type, confidence, and reason
 */
export function classifyShiftByTime(startTime, endTime) {
  // Parse time strings to minutes since midnight
  const parseTime = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + (minutes || 0);
  };

  const startMinutes = parseTime(startTime);
  const endMinutes = parseTime(endTime);

  // Define standard shift patterns (in minutes since midnight)
  const dayShiftPatterns = [
    { start: 6 * 60, end: 18 * 60, name: '6AM-6PM' },      // 360-1080
    { start: 7 * 60, end: 19 * 60, name: '7AM-7PM' },      // 420-1140
    { start: 8 * 60, end: 17 * 60, name: '8AM-5PM' },      // 480-1020
    { start: 6 * 60, end: 17 * 60, name: '6AM-5PM' },      // 360-1020
    { start: 7 * 60, end: 18 * 60, name: '7AM-6PM' },      // 420-1080
    { start: 8 * 60, end: 16 * 60, name: '8AM-4PM' },      // 480-960
    { start: 9 * 60, end: 17 * 60, name: '9AM-5PM' }       // 540-1020
  ];

  const nightShiftPatterns = [
    { start: 18 * 60, end: 6 * 60, name: '6PM-6AM' },      // 1080-360 (crosses midnight)
    { start: 19 * 60, end: 7 * 60, name: '7PM-7AM' },      // 1140-420 (crosses midnight)
    { start: 22 * 60, end: 6 * 60, name: '10PM-6AM' },     // 1320-360 (crosses midnight)
    { start: 23 * 60, end: 7 * 60, name: '11PM-7AM' },     // 1380-420 (crosses midnight)
    { start: 20 * 60, end: 8 * 60, name: '8PM-8AM' }       // 1200-480 (crosses midnight)
  ];

  // Tolerance for matching patterns (±30 minutes)
  const tolerance = 30;

  // Check day shift patterns
  for (const pattern of dayShiftPatterns) {
    if (Math.abs(startMinutes - pattern.start) <= tolerance &&
        Math.abs(endMinutes - pattern.end) <= tolerance) {
      return {
        classification: 'day',
        confidence: 'high',
        matchedPattern: pattern.name,
        reason: `Matches standard day shift pattern ${pattern.name}`,
        suggestion: 'This appears to be a day shift'
      };
    }
  }

  // Check night shift patterns (handle midnight crossing)
  for (const pattern of nightShiftPatterns) {
    const nightShiftMatch = (
      Math.abs(startMinutes - pattern.start) <= tolerance &&
      (
        // End time is next day (smaller number due to midnight crossing)
        (endMinutes < startMinutes && Math.abs(endMinutes - pattern.end) <= tolerance) ||
        // End time is same day but very late (close to midnight)
        (endMinutes > startMinutes && Math.abs(endMinutes - (pattern.end + 24 * 60)) <= tolerance)
      )
    );

    if (nightShiftMatch) {
      return {
        classification: 'night',
        confidence: 'high',
        matchedPattern: pattern.name,
        reason: `Matches standard night shift pattern ${pattern.name}`,
        suggestion: 'This appears to be a night shift'
      };
    }
  }

  // If no standard pattern matches, classify as custom
  return {
    classification: 'custom',
    confidence: 'definitive',
    matchedPattern: null,
    reason: 'Does not match any standard day or night shift patterns',
    suggestion: 'This is a custom shift with unique hours'
  };
}

/**
 * Enhanced classification with business rules
 * @param {string} startTime - Time in HH:MM format
 * @param {string} endTime - Time in HH:MM format
 * @param {string} userSelectedType - User's explicit choice ('day', 'night', 'custom')
 * @returns {object} Enhanced classification result
 */
export function classifyShiftWithBusinessRules(startTime, endTime, userSelectedType = 'custom') {
  const baseClassification = classifyShiftByTime(startTime, endTime);

  // If user explicitly selected day or night, respect that choice
  if (userSelectedType === 'day' || userSelectedType === 'night') {
    return {
      ...baseClassification,
      classification: userSelectedType,
      displayType: userSelectedType,
      userOverride: true,
      reason: `User explicitly selected ${userSelectedType} shift type`,
      suggestion: `Classified as ${userSelectedType} shift per user selection`
    };
  }

  // For custom shifts, use intelligent classification
  return {
    ...baseClassification,
    userOverride: false,
    displayType: baseClassification.classification,
    shiftType: 'custom', // Always store as custom in database
    suggestion: baseClassification.suggestion
  };
}

/**
 * Get shift type display configuration for UI
 * @param {string} shiftType - The shift type ('day', 'night', 'custom')
 * @param {string} displayType - The computed display type (may differ from shiftType)
 * @returns {object} Display configuration with icon, label, and color
 */
export function getShiftDisplayConfig(shiftType, displayType = null) {
  const typeToDisplay = displayType || shiftType;
  
  const displayConfigs = {
    'day': {
      icon: '☀️',
      label: 'Day Shift',
      shortLabel: 'Day',
      color: 'yellow',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-800',
      description: 'Standard daytime working hours'
    },
    'night': {
      icon: '🌙',
      label: 'Night Shift',
      shortLabel: 'Night',
      color: 'blue',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-800',
      description: 'Overnight working hours'
    },
    'custom': {
      icon: '🔧',
      label: 'Custom Shift',
      shortLabel: 'Custom',
      color: 'purple',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-800',
      description: 'Non-standard working hours'
    }
  };

  return displayConfigs[typeToDisplay] || {
    icon: '❓',
    label: 'Unknown Shift',
    shortLabel: 'Unknown',
    color: 'gray',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
    description: 'Unrecognized shift pattern'
  };
}

/**
 * Validate shift time inputs
 * @param {string} startTime - Start time in HH:MM format
 * @param {string} endTime - End time in HH:MM format
 * @returns {object} Validation result with isValid and errors
 */
export function validateShiftTimes(startTime, endTime) {
  const errors = [];

  // Basic format validation
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  
  if (!timeRegex.test(startTime)) {
    errors.push('Start time must be in HH:MM format');
  }
  
  if (!timeRegex.test(endTime)) {
    errors.push('End time must be in HH:MM format');
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  // Parse times for additional validation
  const parseTime = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const startMinutes = parseTime(startTime);
  const endMinutes = parseTime(endTime);

  // Check for minimum shift duration (at least 1 hour)
  let duration;
  if (endMinutes < startMinutes) {
    // Overnight shift
    duration = (24 * 60 - startMinutes) + endMinutes;
  } else {
    // Same day shift
    duration = endMinutes - startMinutes;
  }

  if (duration < 60) {
    errors.push('Shift must be at least 1 hour long');
  }

  if (duration > 16 * 60) {
    errors.push('Shift cannot be longer than 16 hours');
  }

  return {
    isValid: errors.length === 0,
    errors,
    duration: duration,
    isOvernight: endMinutes < startMinutes
  };
}

/**
 * Format time for display
 * @param {string} timeStr - Time in HH:MM format
 * @param {boolean} use12Hour - Whether to use 12-hour format
 * @returns {string} Formatted time string
 */
export function formatTimeDisplay(timeStr, use12Hour = true) {
  if (!timeStr) return '';

  const [hours, minutes] = timeStr.split(':').map(Number);
  
  if (use12Hour) {
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  } else {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }
}

/**
 * Get shift duration in human readable format
 * @param {string} startTime - Start time in HH:MM format
 * @param {string} endTime - End time in HH:MM format
 * @returns {string} Duration string (e.g., "8h 30m")
 */
export function getShiftDurationDisplay(startTime, endTime) {
  const validation = validateShiftTimes(startTime, endTime);
  
  if (!validation.isValid) {
    return 'Invalid times';
  }

  const totalMinutes = validation.duration;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  if (hours > 0 && minutes > 0) {
    return `${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h`;
  } else {
    return `${minutes}m`;
  }
}

/**
 * React hook for shift classification in forms
 * @param {string} startTime - Start time value
 * @param {string} endTime - End time value
 * @param {string} userSelectedType - User's selected shift type
 * @returns {object} Classification state and helpers
 */
export function useShiftClassification(startTime, endTime, userSelectedType = 'custom') {
  const [classification, setClassification] = useState(null);

  useEffect(() => {
    if (startTime && endTime) {
      const validation = validateShiftTimes(startTime, endTime);
      
      if (validation.isValid) {
        const result = classifyShiftWithBusinessRules(startTime, endTime, userSelectedType);
        setClassification({
          ...result,
          validation,
          displayConfig: getShiftDisplayConfig(result.shiftType, result.displayType),
          durationDisplay: getShiftDurationDisplay(startTime, endTime)
        });
      } else {
        setClassification({
          classification: 'invalid',
          validation,
          displayConfig: getShiftDisplayConfig('custom'),
          durationDisplay: 'Invalid'
        });
      }
    } else {
      setClassification(null);
    }
  }, [startTime, endTime, userSelectedType]);

  return classification;
}
