import React from 'react';
import toast from 'react-hot-toast';

/**
 * Error Boundary specifically for WebSocket-related errors
 */
class WebSocketErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
    this.maxRetries = props.maxRetries || 5;
    this.retryTimeout = null;
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('WebSocket Error Boundary caught an error:', error, errorInfo);

    this.setState({
      error: error.message || 'Unknown WebSocket error',
      errorInfo: errorInfo.componentStack || 'No component stack available',
      retryCount: this.state.retryCount + 1
    });

    // Show user-friendly error message
    toast.error(
      <div>
        <div className="font-medium">Connection Issue</div>
        <div className="text-sm mt-1">
          Real-time updates may be delayed. The page will continue to work normally.
        </div>
      </div>,
      { duration: 5000 }
    );

    // Log error for monitoring
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Auto-retry with exponential backoff for certain error types
    if (this.shouldAutoRetry(error) && this.state.retryCount < this.maxRetries) {
      const retryDelay = Math.min(1000 * Math.pow(2, this.state.retryCount), 30000); // Max 30s
      this.retryTimeout = setTimeout(() => {
        this.handleRetry();
      }, retryDelay);
    }
  }

  shouldAutoRetry = (error) => {
    // Auto-retry for network-related errors
    const autoRetryErrors = [
      'NetworkError',
      'WebSocket connection failed',
      'Connection lost',
      'ECONNREFUSED'
    ];
    
    return autoRetryErrors.some(errorType => 
      error.message?.includes(errorType) || error.name?.includes(errorType)
    );
  }

  handleRetry = () => {
    if (this.state.retryCount >= this.maxRetries) {
      toast.error('Maximum retry attempts reached. Please refresh the page.', { duration: 5000 });
      return;
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });

    // Trigger reconnection if callback provided
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  render() {
    if (this.state.hasError) {
      // Fallback UI with retry option
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-yellow-800">
                Real-time Connection Issue
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  Live updates are temporarily unavailable. You can still use all features normally,
                  but you may need to refresh manually to see the latest changes.
                </p>
              </div>
              <div className="mt-4 flex space-x-3">
                <button
                  onClick={this.handleRetry}
                  className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-md text-sm font-medium hover:bg-yellow-200 transition-colors"
                  disabled={this.state.retryCount >= this.maxRetries}
                >
                  Retry Connection ({this.state.retryCount}/{this.maxRetries})
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-white text-yellow-800 px-3 py-1 rounded-md text-sm font-medium border border-yellow-300 hover:bg-yellow-50 transition-colors"
                >
                  Refresh Page
                </button>
              </div>
              {this.state.retryCount > 2 && (
                <div className="mt-3 text-xs text-yellow-600">
                  Multiple connection attempts failed. Consider refreshing the page or checking your network connection.
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default WebSocketErrorBoundary;