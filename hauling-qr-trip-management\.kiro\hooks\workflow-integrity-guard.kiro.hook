{"enabled": true, "name": "4-Phase Workflow Integrity Guard", "description": "Monitors critical workflow files and validates that the 4-phase trip workflow (PENDING → IN_PROGRESS → COMPLETED → VERIFIED) remains intact and undisrupted", "version": "1", "when": {"type": "fileEdited", "patterns": ["server/routes/trip-routes.js", "server/services/TripService.js", "database/migrations/*.sql", "server/middleware/workflow-middleware.js", "client/src/components/TripDashboard.jsx", "client/src/pages/TripPage.jsx"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified files to ensure the 4-phase workflow integrity (PENDING → IN_PROGRESS → COMPLETED → VERIFIED) is maintained. Check for: 1) Proper state transition validation, 2) No bypassing of workflow phases, 3) Consistent state management across client and server, 4) Exception handling that preserves workflow integrity, 5) Database constraints that enforce proper state transitions. If any violations are detected, provide specific recommendations to restore workflow integrity. "}}