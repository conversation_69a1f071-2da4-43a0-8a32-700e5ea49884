// Offline Shift State Management Service
// Tracks active driver shifts in IndexedDB for intelligent offline check-in/check-out determination

import offlineDB from './offlineDB.js';

export class OfflineShiftStateService {
  constructor() {
    this.storeName = 'activeShifts';
  }

  /**
   * Store or update active shift information when driver checks in
   * @param {Object} shiftData - Shift information from successful check-in
   */
  async storeActiveShift(shiftData) {
    try {
      await offlineDB.initialize();

      const activeShift = {
        employeeId: shiftData.employeeId,
        driverName: shiftData.driverName,
        truckId: shiftData.truckId,
        truckNumber: shiftData.truckNumber,
        shiftId: shiftData.shiftId,
        checkInTime: shiftData.checkInTime,
        status: 'active',
        lastUpdated: new Date().toISOString(),
        source: shiftData.source || 'online', // 'online' or 'offline'
        syncStatus: shiftData.source === 'offline' ? 'pending' : 'synced'
      };

      // ENHANCED: Ensure transaction completion with explicit wait
      const result = await offlineDB.updateData(this.storeName, activeShift);

      // CRITICAL: Add small delay to ensure IndexedDB transaction is fully committed
      // This prevents race conditions in consecutive offline scans
      await new Promise(resolve => setTimeout(resolve, 50));

      console.log('[OfflineShiftState] Active shift stored:', {
        employeeId: activeShift.employeeId,
        truckId: activeShift.truckId,
        shiftId: activeShift.shiftId,
        source: activeShift.source,
        result: result
      });

      return activeShift;
    } catch (error) {
      console.error('[OfflineShiftState] Failed to store active shift:', error);
      throw error;
    }
  }

  /**
   * Remove active shift when driver checks out
   * @param {string} employeeId - Driver employee ID
   */
  async removeActiveShift(employeeId) {
    try {
      await offlineDB.initialize();

      const existingShift = await this.getActiveShift(employeeId);
      if (existingShift) {
        const result = await offlineDB.deleteData(this.storeName, employeeId);

        // CRITICAL: Add small delay to ensure IndexedDB transaction is fully committed
        // This prevents race conditions in consecutive offline scans
        await new Promise(resolve => setTimeout(resolve, 50));

        console.log('[OfflineShiftState] Active shift removed for:', employeeId, 'result:', result);
        return existingShift;
      }

      return null;
    } catch (error) {
      console.error('[OfflineShiftState] Failed to remove active shift:', error);
      throw error;
    }
  }

  /**
   * Get active shift for a driver
   * @param {string} employeeId - Driver employee ID
   * @returns {Object|null} Active shift data or null
   */
  async getActiveShift(employeeId) {
    try {
      await offlineDB.initialize();
      const shift = await offlineDB.getData(this.storeName, employeeId);

      // ENHANCED: Add detailed logging for debugging
      console.log(`[OfflineShiftState] getActiveShift(${employeeId}):`, {
        found: !!shift,
        status: shift?.status,
        truckId: shift?.truckId,
        shiftId: shift?.shiftId,
        lastUpdated: shift?.lastUpdated
      });

      if (shift && shift.status === 'active') {
        return shift;
      }

      return null;
    } catch (error) {
      console.error('[OfflineShiftState] Failed to get active shift:', error);
      return null;
    }
  }

  /**
   * Determine if driver should check in or check out based on offline state
   * @param {string} employeeId - Driver employee ID
   * @param {string} truckId - Truck ID from QR scan
   * @returns {Object} Action determination result
   */
  async determineOfflineAction(employeeId, truckId) {
    try {
      console.log(`[OfflineShiftState] determineOfflineAction(${employeeId}, ${truckId}) - Starting...`);

      const activeShift = await this.getActiveShift(employeeId);

      console.log(`[OfflineShiftState] Active shift query result:`, {
        hasActiveShift: !!activeShift,
        activeShiftTruckId: activeShift?.truckId,
        requestedTruckId: truckId,
        trucksMatch: activeShift?.truckId === truckId
      });

      if (!activeShift) {
        // No active shift found - should check in
        const result = {
          action: 'check_in',
          reason: 'no_active_shift',
          message: 'No active shift found - checking in',
          currentShift: null
        };
        console.log(`[OfflineShiftState] Action determined:`, result);
        return result;
      }

      if (activeShift.truckId === truckId) {
        // Same truck - should check out
        const result = {
          action: 'check_out',
          reason: 'same_truck_checkout',
          message: `Checking out from ${activeShift.truckNumber}`,
          currentShift: activeShift
        };
        console.log(`[OfflineShiftState] Action determined:`, result);
        return result;
      } else {
        // Different truck - handover scenario (check in to new truck)
        const result = {
          action: 'check_in',
          reason: 'truck_handover',
          message: `Handover from ${activeShift.truckNumber} to new truck`,
          currentShift: activeShift
        };
        console.log(`[OfflineShiftState] Action determined:`, result);
        return result;
      }
    } catch (error) {
      console.error('[OfflineShiftState] Failed to determine offline action:', error);
      // Default to check_in on error
      const result = {
        action: 'check_in',
        reason: 'error_fallback',
        message: 'Error determining action - defaulting to check in',
        currentShift: null
      };
      console.log(`[OfflineShiftState] Error fallback action:`, result);
      return result;
    }
  }

  /**
   * Sync offline shift states with server data
   * Called when coming back online to reconcile shift states
   * @param {Array} serverShifts - Active shifts from server
   */
  async syncWithServerShifts(serverShifts) {
    try {
      await offlineDB.initialize();
      
      // Get all offline active shifts
      const offlineShifts = await offlineDB.getAllData(this.storeName);
      
      const syncResults = {
        updated: 0,
        removed: 0,
        conflicts: 0
      };

      // Update offline shifts with server data
      for (const serverShift of serverShifts) {
        await this.storeActiveShift({
          employeeId: serverShift.employee_id,
          driverName: serverShift.full_name,
          truckId: serverShift.truck_id.toString(),
          truckNumber: serverShift.truck_number,
          shiftId: serverShift.id,
          checkInTime: serverShift.created_at,
          source: 'online'
        });
        syncResults.updated++;
      }

      // Remove offline shifts that don't exist on server
      const serverEmployeeIds = new Set(serverShifts.map(s => s.employee_id));
      
      for (const offlineShift of offlineShifts) {
        if (!serverEmployeeIds.has(offlineShift.employeeId)) {
          await this.removeActiveShift(offlineShift.employeeId);
          syncResults.removed++;
        }
      }

      console.log('[OfflineShiftState] Sync with server completed:', syncResults);
      return syncResults;
      
    } catch (error) {
      console.error('[OfflineShiftState] Failed to sync with server shifts:', error);
      throw error;
    }
  }

  /**
   * Get all active shifts (for debugging)
   * @returns {Array} All active shifts
   */
  async getAllActiveShifts() {
    try {
      await offlineDB.initialize();
      return await offlineDB.getAllData(this.storeName);
    } catch (error) {
      console.error('[OfflineShiftState] Failed to get all active shifts:', error);
      return [];
    }
  }

  /**
   * Clear all offline shift data (for debugging/reset)
   */
  async clearAllShifts() {
    try {
      await offlineDB.initialize();
      const allShifts = await this.getAllActiveShifts();
      
      for (const shift of allShifts) {
        await offlineDB.deleteData(this.storeName, shift.employeeId);
      }
      
      console.log('[OfflineShiftState] All shifts cleared');
      return allShifts.length;
    } catch (error) {
      console.error('[OfflineShiftState] Failed to clear all shifts:', error);
      throw error;
    }
  }

  /**
   * Handle successful connection sync - update shift state accordingly
   * @param {Object} connectionData - Synced connection data
   * @param {Object} serverResponse - Server response from sync
   */
  async handleSyncedConnection(connectionData, serverResponse) {
    try {
      const { action, driver_qr_data } = connectionData.apiPayload;
      const employeeId = driver_qr_data.employee_id;

      if (action === 'check_in' && serverResponse.success) {
        // Store new active shift from successful check-in
        await this.storeActiveShift({
          employeeId: employeeId,
          driverName: serverResponse.driver?.full_name || driver_qr_data.full_name,
          truckId: connectionData.truckId,
          truckNumber: serverResponse.truck,
          shiftId: serverResponse.shift_id,
          checkInTime: serverResponse.check_in_time,
          source: 'offline'
        });
      } else if (action === 'check_out' && serverResponse.success) {
        // Remove active shift from successful check-out
        await this.removeActiveShift(employeeId);
      }

      console.log('[OfflineShiftState] Handled synced connection:', {
        action,
        employeeId,
        success: serverResponse.success
      });

    } catch (error) {
      console.error('[OfflineShiftState] Failed to handle synced connection:', error);
    }
  }
}

// Create singleton instance
export const offlineShiftState = new OfflineShiftStateService();

// Export service
export default offlineShiftState;
