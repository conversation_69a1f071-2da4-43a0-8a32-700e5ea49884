/**
 * Network Utilities for Dynamic IP Detection
 * Automatically detects local network IP addresses for mobile device access
 */

const os = require('os');

/**
 * Get all network interfaces
 */
function getNetworkInterfaces() {
  return os.networkInterfaces();
}

/**
 * Get the primary local network IP address
 * Prioritizes WiFi/Ethernet interfaces over virtual ones
 */
function getLocalNetworkIP() {
  const interfaces = getNetworkInterfaces();
  const candidates = [];
  
  // Priority order for interface types
  const interfacePriority = {
    'Wi-Fi': 1,
    'WiFi': 1,
    'Wireless': 1,
    'Ethernet': 2,
    'Local Area Connection': 3,
    'en0': 4, // macOS WiFi
    'en1': 5, // macOS Ethernet
    'eth0': 6, // Linux Ethernet
    'wlan0': 7, // Linux WiFi
  };
  
  for (const [interfaceName, addresses] of Object.entries(interfaces)) {
    for (const addr of addresses) {
      // Skip internal/loopback addresses
      if (addr.internal || addr.family !== 'IPv4') {
        continue;
      }
      
      // Skip virtual machine interfaces
      if (interfaceName.toLowerCase().includes('vmware') ||
          interfaceName.toLowerCase().includes('virtualbox') ||
          interfaceName.toLowerCase().includes('hyper-v') ||
          interfaceName.toLowerCase().includes('docker') ||
          interfaceName.toLowerCase().includes('vethernet')) {
        continue;
      }
      
      // Determine priority
      let priority = 999; // Default low priority
      for (const [pattern, p] of Object.entries(interfacePriority)) {
        if (interfaceName.toLowerCase().includes(pattern.toLowerCase())) {
          priority = p;
          break;
        }
      }
      
      candidates.push({
        ip: addr.address,
        interface: interfaceName,
        priority: priority,
        cidr: addr.cidr
      });
    }
  }
  
  // Sort by priority (lower number = higher priority)
  candidates.sort((a, b) => a.priority - b.priority);
  
  // Return the best candidate or fallback to localhost
  return candidates.length > 0 ? candidates[0].ip : '127.0.0.1';
}

/**
 * Get all available local network IPs
 */
function getAllLocalNetworkIPs() {
  const interfaces = getNetworkInterfaces();
  const ips = [];
  
  for (const addresses of Object.values(interfaces)) {
    for (const addr of addresses) {
      if (!addr.internal && addr.family === 'IPv4') {
        ips.push(addr.address);
      }
    }
  }
  
  return ips;
}

/**
 * Check if an IP address is in a private network range
 */
function isPrivateIP(ip) {
  const parts = ip.split('.').map(Number);
  
  // 10.0.0.0/8
  if (parts[0] === 10) return true;
  
  // **********/12
  if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return true;
  
  // ***********/16
  if (parts[0] === 192 && parts[1] === 168) return true;
  
  return false;
}

/**
 * Get the best IP for mobile device access
 * Prefers private network IPs that mobile devices can reach
 */
function getMobileAccessIP() {
  const allIPs = getAllLocalNetworkIPs();
  const privateIPs = allIPs.filter(isPrivateIP);
  
  if (privateIPs.length > 0) {
    // Prefer 192.168.x.x networks (most common for home/office)
    const homeNetworkIPs = privateIPs.filter(ip => ip.startsWith('192.168.'));
    if (homeNetworkIPs.length > 0) {
      return homeNetworkIPs[0];
    }
    
    // Fallback to any private IP
    return privateIPs[0];
  }
  
  // Fallback to primary local IP
  return getLocalNetworkIP();
}

/**
 * Generate URLs for different protocols and ports
 */
function generateServerURLs(ip = null) {
  const targetIP = ip || getMobileAccessIP();
  const httpPort = process.env.PORT || 5000;
  const httpsPort = process.env.HTTPS_PORT || 5443;
  
  return {
    ip: targetIP,
    http: {
      localhost: `http://localhost:${httpPort}`,
      network: `http://${targetIP}:${httpPort}`,
      health: `http://${targetIP}:${httpPort}/health`,
      api: `http://${targetIP}:${httpPort}/api`
    },
    https: {
      localhost: `https://localhost:${httpsPort}`,
      network: `https://${targetIP}:${httpsPort}`,
      health: `https://${targetIP}:${httpsPort}/health`,
      api: `https://${targetIP}:${httpsPort}/api`
    },
    websocket: {
      ws: `ws://${targetIP}:${httpPort}`,
      wss: `wss://${targetIP}:${httpsPort}`
    }
  };
}

/**
 * Generate frontend URLs
 */
function generateFrontendURLs(ip = null) {
  const targetIP = ip || getMobileAccessIP();
  const frontendPort = 3000;
  
  return {
    ip: targetIP,
    http: `http://${targetIP}:${frontendPort}`,
    https: `https://${targetIP}:${frontendPort}`,
    localhost: `http://localhost:${frontendPort}`
  };
}

/**
 * Display network information for debugging
 */
function displayNetworkInfo() {
  console.log('\n🌐 Network Configuration:');
  console.log('=' .repeat(50));
  
  const primaryIP = getLocalNetworkIP();
  const mobileIP = getMobileAccessIP();
  const allIPs = getAllLocalNetworkIPs();
  
  console.log(`Primary IP: ${primaryIP}`);
  console.log(`Mobile Access IP: ${mobileIP}`);
  console.log(`All Network IPs: ${allIPs.join(', ')}`);
  
  const serverURLs = generateServerURLs();
  const frontendURLs = generateFrontendURLs();
  
  console.log('\n📱 Mobile Device Access URLs:');
  console.log(`Frontend: ${frontendURLs.https}`);
  console.log(`Backend API: ${serverURLs.https.api}`);
  console.log(`WebSocket: ${serverURLs.websocket.wss}`);
  
  console.log('\n🔗 All Available URLs:');
  console.log('Frontend:');
  console.log(`  HTTPS: ${frontendURLs.https}`);
  console.log(`  HTTP:  ${frontendURLs.http}`);
  console.log('Backend:');
  console.log(`  HTTPS: ${serverURLs.https.network}`);
  console.log(`  HTTP:  ${serverURLs.http.network}`);
  console.log('WebSocket:');
  console.log(`  WSS: ${serverURLs.websocket.wss}`);
  console.log(`  WS:  ${serverURLs.websocket.ws}`);
  
  return {
    primaryIP,
    mobileIP,
    allIPs,
    serverURLs,
    frontendURLs
  };
}

/**
 * Update environment variables with detected IP
 */
function updateEnvironmentWithIP(ip = null) {
  const targetIP = ip || getMobileAccessIP();
  
  // Update process.env for current session
  process.env.LOCAL_NETWORK_IP = targetIP;
  process.env.DETECTED_IP = targetIP;
  
  const serverURLs = generateServerURLs(targetIP);
  const frontendURLs = generateFrontendURLs(targetIP);
  
  // Update CORS URLs
  if (!process.env.FRONTEND_URL || process.env.FRONTEND_URL.includes('**************')) {
    process.env.FRONTEND_URL = frontendURLs.https;
    process.env.FRONTEND_URL_HTTP = frontendURLs.http;
  }
  
  return {
    ip: targetIP,
    serverURLs,
    frontendURLs
  };
}

module.exports = {
  getNetworkInterfaces,
  getLocalNetworkIP,
  getAllLocalNetworkIPs,
  getMobileAccessIP,
  isPrivateIP,
  generateServerURLs,
  generateFrontendURLs,
  displayNetworkInfo,
  updateEnvironmentWithIP
};
