import React from 'react';

const MetricCard = ({ title, value, subtitle, icon, color = 'blue', trend = null }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600 border-blue-200',
    green: 'bg-green-50 text-green-600 border-green-200',
    yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
    red: 'bg-red-50 text-red-600 border-red-200',
    purple: 'bg-purple-50 text-purple-600 border-purple-200',
    gray: 'bg-gray-50 text-gray-600 border-gray-200'
  };

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg ${colorClasses[color]}`}>
          <span className="text-2xl">{icon}</span>
        </div>
        <div className="ml-4 flex-1">
          <div className="text-sm font-medium text-secondary-500 uppercase tracking-wide">
            {title}
          </div>
          <div className="text-2xl font-bold text-secondary-900">
            {value}
          </div>
          {subtitle && (
            <div className="text-sm text-secondary-500">
              {subtitle}
            </div>
          )}
          {trend && (
            <div className={`text-xs mt-1 ${trend.positive ? 'text-green-600' : 'text-red-600'}`}>
              {trend.positive ? '↗️' : '↘️'} {trend.value}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const FleetMetrics = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="animate-pulse bg-secondary-200 h-24 rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8 text-secondary-500">
        <span className="text-4xl block mb-2">📊</span>
        No fleet data available
      </div>
    );
  }

  const metrics = data.metrics || {};
  const phaseDistribution = data.phaseDistribution || {};
  const alerts = data.alerts || {};

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Fleet Status Metrics */}
      <MetricCard
        title="Active Trucks"
        value={metrics.activeTrucks || 0}
        subtitle={`${metrics.totalTrucks || 0} total trucks`}
        icon="🚛"
        color="blue"
      />
      
      <MetricCard
        title="In Operation"
        value={metrics.trucksInOperation || 0}
        subtitle={`${metrics.fleetUtilization || 0}% utilization`}
        icon="⚡"
        color="green"
      />
      
      <MetricCard
        title="Stopped"
        value={metrics.trucksInStopped || 0}
        subtitle={`${metrics.todayStopped || 0} today`}
        icon="⏹️"
        color={metrics.trucksInStopped > 0 ? 'red' : 'gray'}
      />
      
      <MetricCard
        title="Active Drivers"
        value={metrics.activeDrivers || 0}
        subtitle="Available for assignments"
        icon="👨‍💼"
        color="purple"
      />

      {/* Today's Performance */}
      <MetricCard
        title="Today's Trips"
        value={metrics.todayTrips || 0}
        subtitle={`${metrics.todayCompleted || 0} completed`}
        icon="📋"
        color="blue"
      />
      
      <MetricCard
        title="Completion Rate"
        value={`${metrics.todayCompletionRate || '0.0'}%`}
        subtitle="Today's performance"
        icon="✅"
        color={parseFloat(metrics.todayCompletionRate || 0) >= 90 ? 'green' : 'yellow'}
      />
      
      <MetricCard
        title="Avg Trip Time"
        value={`${metrics.todayAvgTripTime || 0}m`}
        subtitle="Today's average"
        icon="⏱️"
        color="blue"
      />

      {/* Alerts Summary */}
      <MetricCard
        title="Active Alerts"
        value={alerts.overdueTrips + alerts.activeStopped + alerts.todayExceptions || 0}
        subtitle={`${alerts.overdueTrips || 0} overdue, ${alerts.todayExceptions || 0} exceptions`}
        icon="⚠️"
        color={(alerts.overdueTrips + alerts.activeStopped + alerts.todayExceptions) > 0 ? 'red' : 'gray'}
      />

      {/* Phase Distribution */}
      <div className="md:col-span-2 lg:col-span-4">
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
          <h3 className="text-lg font-medium text-secondary-900 mb-4">
            Current Operations by Phase
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {phaseDistribution.loading || 0}
              </div>
              <div className="text-sm text-secondary-500">⬆️ Loading</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {phaseDistribution.travelingToUnload || 0}
              </div>
              <div className="text-sm text-secondary-500">🚛 To Unload</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {phaseDistribution.unloading || 0}
              </div>
              <div className="text-sm text-secondary-500">⬇️ Unloading</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {phaseDistribution.travelingToLoad || 0}
              </div>
              <div className="text-sm text-secondary-500">🔄 To Load</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FleetMetrics;
