#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - REPOSITORY AND ENVIRONMENT SETUP MODULE
# =============================================================================
# Version: 1.0.0 - Complete repository fetching and environment configuration
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: GitHub repository cloning, environment file setup, and configuration
# =============================================================================

# Source shared configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/shared-config.sh"

# Module-specific configuration
readonly MODULE_NAME="setup-repository-environment"
readonly LOG_FILE="${LOG_DIR}/repository-environment-$(date +%Y%m%d-%H%M%S).log"

# Setup error handling
setup_error_handling "$MODULE_NAME"

# =============================================================================
# REPOSITORY FETCHING
# =============================================================================
fetch_repository() {
  log_info "📁 Fetching repository with authentication..."

  # Debug: Check GITHUB_PAT availability (allow bypass for testing)
  if [[ -n "$GITHUB_PAT" ]]; then
    log_info "✅ GITHUB_PAT is available (length: ${#GITHUB_PAT})"
  else
    log_warning "⚠️ GITHUB_PAT is not set - checking for existing repository"
    if [[ -d "/home/<USER>/hauling-qr-trip-management" ]]; then
      log_info "✅ Found existing repository - using local copy for testing"
      GITHUB_PAT="testing-bypass"
    else
      log_error "❌ GITHUB_PAT is not set or empty"
      log_error "Please set GITHUB_PAT environment variable:"
      log_error "export GITHUB_PAT=your_personal_access_token"
      log_error "sudo -E ./auto-deploy.sh"
      return 1
    fi
  fi

  # Mask credentials in logs for security
  local repo_url="https://github.com/${GITHUB_REPO}.git"
  local safe_url="$repo_url"
  if [[ -n "$GITHUB_PAT" ]]; then
    safe_url=${safe_url//${GITHUB_PAT}/***}
  fi
  safe_url=${safe_url//https:\/\/[^@]*@/https:\/\/***@}

  log_info "Performing fresh repository clone: $safe_url (branch: main)"

  local auth_url="$repo_url"
  if [[ -n "$GITHUB_PAT" ]]; then
    auth_url=$(compose_auth_repo_url "$repo_url" "$GITHUB_PAT")
  fi

  # Force fresh clone - remove existing directory if it exists
  if [[ -d "$APP_DIR" ]]; then
    log_info "Removing existing directory: $APP_DIR"
    rm -rf "$APP_DIR"
  fi

  # Create parent directory
  mkdir -p "$(dirname "$APP_DIR")"

  # Handle testing bypass mode
  if [[ "$GITHUB_PAT" == "testing-bypass" ]]; then
    log_info "🧪 Testing mode: Copying existing repository to $APP_DIR"
    if [[ -d "/home/<USER>/hauling-qr-trip-management" ]]; then
      cp -r "/home/<USER>/hauling-qr-trip-management" "$APP_DIR"
      chown -R "$UBUNTU_USER:$UBUNTU_USER" "$APP_DIR"
      log_success "✅ Repository copied successfully for testing"
      return 0
    else
      log_error "❌ Testing mode failed: No existing repository found"
      return 1
    fi
  fi

  log_info "Cloning fresh repository to $APP_DIR"
  log_info "Using authentication URL format: https://x-access-token:***@github.com/..."

  # Try git clone with detailed error output
  if git clone --branch main --depth 1 "$auth_url" "$APP_DIR" 2>&1 | tee -a "$LOG_FILE"; then
    log_success "✅ Repository cloned successfully"
    log_info "Repository contents:"
    ls -la "$APP_DIR" | head -10 >>"$LOG_FILE" 2>&1

    # Set proper ownership
    chown -R $UBUNTU_USER:$UBUNTU_USER "$APP_DIR"
    log_success "✅ Repository ownership set to $UBUNTU_USER"

    return 0
  else
    local git_exit_code=$?
    log_error "❌ Git clone failed with exit code: $git_exit_code"
    log_error ""
    log_error "Debugging information:"
    log_error "- GITHUB_PAT length: ${#GITHUB_PAT}"
    log_error "- Repository: ${GITHUB_REPO}"
    log_error "- Target directory: $APP_DIR"
    log_error ""
    log_error "Common solutions:"
    log_error "1. Verify GITHUB_PAT is valid and has repo access"
    log_error "2. Check if repository name is correct: ${GITHUB_REPO}"
    log_error "3. Ensure you're using sudo -E to preserve environment variables"
    log_error ""
    log_error "To test your PAT manually:"
    log_error "curl -H \"Authorization: token \$GITHUB_PAT\" https://api.github.com/repos/${GITHUB_REPO}"
    return 1
  fi
}

# =============================================================================
# ENVIRONMENT FILE MANAGEMENT
# =============================================================================
ensure_env_file() {
  log_info "📝 Setting up environment configuration..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  # Check for dual environment files
  if [[ -f ".env.dev" && -f ".env.prod" ]]; then
    log_info "✅ Found dual environment files (.env.dev and .env.prod)"

    # Select appropriate environment file
    if [[ "${DEPLOYMENT_ENV}" == "production" ]]; then
      log_info "📁 Using production environment: .env.prod"
      cp -f ".env.prod" ".env"
    else
      log_info "📁 Using development environment: .env.dev"
      cp -f ".env.dev" ".env"
    fi

  elif [[ -f ".env" ]]; then
    log_info "📁 Found single .env file - updating for deployment"
    update_env_for_deployment

  elif [[ -f ".env.example" ]]; then
    log_info "📁 Creating .env from .env.example template"
    cp -f ".env.example" ".env"
    update_env_for_deployment

  else
    log_warning "⚠️ No environment files found - creating from template"
    create_env_from_template
  fi

  # Set proper permissions
  chmod 600 "$APP_DIR/.env"
  chown $UBUNTU_USER:$UBUNTU_USER "$APP_DIR/.env"

  # CRITICAL FIX: Ensure .env.dev exists for WSL environment detection
  if [[ ! -f "$APP_DIR/.env.dev" ]]; then
    log_info "📝 Creating missing .env.dev file for WSL compatibility..."
    create_basic_development_env_file
  fi

  # Validate environment configuration
  validate_env_configuration

  log_success "✅ Environment configuration completed for: ${DEPLOYMENT_ENV}"
}

# =============================================================================
# ENVIRONMENT FILE CREATION AND UPDATES
# =============================================================================
create_env_from_template() {
  log_info "📝 Creating environment file from template..."

  local env_file="$APP_DIR/.env"

  # Create basic environment file based on deployment environment
  if [[ "${DEPLOYMENT_ENV}" == "production" ]]; then
    # Copy production template if it exists, otherwise create basic production config
    if [[ -f "$APP_DIR/.env.prod" ]]; then
      cp "$APP_DIR/.env.prod" "$env_file"
      log_info "✅ Created .env from .env.prod template"
    else
      create_basic_production_env
    fi
  else
    # Copy development template if it exists, otherwise create basic development config
    if [[ -f "$APP_DIR/.env.dev" ]]; then
      cp "$APP_DIR/.env.dev" "$env_file"
      log_info "✅ Created .env from .env.dev template"
    else
      create_basic_development_env
    fi
  fi
}

create_basic_production_env() {
  log_info "📝 Creating basic production environment file..."
  
  cat > "$APP_DIR/.env" << EOF
# Hauling QR Trip System - Production Environment
NODE_ENV=production
PORT=${SERVER_HTTP_PORT}

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD="${DB_PASSWORD}"

# JWT Configuration
JWT_SECRET=${JWT_SECRET}

# Server Configuration
SERVER_PORT=${SERVER_HTTP_PORT}
CLIENT_URL=https://${PRODUCTION_DOMAIN}
API_URL=https://${PRODUCTION_DOMAIN}

# React App Configuration - FIXED for Cloudflare API subdomain
REACT_APP_API_URL=https://api.${PRODUCTION_DOMAIN}/api
REACT_APP_WS_URL=wss://api.${PRODUCTION_DOMAIN}/ws
REACT_APP_USE_HTTPS=false

# Security - FIXED CORS for main domain
CORS_ORIGIN=https://${PRODUCTION_DOMAIN},http://${PRODUCTION_DOMAIN},https://api.${PRODUCTION_DOMAIN},http://api.${PRODUCTION_DOMAIN}
DEV_ENABLE_CORS_ALL=true
SECURE_COOKIES=true

# CORS Configuration - NGINX Proxy Mode (prevents duplicate headers)
NGINX_PROXY_MODE=true
EXPRESS_CORS_DISABLED=true
CORS_HANDLED_BY_NGINX=true

# Logging
LOG_LEVEL=info
EOF
  
  log_success "✅ Basic production environment file created"
}

create_basic_development_env_file() {
  log_info "📝 Creating .env.dev file for WSL environment detection..."

  cat > "$APP_DIR/.env.dev" << EOF
# Hauling QR Trip System - Development Environment
NODE_ENV=development
PORT=${SERVER_HTTP_PORT}

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD="${DB_PASSWORD}"

# JWT Configuration
JWT_SECRET=dev-jwt-secret-key-change-in-production

# Server Configuration
SERVER_PORT=${SERVER_HTTP_PORT}
CLIENT_PORT=3000

# API URLs for development
REACT_APP_API_URL=http://localhost:${SERVER_HTTP_PORT}/api
REACT_APP_WS_URL=ws://localhost:${SERVER_HTTP_PORT}

# Development flags
DEBUG=true
ENABLE_CORS=true
EOF

  log_success "✅ .env.dev file created"
}

create_basic_development_env() {
  log_info "📝 Creating basic development environment file..."

  cat > "$APP_DIR/.env" << EOF
# Hauling QR Trip System - Development Environment
NODE_ENV=development
PORT=${SERVER_HTTP_PORT}

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD="${DB_PASSWORD}"

# JWT Configuration
JWT_SECRET=${JWT_SECRET}

# Server Configuration
SERVER_PORT=${SERVER_HTTP_PORT}
CLIENT_URL=http://localhost:${CLIENT_PORT}
API_URL=http://localhost:${SERVER_HTTP_PORT}

# Security
CORS_ORIGIN=http://localhost:${CLIENT_PORT}
SECURE_COOKIES=false

# CORS Configuration - NGINX Proxy Mode (prevents duplicate headers)
NGINX_PROXY_MODE=true
EXPRESS_CORS_DISABLED=true
CORS_HANDLED_BY_NGINX=true

# Logging
LOG_LEVEL=debug
EOF
  
  log_success "✅ Basic development environment file created"
}

update_env_for_deployment() {
  log_info "🔧 Updating environment file for deployment..."
  
  local env_file="$APP_DIR/.env"
  
  # Detect VPS IP if not already detected
  if [[ -z "$DETECTED_VPS_IP" ]]; then
    detect_vps_ip || log_warning "⚠️ Could not detect VPS IP"
  fi
  
  # Update database configuration
  sed -i '/^DB_HOST=/d' "$env_file" 2>/dev/null || true
  sed -i '/^DB_PORT=/d' "$env_file" 2>/dev/null || true
  sed -i '/^DB_NAME=/d' "$env_file" 2>/dev/null || true
  sed -i '/^DB_USER=/d' "$env_file" 2>/dev/null || true
  sed -i '/^DB_PASSWORD=/d' "$env_file" 2>/dev/null || true
  
  # Add database configuration
  echo "" >> "$env_file"
  echo "# Database Configuration - Updated by deployment" >> "$env_file"
  echo "DB_HOST=localhost" >> "$env_file"
  echo "DB_PORT=5432" >> "$env_file"
  echo "DB_NAME=${DB_NAME}" >> "$env_file"
  echo "DB_USER=${DB_USER}" >> "$env_file"
  echo "DB_PASSWORD=\"${DB_PASSWORD}\"" >> "$env_file"
  
  # Update server configuration
  sed -i '/^SERVER_PORT=/d' "$env_file" 2>/dev/null || true
  sed -i '/^PORT=/d' "$env_file" 2>/dev/null || true
  echo "SERVER_PORT=${SERVER_HTTP_PORT}" >> "$env_file"
  echo "PORT=${SERVER_HTTP_PORT}" >> "$env_file"
  
  # Update JWT secret
  sed -i '/^JWT_SECRET=/d' "$env_file" 2>/dev/null || true
  echo "JWT_SECRET=${JWT_SECRET}" >> "$env_file"

  # CRITICAL: Add NGINX proxy mode configuration to prevent CORS header duplication
  local cors_disable_marker="# HAULING-QR-DEPLOYMENT-CORS-DISABLE-$(date +%Y%m%d)"

  if ! grep -q "NGINX_PROXY_MODE=true" "$env_file" 2>/dev/null; then
    echo "" >> "$env_file"
    echo "$cors_disable_marker" >> "$env_file"
    echo "# CORS Configuration - NGINX Proxy Mode (prevents duplicate headers)" >> "$env_file"
    echo "# CRITICAL: These variables disable Express.js CORS when behind NGINX proxy" >> "$env_file"
    echo "# This prevents duplicate Access-Control-Allow-Origin headers that cause browser rejection" >> "$env_file"
    echo "NGINX_PROXY_MODE=true" >> "$env_file"
    echo "EXPRESS_CORS_DISABLED=true" >> "$env_file"
    echo "CORS_HANDLED_BY_NGINX=true" >> "$env_file"
    log_info "✅ APPLIED: Express.js CORS disabled for NGINX proxy mode"
  else
    log_info "✅ SKIPPED: NGINX proxy mode already configured"
  fi

  # CRITICAL: Ensure environment variables are also set in system-wide environment
  # This provides additional persistence across different startup methods
  local system_env_file="/etc/environment"

  log_info "🔧 Adding CORS configuration to system-wide environment for persistence..."

  # Add NGINX_PROXY_MODE to system environment if not present
  if ! sudo grep -q "NGINX_PROXY_MODE=true" "$system_env_file" 2>/dev/null; then
    echo "NGINX_PROXY_MODE=true" | sudo tee -a "$system_env_file" >/dev/null
    echo "EXPRESS_CORS_DISABLED=true" | sudo tee -a "$system_env_file" >/dev/null
    echo "CORS_HANDLED_BY_NGINX=true" | sudo tee -a "$system_env_file" >/dev/null
    log_info "✅ CORS configuration added to system-wide environment"
  else
    log_info "✅ CORS configuration already present in system-wide environment"
  fi

  # CRITICAL: Create a dedicated CORS configuration file for PM2
  local pm2_cors_env_file="$APP_DIR/.env.cors"
  log_info "📝 Creating dedicated CORS environment file for PM2..."

  cat > "$pm2_cors_env_file" << EOF
# CORS Configuration for PM2 - CRITICAL FOR RESTART PERSISTENCE
# These variables disable Express.js CORS when behind NGINX proxy
# This prevents duplicate Access-Control-Allow-Origin headers that cause browser rejection
NGINX_PROXY_MODE=true
EXPRESS_CORS_DISABLED=true
CORS_HANDLED_BY_NGINX=true
EOF

  # Set proper permissions
  sudo chown ubuntu:ubuntu "$pm2_cors_env_file"
  sudo chmod 644 "$pm2_cors_env_file"

  log_success "✅ Dedicated CORS environment file created: $pm2_cors_env_file"
  log_success "✅ Environment file updated for deployment with enhanced CORS configuration persistence"
}

validate_env_configuration() {
  log_info "🔍 Validating environment configuration..."

  local env_file="$APP_DIR/.env"
  if [[ ! -f "$env_file" ]]; then
    log_error "❌ Environment file not found: $env_file"
    return 1
  fi

  # Check required variables
  local required_vars=("DB_NAME" "DB_USER" "DB_PASSWORD" "JWT_SECRET" "SERVER_PORT")
  local missing_vars=0

  for var in "${required_vars[@]}"; do
    if ! grep -q "^${var}=" "$env_file"; then
      log_error "❌ Missing required variable: $var"
      missing_vars=$((missing_vars + 1))
    else
      log_info "✅ Found required variable: $var"
    fi
  done

  if [[ $missing_vars -eq 0 ]]; then
    log_success "✅ Environment configuration validation passed"
    return 0
  else
    log_error "❌ Environment configuration validation failed with $missing_vars missing variables"
    return 1
  fi
}

# =============================================================================
# MAIN SETUP FUNCTION
# =============================================================================
main() {
  log_info "🚀 Starting Repository and Environment Setup"
  log_info "📅 Started at: $(date)"
  log_info "📝 Log file: $LOG_FILE"
  
  # Check root privileges
  check_root
  
  # Validate required variables
  if [[ -z "$GITHUB_REPO" ]]; then
    log_error "❌ GITHUB_REPO not set"
    exit 1
  fi
  
  # Step 1: Fetch repository
  if ! fetch_repository; then
    log_error "❌ Repository fetching failed"
    exit 1
  fi
  
  # Step 2: Setup environment configuration
  if ! ensure_env_file; then
    log_error "❌ Environment setup failed"
    exit 1
  fi
  
  # Final validation
  log_info "🔍 Validating setup..."
  local validation_errors=0
  
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    validation_errors=$((validation_errors + 1))
  fi
  
  if [[ ! -f "$APP_DIR/.env" ]]; then
    log_error "❌ Environment file not found: $APP_DIR/.env"
    validation_errors=$((validation_errors + 1))
  fi
  
  if [[ ! -f "$APP_DIR/package.json" ]]; then
    log_error "❌ Root package.json not found"
    validation_errors=$((validation_errors + 1))
  fi
  
  if [[ ! -d "$APP_DIR/server" ]]; then
    log_error "❌ Server directory not found"
    validation_errors=$((validation_errors + 1))
  fi
  
  if [[ ! -d "$APP_DIR/client" ]]; then
    log_error "❌ Client directory not found"
    validation_errors=$((validation_errors + 1))
  fi
  
  if [[ $validation_errors -eq 0 ]]; then
    log_success "🎉 Repository and Environment Setup completed successfully!"
    log_info "✅ Application directory: $APP_DIR"
    log_info "✅ Environment: $DEPLOYMENT_ENV"
    log_info "✅ Repository structure validated"
    return 0
  else
    log_error "❌ Repository and Environment Setup completed with $validation_errors errors"
    return 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
