import React, { useState, useEffect } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { getApiBaseUrl } from '../../utils/network-utils';
import { usePermissions, AVAILABLE_PAGES } from '../../hooks/usePermissions';

const Sidebar = ({ isOpen, setIsOpen }) => {
  const location = useLocation();
  const [logoSettings, setLogoSettings] = useState(null);
  const { hasPermission, loading: permissionsLoading } = usePermissions();

  // Load logo settings from server
  useEffect(() => {
    const loadLogoSettings = async () => {
      try {
        const apiUrl = getApiBaseUrl();
        const response = await fetch(`${apiUrl}/upload/logos`);
        if (response.ok) {
          const result = await response.json();
          if (result.logos && result.logos.length > 0) {
            const latestLogo = result.logos[0];
            // Dynamic image base URL - works for any domain (truckhaul.top, truckhaul.local, custom domains)
            let imageBaseUrl;
            if (apiUrl.endsWith('/api')) {
              // Remove '/api' from the end to get base domain
              imageBaseUrl = apiUrl.slice(0, -4);
            } else {
              // Fallback to API URL if no /api path found
              imageBaseUrl = apiUrl;
            }
            setLogoSettings({
              src: `${imageBaseUrl}${latestLogo.path}`,
              alt: 'Company Logo',
              width: 40,
              height: 40
            });
          } else {
            setLogoSettings(null);
          }
        }
      } catch (error) {
        console.error('Error loading logo settings:', error);
        setLogoSettings(null);
      }
    };

    loadLogoSettings();

    // Listen for custom logo update events
    const handleLogoUpdate = () => {
      loadLogoSettings();
    };

    window.addEventListener('logoUpdated', handleLogoUpdate);
    return () => window.removeEventListener('logoUpdated', handleLogoUpdate);
  }, []);

  const allNavigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: '📊',
      description: 'Overview and analytics',
      permission: AVAILABLE_PAGES.DASHBOARD
    },
    {
      name: 'Users',
      href: '/users',
      icon: '👥',
      description: 'Manage system users',
      permission: AVAILABLE_PAGES.USERS
    },
    {
      name: 'Trucks',
      href: '/trucks',
      icon: '🚛',
      description: 'Manage dump trucks',
      permission: AVAILABLE_PAGES.TRIPS // Trucks are part of trip management
    },
    {
      name: 'Drivers',
      href: '/drivers',
      icon: '👤',
      description: 'Manage drivers',
      permission: AVAILABLE_PAGES.USERS // Drivers are managed like users
    },
    {
      name: 'Driver Attendance',
      href: '/driver-attendance',
      icon: '📊',
      description: 'Driver time tracking & reports',
      permission: AVAILABLE_PAGES.USERS // Driver attendance requires user management permission
    },
    {
      name: 'Locations',
      href: '/locations',
      icon: '📍',
      description: 'Loading & unloading points',
      permission: AVAILABLE_PAGES.TRIPS // Locations are part of trip management
    },
    {
      name: 'Assignments',
      href: '/assignments',
      icon: '📋',
      description: 'Route assignments',
      permission: AVAILABLE_PAGES.ASSIGNMENTS
    },
    {
      name: 'Shift Management',
      href: '/shifts',
      icon: '🔄',
      description: 'Manage driver shifts',
      permission: AVAILABLE_PAGES.SHIFTS
    },
    {
      name: 'Trip Monitoring',
      href: '/trips',
      icon: '🛣️',
      description: 'Track active trips',
      permission: AVAILABLE_PAGES.TRIPS
    },
    {
      name: 'Truck Trip Summary',
      href: '/trucks/trip-summary',
      icon: '📝',
      description: 'Summary of trips per truck',
      permission: AVAILABLE_PAGES.TRIPS
    },
    {
      name: 'QR Scanner',
      href: '/scanner',
      icon: '📱',
      description: 'Scan QR codes',
      permission: AVAILABLE_PAGES.TRIPS // Scanner is part of trip workflow
    },
    {
      name: 'Assignment Monitoring',
      href: '/assignment-monitoring',
      icon: '📊',
      description: 'Monitor dynamic assignments',
      permission: AVAILABLE_PAGES.ASSIGNMENTS
    },
    {
      name: 'Fleet Resource Monitor',
      href: '/fleet-resources',
      icon: '🚛',
      description: 'Monitor driver and truck resources',
      permission: AVAILABLE_PAGES.ANALYTICS
    },
    {
      name: 'Analytics & Reports',
      href: '/analytics',
      icon: '📊',
      description: 'Comprehensive analytics dashboard',
      permission: AVAILABLE_PAGES.ANALYTICS
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: '⚙️',
      description: 'System configuration',
      permission: AVAILABLE_PAGES.SETTINGS
    }
  ];

  // Filter navigation based on user permissions
  const navigation = permissionsLoading 
    ? [] // Show empty navigation while loading permissions
    : allNavigation.filter(item => hasPermission(item.permission));

  const closeSidebar = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
        <div className="flex-1 flex flex-col min-h-0 bg-white border-r border-secondary-200">
          {/* Logo */}
          <div className="flex items-center h-16 flex-shrink-0 px-4 border-b border-secondary-200">
            <div className="flex items-center w-full">
              {/* Logo Container - Fixed size with proper containment */}
              <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center overflow-hidden bg-white rounded-md">
                {logoSettings?.src ? (
                  <img
                    src={logoSettings.src}
                    alt={logoSettings.alt}
                    className="object-contain max-w-full max-h-full"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      // Fallback to default emoji if logo fails to load
                    }}
                  />
                ) : (
                  <span className="text-2xl">🚛</span>
                )}
              </div>
              
              {/* Title with proper spacing and text wrapping */}
              <div className="ml-3 flex-1 min-w-0">
                <h1 className="text-lg font-semibold text-secondary-800 font-custom-header truncate">
                  Hauling QR
                </h1>
                <p className="text-xs text-secondary-500 font-custom-footer truncate">
                  Trip Management
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 flex flex-col overflow-y-auto">
            <nav className="flex-1 px-2 py-4 space-y-2">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors duration-200 font-custom-sidebar ${
                      isActive
                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                        : 'text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900'
                    }`}
                  >
                    <span className="text-lg mr-3">{item.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-secondary-500 mt-0.5">
                        {item.description}
                      </div>
                    </div>
                  </NavLink>
                );
              })}
            </nav>

            {/* System Status */}
            <div className="p-4 border-t border-secondary-200">
              <div className="bg-success-50 border border-success-200 rounded-lg p-3">
                <div className="flex items-center">
                  <span className="text-success-500 text-lg">🤖</span>
                  <div className="ml-2">
                    <p className="text-xs font-medium text-success-800">
                      Hauling QR Trip Management System
                    </p>
                    <p className="text-xs text-success-600">
                      Powered by AI
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-2 text-center">
                <p className="text-xs text-secondary-500">
                  © 2025 • Developer: Ariez-AI
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-white transform transition-transform duration-300 ease-in-out mobile-sidebar ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="mobile-sidebar-content border-r border-secondary-200">
          {/* Mobile Logo */}
          <div className="flex items-center justify-between h-16 flex-shrink-0 px-4 border-b border-secondary-200">
            <div className="flex items-center flex-1 min-w-0 pr-2">
              {/* Logo Container - Fixed size with proper containment */}
              <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center overflow-hidden bg-white rounded-md">
                {logoSettings?.src ? (
                  <img
                    src={logoSettings.src}
                    alt={logoSettings.alt}
                    className="object-contain max-w-full max-h-full"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      // Fallback to default emoji if logo fails to load
                    }}
                  />
                ) : (
                  <span className="text-2xl">🚛</span>
                )}
              </div>
              
              {/* Title with proper spacing and text wrapping */}
              <div className="ml-3 flex-1 min-w-0">
                <h1 className="text-lg font-semibold text-secondary-800 font-custom-header truncate">
                  Hauling QR
                </h1>
                <p className="text-xs text-secondary-500 font-custom-footer truncate">
                  Trip Management
                </p>
              </div>
            </div>
            <button
              onClick={closeSidebar}
              className="p-2 rounded-md text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100"
            >
              <span className="sr-only">Close sidebar</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Mobile Navigation */}
          <div className="mobile-sidebar-nav mobile-scroll-container">
            <nav className="px-2 py-4 space-y-2">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    onClick={closeSidebar}
                    className={`group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors duration-200 font-custom-sidebar ${
                      isActive
                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                        : 'text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900'
                    }`}
                  >
                    <span className="text-lg mr-3">{item.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-secondary-500 mt-0.5">
                        {item.description}
                      </div>
                    </div>
                  </NavLink>
                );
              })}
            </nav>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;