/**
 * Main application entry point
 * 
 * This file initializes the React application with:
 * - Error boundary for graceful error handling
 * - Performance monitoring for optimization insights
 * - Service worker registration for PWA functionality
 * - Security initialization for production environments
 * 
 * <AUTHOR> QR Trip System
 * @version 1.0.0
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import { registerSW } from './utils/serviceWorkerRegistration';
import { initializePerformanceMonitoring } from './utils/performanceMonitor';

// Application configuration
const APP_CONFIG = {
  enablePerformanceMonitoring: true,
  enableServiceWorker: true,
  strictMode: true,
  environment: process.env.NODE_ENV || 'development'
};

/**
 * Initialize application with all required services
 */
function initializeApp() {
  // Initialize performance monitoring if enabled
  if (APP_CONFIG.enablePerformanceMonitoring) {
    initializePerformanceMonitoring();
  }

  // Create React root and render application
  const rootElement = document.getElementById('root');
  if (!rootElement) {
    throw new Error('Root element not found. Please ensure index.html contains a div with id="root"');
  }

  const root = ReactDOM.createRoot(rootElement);
  const AppComponent = APP_CONFIG.strictMode ? (
    <React.StrictMode>
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    </React.StrictMode>
  ) : (
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  );

  root.render(AppComponent);

  // Register service worker for PWA functionality if enabled
  if (APP_CONFIG.enableServiceWorker) {
    registerSW({
      enableInDev: true, // Enable service worker in development for PWA testing
      onUpdate: (registration) => {
        console.log('New content available! Please refresh.');
        // Future enhancement: Show user notification with refresh button
        // Could dispatch a custom event here for the app to handle
        window.dispatchEvent(new CustomEvent('sw-update-available', {
          detail: { registration }
        }));
      },
      onSuccess: (registration) => {
        console.log('Content is cached for offline use.');
        window.dispatchEvent(new CustomEvent('sw-ready', {
          detail: { registration }
        }));
      },
      onError: (error) => {
        console.error('SW registration failed:', error);
        window.dispatchEvent(new CustomEvent('sw-error', {
          detail: { error }
        }));
      }
    });
  }
}

// Initialize the application
try {
  initializeApp();
} catch (error) {
  console.error('Failed to initialize application:', error);

  // Fallback error display
  document.body.innerHTML = `
    <div style="
      display: flex; 
      align-items: center; 
      justify-content: center; 
      height: 100vh; 
      font-family: system-ui, -apple-system, sans-serif;
      background-color: #f3f4f6;
    ">
      <div style="
        background: white; 
        padding: 2rem; 
        border-radius: 0.5rem; 
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        max-width: 400px;
        text-align: center;
      ">
        <h1 style="color: #dc2626; margin-bottom: 1rem;">Application Error</h1>
        <p style="color: #6b7280; margin-bottom: 1rem;">
          The application failed to initialize. Please refresh the page or contact support.
        </p>
        <button 
          onclick="window.location.reload()" 
          style="
            background: #3b82f6; 
            color: white; 
            padding: 0.5rem 1rem; 
            border: none; 
            border-radius: 0.25rem; 
            cursor: pointer;
          "
        >
          Reload Page
        </button>
      </div>
    </div>
  `;
}