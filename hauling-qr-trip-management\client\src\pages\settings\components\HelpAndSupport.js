import React, { useState } from 'react';
import toast from 'react-hot-toast';

/**
 * Help & Support Component
 * Provides comprehensive support interface with FAQ, contact options, and embedded chat system
 * Accessible from System Administrator ViewBox and Settings page
 */

const HelpAndSupport = () => {
  const [activeTab, setActiveTab] = useState('faq');
  const [chatMessage, setChatMessage] = useState('');
  const [chatHistory, setChatHistory] = useState([
    {
      id: 1,
      type: 'system',
      message: 'Welcome to Hauling QR Trip System Support! How can we help you today?',
      timestamp: new Date()
    }
  ]);

  // FAQ data organized by categories
  const faqCategories = [
    {
      id: 'general',
      title: 'General System',
      icon: '🏠',
      questions: [
        {
          question: 'How do I reset my password?',
          answer: 'Contact your system administrator to reset your password. They can update your credentials through the Users Management section.'
        },
        {
          question: 'Why can\'t I access certain pages?',
          answer: 'Page access is controlled by user roles. Contact your administrator if you need access to additional features. Your current permissions are displayed in your user profile.'
        },
        {
          question: 'How do I update my profile information?',
          answer: 'Currently, profile updates must be done by system administrators through the Users Management section. Contact your admin with any changes needed.'
        }
      ]
    },
    {
      id: 'trips',
      title: 'Trip Management',
      icon: '🛣️',
      questions: [
        {
          question: 'How does the 4-phase trip workflow work?',
          answer: 'Trips follow this sequence: Assigned → Loading Start → Loading End → Unloading Start → Unloading End → Trip Completed. Each phase requires QR code scanning or manual completion.'
        },
        {
          question: 'What should I do if a trip gets stuck in one phase?',
          answer: 'Use the Trip Monitoring dashboard to manually advance or complete stuck trips. Look for the "Complete Trip" button in the trip details.'
        },
        {
          question: 'How do I handle emergency trip completions?',
          answer: 'Administrators can manually complete trips from any status using the Trip Monitoring dashboard. This bypasses normal workflow validation for emergency situations.'
        }
      ]
    },
    {
      id: 'qr',
      title: 'QR Code System',
      icon: '📱',
      questions: [
        {
          question: 'QR scanner is not working properly',
          answer: 'Ensure camera permissions are enabled, lighting is adequate, and QR codes are clean and undamaged. Try refreshing the scanner page or using a different device.'
        },
        {
          question: 'How do I generate new QR codes?',
          answer: 'QR codes can be generated from the respective management pages (Drivers, Trucks, Locations). Look for the "Generate QR" or QR code icon buttons.'
        },
        {
          question: 'What information is stored in QR codes?',
          answer: 'QR codes contain encrypted IDs and basic information. Driver QR codes contain driver_id and employee_id. Truck and location QR codes contain their respective identifiers.'
        }
      ]
    },
    {
      id: 'technical',
      title: 'Technical Issues',
      icon: '🔧',
      questions: [
        {
          question: 'The system is running slowly',
          answer: 'Try clearing your browser cache, refreshing the page, or using the Cache Management tool in Settings. Contact support if issues persist.'
        },
        {
          question: 'I\'m getting database connection errors',
          answer: 'This indicates a server issue. Check the System Health Monitor in Settings or contact your system administrator immediately.'
        },
        {
          question: 'How do I report a bug or request a feature?',
          answer: 'Use the support chat below or contact your system administrator. Provide detailed steps to reproduce any issues and include screenshots if possible.'
        }
      ]
    }
  ];

  const handleSendMessage = () => {
    if (!chatMessage.trim()) return;

    const newMessage = {
      id: chatHistory.length + 1,
      type: 'user',
      message: chatMessage,
      timestamp: new Date()
    };

    setChatHistory([...chatHistory, newMessage]);
    setChatMessage('');

    // Simulate support response
    setTimeout(() => {
      const supportResponse = {
        id: chatHistory.length + 2,
        type: 'support',
        message: 'Thank you for your message. A support representative will respond shortly. For urgent issues, please contact your system administrator directly.',
        timestamp: new Date()
      };
      setChatHistory(prev => [...prev, supportResponse]);
    }, 1000);

    toast.success('Message sent to support team');
  };

  const handleContactClick = (type, value) => {
    if (type === 'phone') {
      window.location.href = `tel:${value}`;
    } else if (type === 'email') {
      window.location.href = `mailto:${value}`;
    }
    toast.success(`Opening ${type} contact...`);
  };

  const renderFAQ = () => (
    <div className="space-y-6">
      {faqCategories.map((category) => (
        <div key={category.id} className="bg-white rounded-lg border border-secondary-200 p-6">
          <h3 className="text-lg font-semibold text-secondary-900 mb-4 flex items-center">
            <span className="mr-2">{category.icon}</span>
            {category.title}
          </h3>
          <div className="space-y-4">
            {category.questions.map((item, index) => (
              <div key={index} className="border-l-4 border-blue-200 pl-4">
                <h4 className="font-medium text-secondary-900 mb-2">{item.question}</h4>
                <p className="text-secondary-700 text-sm leading-relaxed">{item.answer}</p>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  const renderChat = () => (
    <div className="bg-white rounded-lg border border-secondary-200 p-6">
      <h3 className="text-lg font-semibold text-secondary-900 mb-4 flex items-center">
        <span className="mr-2">💬</span>
        Support Chat
      </h3>
      
      {/* Chat History */}
      <div className="border border-secondary-200 rounded-lg p-4 h-64 overflow-y-auto mb-4 bg-gray-50">
        {chatHistory.map((msg) => (
          <div key={msg.id} className={`mb-3 ${msg.type === 'user' ? 'text-right' : 'text-left'}`}>
            <div className={`inline-block max-w-xs lg:max-w-md px-3 py-2 rounded-lg text-sm ${
              msg.type === 'user' 
                ? 'bg-blue-600 text-white' 
                : msg.type === 'support'
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-200 text-gray-800'
            }`}>
              {msg.message}
            </div>
            <div className="text-xs text-secondary-500 mt-1">
              {msg.timestamp.toLocaleTimeString()}
            </div>
          </div>
        ))}
      </div>

      {/* Chat Input */}
      <div className="flex space-x-2">
        <input
          type="text"
          value={chatMessage}
          onChange={(e) => setChatMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          placeholder="Type your message..."
          className="flex-1 px-3 py-2 border border-secondary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          onClick={handleSendMessage}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Send
        </button>
      </div>
    </div>
  );

  const renderContacts = () => (
    <div className="space-y-6">
      {/* Emergency Contacts */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-red-800 mb-4 flex items-center">
          <span className="mr-2">🚨</span>
          Emergency Support
        </h3>
        <p className="text-red-700 mb-4">For urgent system issues or emergencies:</p>
        <div className="space-y-2">
          <button
            onClick={() => handleContactClick('phone', '(555) 911-HELP')}
            className="flex items-center space-x-2 text-red-600 hover:text-red-800 transition-colors"
          >
            <span>📞</span>
            <span className="font-medium">(555) 911-HELP</span>
          </button>
          <button
            onClick={() => handleContactClick('email', '<EMAIL>')}
            className="flex items-center space-x-2 text-red-600 hover:text-red-800 transition-colors"
          >
            <span>✉️</span>
            <span className="font-medium"><EMAIL></span>
          </button>
        </div>
      </div>

      {/* General Support */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-4 flex items-center">
          <span className="mr-2">🎧</span>
          General Support
        </h3>
        <p className="text-blue-700 mb-4">For general questions and technical support:</p>
        <div className="space-y-2">
          <button
            onClick={() => handleContactClick('phone', '(555) 123-TECH')}
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <span>📞</span>
            <span className="font-medium">(555) 123-TECH</span>
          </button>
          <button
            onClick={() => handleContactClick('email', '<EMAIL>')}
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <span>✉️</span>
            <span className="font-medium"><EMAIL></span>
          </button>
        </div>
        <div className="mt-4 text-sm text-blue-600">
          <p><strong>Support Hours:</strong> Monday - Friday, 8:00 AM - 6:00 PM</p>
          <p><strong>Response Time:</strong> Within 4 hours during business hours</p>
        </div>
      </div>

      {/* System Administrator */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-800 mb-4 flex items-center">
          <span className="mr-2">👨‍💼</span>
          System Administrator
        </h3>
        <p className="text-green-700 mb-4">For account issues, permissions, and system configuration:</p>
        <div className="space-y-2">
          <button
            onClick={() => handleContactClick('phone', '(555) 123-ADMIN')}
            className="flex items-center space-x-2 text-green-600 hover:text-green-800 transition-colors"
          >
            <span>📞</span>
            <span className="font-medium">(555) 123-ADMIN</span>
          </button>
          <button
            onClick={() => handleContactClick('email', '<EMAIL>')}
            className="flex items-center space-x-2 text-green-600 hover:text-green-800 transition-colors"
          >
            <span>✉️</span>
            <span className="font-medium"><EMAIL></span>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-secondary-900">🆘 Help & Support</h2>
        <p className="text-secondary-600 mt-1">
          Get help with the Hauling QR Trip System. Find answers to common questions, 
          contact support, or chat with our team.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-secondary-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'faq', label: 'FAQ', icon: '❓' },
            { id: 'chat', label: 'Support Chat', icon: '💬' },
            { id: 'contacts', label: 'Contact Info', icon: '📞' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'faq' && renderFAQ()}
        {activeTab === 'chat' && renderChat()}
        {activeTab === 'contacts' && renderContacts()}
      </div>
    </div>
  );
};

export default HelpAndSupport;
