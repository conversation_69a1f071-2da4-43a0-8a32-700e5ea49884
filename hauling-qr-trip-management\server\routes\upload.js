const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../../client/public/images');
    
    // Ensure the directory exists
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext).replace(/\s+/g, '-').toLowerCase();
    cb(null, `logo-${uniqueSuffix}${ext}`);
  }
});

// File filter for images
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, GIF, SVG, and WebP files are allowed.'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB limit
  }
});

// Health check endpoint for upload service
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Upload service is running',
    timestamp: new Date().toISOString()
  });
});

// POST endpoint for logo upload
router.post('/logo', upload.single('logo'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Return the relative path for the client
    const filePath = `/images/${req.file.filename}`;
    
    res.json({
      success: true,
      message: 'Logo uploaded successfully',
      filePath: filePath,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size
    });
  } catch (error) {
    console.error('Error uploading logo:', error);
    res.status(500).json({ error: 'Failed to upload logo' });
  }
});

// GET endpoint to list uploaded logos
router.get('/logos', (req, res) => {
  try {
    const uploadPath = path.join(__dirname, '../../client/public/images');
    
    if (!fs.existsSync(uploadPath)) {
      return res.json({ logos: [] });
    }

    const files = fs.readdirSync(uploadPath)
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'].includes(ext);
      })
      .map(file => ({
        filename: file,
        path: `/images/${file}`,
        size: fs.statSync(path.join(uploadPath, file)).size,
        created: fs.statSync(path.join(uploadPath, file)).birthtime
      }))
      .sort((a, b) => new Date(b.created) - new Date(a.created));

    res.json({ logos: files });
  } catch (error) {
    console.error('Error listing logos:', error);
    res.status(500).json({ error: 'Failed to list logos' });
  }
});

// DELETE endpoint to remove logo
router.delete('/logo/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '../../client/public/images', filename);

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      res.json({ success: true, message: 'Logo deleted successfully' });
    } else {
      res.status(404).json({ error: 'Logo file not found' });
    }
  } catch (error) {
    console.error('Error deleting logo:', error);
    res.status(500).json({ error: 'Failed to delete logo' });
  }
});

module.exports = router;