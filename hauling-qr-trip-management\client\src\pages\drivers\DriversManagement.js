import React, { useState, useEffect, useCallback } from 'react';
import { driversAPI } from '../../services/api';
import DriversTable from './components/DriversTable';
import DriverFormModal from './components/DriverFormModal';
import QRCodeModal from '../../components/common/QRCodeModal';
import DeleteConfirmModal from '../../components/common/DeleteConfirmModal';
import toast from 'react-hot-toast';

const DriversManagement = () => {
  const [drivers, setDrivers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  });

  // Modal states
  const [showFormModal, setShowFormModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState(null);
  const [qrData, setQrData] = useState(null);

  // Filter and search states
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    sortBy: 'full_name',
    sortOrder: 'asc'
  });

  // Load drivers with error handling
  const loadDrivers = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pagination.itemsPerPage,
        ...filters
      };

      const response = await driversAPI.getAll({ params });
      setDrivers(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Error loading drivers:', error);
      
      // Better error handling
      if (error.code === 'ERR_NETWORK') {
        toast.error('Network error. Please check your connection.');
      } else if (error.response?.status === 429) {
        toast.error('Too many requests. Please wait a moment.');
      } else {
        toast.error(error.response?.data?.message || 'Failed to load drivers');
      }
    } finally {
      setLoading(false);
    }
  }, [pagination.itemsPerPage, filters]);


  // Initial load
  useEffect(() => {
    loadDrivers();
  }, [loadDrivers]);

  // Handle page change
  const handlePageChange = (page) => {
    loadDrivers(page);
  };

  // Handle filter change
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle search
  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  // Handle sort
  const handleSort = (column) => {
    const newOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: newOrder
    }));
  };

  // Handle create driver
  const handleCreate = () => {
    setSelectedDriver(null);
    setShowFormModal(true);
  };

  // Handle edit driver
  const handleEdit = (driver) => {
    setSelectedDriver(driver);
    setShowFormModal(true);
  };

  // Handle delete driver
  const handleDelete = (driver) => {
    setSelectedDriver(driver);
    setShowDeleteModal(true);
  };

  // Handle view QR code
  const handleViewQR = async (driver) => {
    try {
      setSelectedDriver(driver);
      const response = await driversAPI.generateQR(driver.id);
      
      // Extract the actual QR data from the API response
      const qrData = response.data.data;
      
      // Log the response for debugging
      console.log('QR Generation Response:', response.data);
      console.log('QR Data:', qrData);
      
      setQrData(qrData);
      setShowQRModal(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error('Failed to generate QR code');
    }
  };

  // Handle form submit
  const handleFormSubmit = async (formData) => {
    try {
      if (selectedDriver) {
        // Update
        await driversAPI.update(selectedDriver.id, formData);
        toast.success('Driver updated successfully');
      } else {
        // Create
        await driversAPI.create(formData);
        toast.success('Driver created successfully');
      }
      
      setShowFormModal(false);
      loadDrivers(pagination.currentPage);
    } catch (error) {
      console.error('Error saving driver:', error);
      const message = error.response?.data?.message || 'Failed to save driver';
      toast.error(message);
    }
  };

  // Handle delete confirm
  const handleDeleteConfirm = async () => {
    try {
      await driversAPI.delete(selectedDriver.id);
      toast.success('Driver deleted successfully');
      setShowDeleteModal(false);
      loadDrivers(pagination.currentPage);
    } catch (error) {
      console.error('Error deleting driver:', error);
      const message = error.response?.data?.message || 'Failed to delete driver';
      toast.error(message);
    }
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      sortBy: 'full_name',
      sortOrder: 'asc'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-secondary-900 sm:text-3xl sm:truncate">
            👤 Drivers Management
          </h1>
          <p className="mt-1 text-sm text-secondary-500">
            Manage driver information, licenses, assignments, and generate QR codes for time tracking.
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            onClick={handleCreate}
            className="btn btn-primary"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add New Driver
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow-sm rounded-lg border border-secondary-200">
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search drivers..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              {/* Status Filter */}
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange({ status: e.target.value })}
                className="block w-full sm:w-auto px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
                <option value="on_leave">On Leave</option>
                <option value="terminated">Terminated</option>
              </select>

              {/* Clear Filters */}
              <button
                onClick={clearFilters}
                className="btn btn-secondary whitespace-nowrap"
              >
                Clear Filters
              </button>
            </div>
          </div>

          {/* Active Filters Display */}
          {(filters.search || filters.status) && (
            <div className="mt-4 flex flex-wrap gap-2">
              {filters.search && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800">
                  Search: "{filters.search}"
                  <button
                    onClick={() => handleSearch('')}
                    className="ml-2 text-primary-600 hover:text-primary-800"
                  >
                    ×
                  </button>
                </span>
              )}
              {filters.status && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800">
                  Status: {filters.status}
                  <button
                    onClick={() => handleFilterChange({ status: '' })}
                    className="ml-2 text-primary-600 hover:text-primary-800"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Info Banner */}
      <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
        <p className="text-sm text-primary-700">
          Manage drivers, generate QR codes for time tracking, and monitor driver activity efficiently.
        </p>
      </div>

      {/* Drivers Table */}
      <DriversTable
        drivers={drivers}
        loading={loading}
        pagination={pagination}
        filters={filters}
        onPageChange={handlePageChange}
        onSort={handleSort}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onViewQR={handleViewQR}
      />

      {/* Modals */}
      {showFormModal && (
        <DriverFormModal
          driver={selectedDriver}
          onClose={() => setShowFormModal(false)}
          onSubmit={handleFormSubmit}
        />
      )}

      {showDeleteModal && selectedDriver && (
        <DeleteConfirmModal
          title="Delete Driver"
          message={`Are you sure you want to delete driver ${selectedDriver.full_name}? This action cannot be undone.`}
          onConfirm={handleDeleteConfirm}
          onCancel={() => setShowDeleteModal(false)}
        />
      )}

      {/* QR Code Modal */}
      {showQRModal && qrData && (
        <QRCodeModal
          qrData={qrData}
          onClose={() => {
            setShowQRModal(false);
            setQrData(null);
          }}
        />
      )}
    </div>
  );
};

export default DriversManagement;