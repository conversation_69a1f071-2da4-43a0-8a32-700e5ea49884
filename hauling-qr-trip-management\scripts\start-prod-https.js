#!/usr/bin/env node
/**
 * Production Mode Startup Script (HTTPS)
 * Automatically configures and starts the system in production mode with HTTPS
 */

const { spawn } = require('child_process');
const { loadConfig, writeClientEnv, displayConfig } = require('../config-loader');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Hauling QR Trip System in Production Mode (HTTPS)...\n');

// Check if SSL certificates exist
const sslProdPath = path.join(__dirname, '..', 'server', 'ssl', 'production');
const certPath = path.join(sslProdPath, 'fullchain.crt');
const keyPath = path.join(sslProdPath, 'server.key');

if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
  console.log('⚠️  Production SSL certificates not found!');
  console.log('📋 Please ensure the following files exist:');
  console.log(`   - ${certPath}`);
  console.log(`   - ${keyPath}`);
  console.log('\n💡 For Let\'s Encrypt certificates, run:');
  console.log('   sudo certbot certonly --standalone -d yourdomain.com');
  console.log('   Then copy the certificates to the ssl/production directory');
  console.log('\n🔧 For development with self-signed certificates, use:');
  console.log('   npm run dev:https');
  process.exit(1);
}

console.log('🔐 Production SSL certificates found');

// Configure environment for production HTTPS
const envPath = path.join(__dirname, '..', '.env');
let envContent = fs.readFileSync(envPath, 'utf8');

// Update configuration
envContent = envContent.replace(/^NODE_ENV=.*/m, 'NODE_ENV=production');
envContent = envContent.replace(/^ENABLE_HTTPS=.*/m, 'ENABLE_HTTPS=true');

// Write updated .env file
fs.writeFileSync(envPath, envContent);

// Load configuration and generate client env
const config = loadConfig();
writeClientEnv(config);
displayConfig(config);

console.log('\n🏗️  Building client for production...\n');

// Build the client first
const buildProcess = spawn('npm', ['run', 'build'], {
  cwd: path.join(__dirname, '..', 'client'),
  stdio: 'inherit'
});

buildProcess.on('exit', (code) => {
  if (code !== 0) {
    console.error('❌ Client build failed');
    process.exit(1);
  }
  
  console.log('\n✅ Client build completed');
  console.log('\n🔄 Starting production server with HTTPS...\n');
  
  // Start the production server with HTTPS
  const serverProcess = spawn('node', ['production-https.js'], {
    cwd: path.join(__dirname, '..', 'server'),
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production' }
  });
  
  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down production server...');
    serverProcess.kill('SIGINT');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down production server...');
    serverProcess.kill('SIGTERM');
    process.exit(0);
  });

  serverProcess.on('exit', (code) => {
    console.log(`\n✅ Production server stopped with code ${code}`);
    process.exit(code);
  });
});
