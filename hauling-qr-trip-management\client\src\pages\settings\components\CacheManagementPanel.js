import React, { useState, useEffect } from 'react';
import { getApiBaseUrl } from '../../../utils/network-utils';

const CacheManagementPanel = () => {
  const [loading, setLoading] = useState({});
  const [results, setResults] = useState({});
  const [cacheStats, setCacheStats] = useState({
    localStorage: 0,
    sessionStorage: 0,
    memoryCache: 0,
    shiftCache: 0
  });

  // Cache types configuration
  const cacheTypes = [
    {
      id: 'browser-storage',
      name: 'Browser Storage',
      description: 'Clear localStorage and sessionStorage data',
      icon: '🗄️',
      color: 'border-blue-200 bg-blue-50',
      buttonColor: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      id: 'memory-cache',
      name: 'Memory Cache',
      description: 'Clear in-memory application caches',
      icon: '💾',
      color: 'border-purple-200 bg-purple-50',
      buttonColor: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      id: 'shift-cache',
      name: 'Shift Display Cache',
      description: 'Clear shift-related display caches',
      icon: '🔄',
      color: 'border-green-200 bg-green-50',
      buttonColor: 'bg-green-600 hover:bg-green-700'
    },
    {
      id: 'analytics-cache',
      name: 'Analytics Cache',
      description: 'Clear analytics and performance data cache',
      icon: '📊',
      color: 'border-orange-200 bg-orange-50',
      buttonColor: 'bg-orange-600 hover:bg-orange-700'
    },
    {
      id: 'all-cache',
      name: 'Complete Cache Reset',
      description: 'Clear all caches and storage (includes logout)',
      icon: '🧹',
      color: 'border-red-200 bg-red-50',
      buttonColor: 'bg-red-600 hover:bg-red-700'
    }
  ];

  // Calculate cache statistics
  const calculateCacheStats = () => {
    try {
      let localStorageSize = 0;
      let sessionStorageSize = 0;

      // Calculate localStorage size
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          localStorageSize += (localStorage[key].length + key.length) * 2;
        }
      }

      // Calculate sessionStorage size
      for (let key in sessionStorage) {
        if (sessionStorage.hasOwnProperty(key)) {
          sessionStorageSize += (sessionStorage[key].length + key.length) * 2;
        }
      }

      setCacheStats({
        localStorage: Math.round(localStorageSize / 1024 * 100) / 100, // KB
        sessionStorage: Math.round(sessionStorageSize / 1024 * 100) / 100, // KB
        memoryCache: 0, // Will be updated by API
        shiftCache: 0   // Will be updated by API
      });
    } catch (error) {
      console.error('Error calculating cache stats:', error);
    }
  };

  useEffect(() => {
    calculateCacheStats();
  }, []);

  const clearBrowserStorage = () => {
    try {
      const keysToPreserve = ['hauling_token', 'hauling_user']; // Don't log out user
      const keysToClear = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && !keysToPreserve.includes(key)) {
          keysToClear.push(key);
        }
      }

      keysToClear.forEach(key => localStorage.removeItem(key));
      sessionStorage.clear();
      
      return { success: true, clearedKeys: keysToClear.length };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const clearMemoryCache = async () => {
    try {
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/admin/clear-cache`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type: 'memory' })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const clearShiftCache = async () => {
    try {
      // Import and use ShiftDisplayHelper if available
      if (window.ShiftDisplayHelper) {
        window.ShiftDisplayHelper.clearAllCache();
      }

      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/admin/clear-cache`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type: 'shift' })
      });

      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const clearAnalyticsCache = async () => {
    try {
      const apiUrl = getApiBaseUrl();
      const response = await fetch(`${apiUrl}/admin/clear-cache`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type: 'analytics' })
      });

      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const clearAllCache = async () => {
    try {
      // Clear browser storage (except auth)
      const browserResult = clearBrowserStorage();
      
      // Clear memory caches
      const memoryResult = await clearMemoryCache();
      const shiftResult = await clearShiftCache();
      const analyticsResult = await clearAnalyticsCache();

      // Force reload to clear any remaining caches
      window.location.reload();

      return {
        success: true,
        details: {
          browser: browserResult,
          memory: memoryResult,
          shift: shiftResult,
          analytics: analyticsResult
        }
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const handleCacheClear = async (cacheType) => {
    try {
      setLoading(prev => ({ ...prev, [cacheType]: true }));
      setResults(prev => ({ ...prev, [cacheType]: null }));

      let result;
      
      switch (cacheType) {
        case 'browser-storage':
          result = clearBrowserStorage();
          break;
        case 'memory-cache':
          result = await clearMemoryCache();
          break;
        case 'shift-cache':
          result = await clearShiftCache();
          break;
        case 'analytics-cache':
          result = await clearAnalyticsCache();
          break;
        case 'all-cache':
          result = await clearAllCache();
          break;
        default:
          throw new Error('Unknown cache type');
      }

      setResults(prev => ({ ...prev, [cacheType]: result }));
      
      if (result.success) {
        alert(`✅ ${cacheTypes.find(c => c.id === cacheType)?.name} cleared successfully!`);
        calculateCacheStats(); // Refresh stats
      } else {
        alert(`❌ Error: ${result.error || 'Unknown error'}`);
      }

    } catch (error) {
      console.error(`Error clearing ${cacheType}:`, error);
      alert(`❌ Error clearing cache: ${error.message}`);
    } finally {
      setLoading(prev => ({ ...prev, [cacheType]: false }));
    }
  };


  const getButtonColor = (cacheType) => {
    const cache = cacheTypes.find(c => c.id === cacheType);
    return cache ? cache.buttonColor : 'bg-gray-600 hover:bg-gray-700';
  };

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200">
      <div className="px-6 py-4 border-b border-secondary-200">
        <div>
          <h3 className="text-lg font-medium text-secondary-900">
            🧹 Cache Management
          </h3>
          <p className="text-sm text-secondary-500 mt-1">
            Clear various caches and storage to fix display issues or reset application state
          </p>
        </div>
      </div>

      <div className="p-6">
        {/* Cache Statistics */}
        <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h4 className="text-sm font-medium text-gray-900 mb-2">📊 Current Cache Usage</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600">localStorage:</span>
              <span className="ml-1 font-medium">{cacheStats.localStorage} KB</span>
            </div>
            <div>
              <span className="text-gray-600">sessionStorage:</span>
              <span className="ml-1 font-medium">{cacheStats.sessionStorage} KB</span>
            </div>
            <div>
              <span className="text-gray-600">Memory Cache:</span>
              <span className="ml-1 font-medium">{cacheStats.memoryCache} items</span>
            </div>
            <div>
              <span className="text-gray-600">Shift Cache:</span>
              <span className="ml-1 font-medium">{cacheStats.shiftCache} items</span>
            </div>
          </div>
        </div>

        {/* Cache Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {cacheTypes.map((cache) => (
            <div key={cache.id} className={`border rounded-lg p-4 ${cache.color}`}>
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium text-secondary-900">{cache.name}</h4>
                  <p className="text-sm text-secondary-600 mt-1">{cache.description}</p>
                </div>
                <div className="text-2xl">{cache.icon}</div>
              </div>

              <button
                onClick={() => handleCacheClear(cache.id)}
                disabled={loading[cache.id]}
                className={`mt-4 w-full px-3 py-1.5 text-sm font-medium text-white rounded ${getButtonColor(cache.id)} disabled:opacity-50`}
              >
                {loading[cache.id] ? '⏳ Clearing...' : `🧹 Clear ${cache.name}`}
              </button>

              {results[cache.id] && (
                <div className="mt-3 p-2 bg-white rounded text-xs">
                  {results[cache.id].success ? (
                    <span className="text-green-600">✅ Cleared successfully</span>
                  ) : (
                    <span className="text-red-600">❌ Error: {results[cache.id].error}</span>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Warning Section */}
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <h5 className="text-sm font-medium text-red-900 mb-2">⚠️ Important Notes</h5>
          <ul className="text-sm text-red-700 space-y-1">
            <li>• Clearing browser storage will reset appearance settings and preferences</li>
            <li>• "Complete Cache Reset" will log you out and clear all stored data</li>
            <li>• Some caches will automatically rebuild when you use the application</li>
            <li>• Use these tools when experiencing display issues or stale data</li>
          </ul>
        </div>

        {/* Quick Actions */}
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h5 className="text-sm font-medium text-blue-900 mb-2">💡 When to Use</h5>
          <div className="text-sm text-blue-700 space-y-1">
            <p>• <strong>Display Issues:</strong> Clear browser storage and memory cache</p>
            <p>• <strong>Stale Shift Data:</strong> Clear shift display cache</p>
            <p>• <strong>Analytics Problems:</strong> Clear analytics cache</p>
            <p>• <strong>Complete Reset:</strong> Use "Complete Cache Reset" for fresh start</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CacheManagementPanel;