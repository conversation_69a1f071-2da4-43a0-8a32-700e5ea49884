/**
 * PWA Investigation Utilities
 * Comprehensive tools to investigate PWA-specific sync issues
 */

import { backgroundSync } from '../services/backgroundSync';
import { driverConnectOffline } from '../services/driverConnectOffline';

/**
 * Detect PWA environment with detailed analysis
 */
export const detectPWAEnvironment = () => {
  const standaloneMatch = window.matchMedia('(display-mode: standalone)').matches;
  const iOSStandalone = window.navigator.standalone === true;
  const androidApp = document.referrer.includes('android-app://');
  const windowMode = window.matchMedia('(display-mode: browser)').matches;
  
  return {
    isPWAMode: standaloneMatch || iOSStandalone || androidApp,
    detectionMethods: {
      standaloneMatch,
      iOSStandalone,
      androidApp,
      windowMode
    },
    displayMode: standaloneMatch ? 'standalone' : windowMode ? 'browser' : 'unknown',
    userAgent: navigator.userAgent,
    currentPath: window.location.pathname,
    referrer: document.referrer,
    networkStatus: navigator.onLine ? 'online' : 'offline',
    timestamp: new Date().toISOString()
  };
};

/**
 * Test service worker communication in PWA vs browser
 */
export const testServiceWorkerCommunication = async () => {
  if (!('serviceWorker' in navigator)) {
    return { error: 'Service Worker not supported' };
  }

  try {
    const registration = await navigator.serviceWorker.ready;
    const controller = navigator.serviceWorker.controller;
    
    if (!controller) {
      return { error: 'No service worker controller available' };
    }

    // Test message sending
    const testMessage = {
      type: 'PWA_INVESTIGATION_TEST',
      timestamp: new Date().toISOString(),
      pwaEnvironment: detectPWAEnvironment()
    };

    return new Promise((resolve) => {
      const messageHandler = (event) => {
        if (event.data?.type === 'PWA_INVESTIGATION_RESPONSE') {
          navigator.serviceWorker.removeEventListener('message', messageHandler);
          resolve({
            success: true,
            response: event.data,
            roundTripTime: Date.now() - testMessage.timestamp
          });
        }
      };

      navigator.serviceWorker.addEventListener('message', messageHandler);
      controller.postMessage(testMessage);

      // Timeout after 5 seconds
      setTimeout(() => {
        navigator.serviceWorker.removeEventListener('message', messageHandler);
        resolve({ error: 'Service worker communication timeout' });
      }, 5000);
    });
  } catch (error) {
    return { error: error.message };
  }
};

/**
 * Compare sync behavior in PWA vs browser mode
 */
export const compareSyncBehavior = async () => {
  const pwaEnv = detectPWAEnvironment();
  const results = {
    environment: pwaEnv,
    syncTest: null,
    queueStatus: null,
    referenceDataTest: null,
    errors: []
  };

  try {
    // Test queue status
    const connectionCount = await driverConnectOffline.getPendingCount();
    const pendingConnections = await driverConnectOffline.getPendingConnections();
    
    results.queueStatus = {
      connectionCount,
      pendingConnections: pendingConnections.map(conn => ({
        id: conn.id,
        action: conn.apiPayload?.action,
        timestamp: conn.timestamp,
        status: conn.status
      }))
    };

    // Test reference data skip logic
    const isDriverConnectPWA = window.location.pathname.includes('/driver-connect') || 
                                window.location.pathname === '/driver-connect';
    
    results.referenceDataTest = {
      shouldSkipReferenceData: isDriverConnectPWA,
      currentPath: window.location.pathname,
      pathIncludes: window.location.pathname.includes('/driver-connect'),
      pathEquals: window.location.pathname === '/driver-connect'
    };

    // Test actual sync operation
    console.log('[PWA Investigation] Starting sync test...');
    const syncResults = await backgroundSync.startSync();
    
    results.syncTest = {
      success: true,
      results: syncResults,
      driverConnectionsOk: syncResults && typeof syncResults.driverConnections === 'object',
      referenceDataOk: syncResults && syncResults.referenceData && 
                       (syncResults.referenceData.summary?.successful >= 0 || 
                        syncResults.referenceData.summary?.skipped >= 0)
    };

  } catch (error) {
    results.errors.push({
      type: 'sync_test_error',
      message: error.message,
      stack: error.stack
    });
  }

  return results;
};

/**
 * Monitor sync status changes over time
 */
export const monitorSyncStatusChanges = (callback, duration = 30000) => {
  const statusHistory = [];
  let previousStatus = null;
  
  const checkStatus = async () => {
    try {
      const connectionCount = await driverConnectOffline.getPendingCount();
      const networkStatus = navigator.onLine ? 'online' : 'offline';
      const pwaEnv = detectPWAEnvironment();
      
      const currentStatus = {
        timestamp: new Date().toISOString(),
        connectionCount,
        networkStatus,
        isPWAMode: pwaEnv.isPWAMode,
        displayMode: pwaEnv.displayMode
      };
      
      statusHistory.push(currentStatus);
      
      // Detect changes
      if (previousStatus) {
        const changes = {};
        if (previousStatus.connectionCount !== currentStatus.connectionCount) {
          changes.connectionCount = { from: previousStatus.connectionCount, to: currentStatus.connectionCount };
        }
        if (previousStatus.networkStatus !== currentStatus.networkStatus) {
          changes.networkStatus = { from: previousStatus.networkStatus, to: currentStatus.networkStatus };
        }
        if (previousStatus.isPWAMode !== currentStatus.isPWAMode) {
          changes.isPWAMode = { from: previousStatus.isPWAMode, to: currentStatus.isPWAMode };
        }
        
        if (Object.keys(changes).length > 0) {
          callback({
            type: 'status_change',
            changes,
            currentStatus,
            previousStatus,
            history: statusHistory
          });
        }
      }
      
      previousStatus = currentStatus;
      callback({
        type: 'status_update',
        currentStatus,
        history: statusHistory
      });
      
    } catch (error) {
      callback({
        type: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  };
  
  // Initial check
  checkStatus();
  
  // Set up monitoring interval
  const interval = setInterval(checkStatus, 2000); // Check every 2 seconds
  
  // Stop monitoring after duration
  setTimeout(() => {
    clearInterval(interval);
    callback({
      type: 'monitoring_complete',
      duration,
      totalChecks: statusHistory.length,
      history: statusHistory
    });
  }, duration);
  
  return () => clearInterval(interval); // Return cleanup function
};

/**
 * Test offline-to-online transition specifically
 */
export const testOfflineToOnlineTransition = async () => {
  const results = {
    initialState: null,
    transitionSteps: [],
    finalState: null,
    errors: []
  };

  try {
    // Capture initial state
    results.initialState = {
      isOnline: navigator.onLine,
      connectionCount: await driverConnectOffline.getPendingCount(),
      pwaEnvironment: detectPWAEnvironment(),
      timestamp: new Date().toISOString()
    };

    results.transitionSteps.push({
      step: 'initial_state_captured',
      data: results.initialState,
      timestamp: new Date().toISOString()
    });

    // If we're online, we can't test the transition
    if (navigator.onLine) {
      results.transitionSteps.push({
        step: 'already_online',
        message: 'Cannot test offline-to-online transition - already online',
        timestamp: new Date().toISOString()
      });
      return results;
    }

    // Monitor for online event
    return new Promise((resolve) => {
      const onlineHandler = async () => {
        try {
          results.transitionSteps.push({
            step: 'online_event_triggered',
            timestamp: new Date().toISOString()
          });

          // Wait a moment for sync status to update
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Test sync after coming online
          const syncResults = await backgroundSync.startSync();
          
          results.transitionSteps.push({
            step: 'sync_attempted',
            syncResults,
            timestamp: new Date().toISOString()
          });

          // Capture final state
          results.finalState = {
            isOnline: navigator.onLine,
            connectionCount: await driverConnectOffline.getPendingCount(),
            pwaEnvironment: detectPWAEnvironment(),
            timestamp: new Date().toISOString()
          };

          results.transitionSteps.push({
            step: 'final_state_captured',
            data: results.finalState,
            timestamp: new Date().toISOString()
          });

          window.removeEventListener('online', onlineHandler);
          resolve(results);

        } catch (error) {
          results.errors.push({
            step: 'transition_error',
            error: error.message,
            timestamp: new Date().toISOString()
          });
          window.removeEventListener('online', onlineHandler);
          resolve(results);
        }
      };

      window.addEventListener('online', onlineHandler);

      // Timeout after 60 seconds
      setTimeout(() => {
        window.removeEventListener('online', onlineHandler);
        results.transitionSteps.push({
          step: 'timeout',
          message: 'Timeout waiting for online event',
          timestamp: new Date().toISOString()
        });
        resolve(results);
      }, 60000);
    });

  } catch (error) {
    results.errors.push({
      step: 'setup_error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
    return results;
  }
};

/**
 * Generate comprehensive PWA investigation report
 */
export const generatePWAInvestigationReport = async () => {
  console.log('[PWA Investigation] Starting comprehensive investigation...');
  
  const report = {
    timestamp: new Date().toISOString(),
    environment: detectPWAEnvironment(),
    serviceWorkerTest: await testServiceWorkerCommunication(),
    syncBehaviorTest: await compareSyncBehavior(),
    recommendations: []
  };

  // Analyze results and generate recommendations
  if (!report.environment.isPWAMode) {
    report.recommendations.push({
      type: 'warning',
      message: 'Investigation running in browser mode, not PWA mode. Install as PWA to test PWA-specific behavior.'
    });
  }

  if (report.serviceWorkerTest.error) {
    report.recommendations.push({
      type: 'error',
      message: `Service Worker communication failed: ${report.serviceWorkerTest.error}`
    });
  }

  if (report.syncBehaviorTest.errors.length > 0) {
    report.recommendations.push({
      type: 'error',
      message: `Sync behavior test failed: ${report.syncBehaviorTest.errors.map(e => e.message).join(', ')}`
    });
  }

  if (report.syncBehaviorTest.referenceDataTest && !report.syncBehaviorTest.referenceDataTest.shouldSkipReferenceData) {
    report.recommendations.push({
      type: 'warning',
      message: 'Reference data skip logic may not be working correctly for Driver Connect PWA'
    });
  }

  console.log('[PWA Investigation] Investigation complete:', report);
  return report;
};

// Export for global access in debug tools
if (typeof window !== 'undefined') {
  window.pwaInvestigation = {
    detectPWAEnvironment,
    testServiceWorkerCommunication,
    compareSyncBehavior,
    monitorSyncStatusChanges,
    testOfflineToOnlineTransition,
    generatePWAInvestigationReport
  };
}
