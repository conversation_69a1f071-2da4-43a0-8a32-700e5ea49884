#!/usr/bin/env node

// Custom start script to suppress source map warnings
const { spawn } = require('child_process');
const path = require('path');

// Set environment variables to suppress warnings
process.env.GENERATE_SOURCEMAP = 'false';
process.env.DISABLE_ESLINT_PLUGIN = 'true';
process.env.ESLINT_NO_DEV_ERRORS = 'true';
process.env.HOST = '0.0.0.0';
process.env.SKIP_PREFLIGHT_CHECK = 'true';
process.env.TSC_COMPILE_ON_ERROR = 'true';

// Suppress console warnings related to source maps
const originalConsoleWarn = console.warn;
console.warn = function(...args) {
  const message = args.join(' ');
  
  // Filter out source map warnings
  if (message.includes('Failed to parse source map') ||
      message.includes('@yudiel/react-qr-scanner') ||
      message.includes('ENOENT') ||
      message.includes('source-map-loader')) {
    return; // Suppress these warnings
  }
  
  // Allow other warnings through
  originalConsoleWarn.apply(console, args);
};

// Start the React development server
const reactScripts = path.resolve(__dirname, 'node_modules', '.bin', 'react-scripts');
const child = spawn('node', [reactScripts, 'start'], {
  stdio: 'inherit',
  env: process.env
});

child.on('close', (code) => {
  process.exit(code);
});

child.on('error', (err) => {
  console.error('Failed to start development server:', err);
  process.exit(1);
});
