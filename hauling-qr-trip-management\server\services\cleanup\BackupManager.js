const fs = require('fs').promises;
const path = require('path');

class BackupManager {
    constructor() {
        this.backupDir = path.join(__dirname, '../../backups/cleanup');
    }

    async createBackup(directories = ['server', 'scripts']) {
        try {
            await fs.mkdir(this.backupDir, { recursive: true });
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = path.join(this.backupDir, `backup-${timestamp}`);

            for (const directory of directories) {
                const srcPath = path.join(__dirname, '../../', directory);
                const destPath = path.join(backupPath, directory);
                
                try {
                    await fs.access(srcPath);
                    await this._copyDirectory(srcPath, destPath);
                } catch (error) {
                    console.warn(`Warning: Could not backup directory ${directory}: ${error.message}`);
                }
            }

            return backupPath;
        } catch (error) {
            throw new Error(`Backup creation failed: ${error.message}`);
        }
    }

    async _copyDirectory(src, dest) {
        await fs.mkdir(dest, { recursive: true });
        const entries = await fs.readdir(src, { withFileTypes: true });

        const copyPromises = entries.map(async (entry) => {
            const srcPath = path.join(src, entry.name);
            const destPath = path.join(dest, entry.name);

            if (entry.isDirectory() && entry.name !== 'node_modules') {
                return this._copyDirectory(srcPath, destPath);
            } else if (entry.isFile()) {
                return fs.copyFile(srcPath, destPath);
            }
        });

        await Promise.all(copyPromises);
    }

    async rollbackFromBackup(backupPath, directories = ['server', 'scripts']) {
        try {
            const backupExists = await fs.access(backupPath).then(() => true).catch(() => false);
            if (!backupExists) {
                throw new Error(`Backup not found: ${backupPath}`);
            }

            const rollbackPromises = directories.map(async (directory) => {
                const srcPath = path.join(backupPath, directory);
                const destPath = path.join(__dirname, '../../', directory);
                
                try {
                    await fs.access(srcPath);
                    await this._copyDirectory(srcPath, destPath);
                } catch (error) {
                    console.warn(`Warning: Could not restore directory ${directory}: ${error.message}`);
                }
            });

            await Promise.all(rollbackPromises);

            return {
                success: true,
                timestamp: new Date().toISOString(),
                restoredDirectories: directories
            };
        } catch (error) {
            return {
                success: false,
                timestamp: new Date().toISOString(),
                error: error.message
            };
        }
    }

    async listBackups() {
        try {
            const entries = await fs.readdir(this.backupDir, { withFileTypes: true });
            const backups = entries
                .filter(entry => entry.isDirectory() && entry.name.startsWith('backup-'))
                .map(entry => ({
                    name: entry.name,
                    path: path.join(this.backupDir, entry.name),
                    timestamp: entry.name.replace('backup-', '').replace(/-/g, ':')
                }))
                .sort((a, b) => b.timestamp.localeCompare(a.timestamp));

            return backups;
        } catch (error) {
            console.warn(`Warning: Could not list backups: ${error.message}`);
            return [];
        }
    }
}

module.exports = BackupManager;