#!/bin/bash

# Production Fix Script for Hauling QR Trip System
# Fixes NGINX configuration and PM2 environment variables

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/hauling-qr-system"
PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "🚨 PRODUCTION FIX: Hauling QR Trip System"
echo "🌐 Domain: $PRODUCTION_DOMAIN"
echo "📅 $(date)"
echo

# Fix 1: NGINX Configuration
log_info "🔧 Fix 1: Regenerating NGINX configuration with proper domain..."

# Detect VPS IP
DETECTED_VPS_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "127.0.0.1")

# Create corrected NGINX configuration
sudo tee /etc/nginx/sites-available/hauling-qr-system >/dev/null <<EOF
# ENHANCED: Backend upstream with health checks and failover
upstream hauling_backend {
    server localhost:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

server {
    listen 80;
    server_name ${PRODUCTION_DOMAIN} www.${PRODUCTION_DOMAIN} api.${PRODUCTION_DOMAIN} ${DETECTED_VPS_IP};

    # Real IP from Cloudflare
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: https://api.${PRODUCTION_DOMAIN} wss://api.${PRODUCTION_DOMAIN} *.cloudflare.com *.cloudflareinsights.com;" always;

    # Static frontend
    root /var/www/hauling-qr-system/client/build;
    index index.html;

    # Static assets with CORS
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${PRODUCTION_DOMAIN}";
    }

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Images static path
    location /images/ {
        alias /var/www/hauling-qr-system/client/public/images/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${PRODUCTION_DOMAIN}";
        try_files \$uri =404;
    }

    # API with CORS
    location /api {
        # Strip upstream CORS headers
        proxy_hide_header Access-Control-Allow-Origin;
        proxy_hide_header Access-Control-Allow-Credentials;
        proxy_hide_header Access-Control-Allow-Methods;
        proxy_hide_header Access-Control-Allow-Headers;

        # Handle preflight OPTIONS
        if (\$request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://${PRODUCTION_DOMAIN}' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }

        # CORS headers for actual requests
        add_header 'Access-Control-Allow-Origin' 'https://${PRODUCTION_DOMAIN}' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;

        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 30s;
    }

    # WebSocket support
    location /ws {
        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
    }
}
EOF

# Test and reload NGINX
log_info "🧪 Testing NGINX configuration..."
if sudo nginx -t; then
    log_success "✅ NGINX configuration is valid"
    sudo systemctl reload nginx
    log_success "✅ NGINX reloaded successfully"
else
    log_error "❌ NGINX configuration test failed"
    exit 1
fi

# Fix 2: PM2 Environment Variables
log_info "🔧 Fix 2: Updating PM2 environment variables..."

cd "$APP_DIR"

# Regenerate ecosystem.config.js with correct CORS settings
cat >ecosystem.config.js <<EOF
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: 'server/server.js',
    instances: 1,
    exec_mode: 'cluster',
    env_production: {
      NODE_ENV: 'production',
      PORT: 8080,
      
      // CRITICAL: CORS Configuration - NGINX handles CORS
      NGINX_PROXY_MODE: 'true',
      EXPRESS_CORS_DISABLED: 'true',
      CORS_HANDLED_BY_NGINX: 'true',
      
      // Dynamic Domain Configuration
      PRODUCTION_DOMAIN: '${PRODUCTION_DOMAIN}',
      API_BASE_URL: 'https://api.${PRODUCTION_DOMAIN}',
      FRONTEND_URL: 'https://${PRODUCTION_DOMAIN}',
      CLIENT_URL: 'https://${PRODUCTION_DOMAIN}',
      
      // Database Configuration
      DB_HOST: 'localhost',
      DB_PORT: 5432,
      DB_NAME: 'hauling_qr_system',
      DB_USER: 'postgres'
    }
  }]
};
EOF

# Restart PM2 with new configuration
log_info "🔄 Restarting PM2 with updated configuration..."
pm2 delete hauling-qr-server 2>/dev/null || true
pm2 start ecosystem.config.js --env production
sleep 5

# Verify PM2 environment variables
log_info "🔍 Verifying PM2 environment variables..."
if pm2 show hauling-qr-server | grep -q "NGINX_PROXY_MODE.*true"; then
    log_success "✅ NGINX_PROXY_MODE=true confirmed in PM2"
else
    log_error "❌ NGINX_PROXY_MODE still not set correctly"
fi

# Final verification
log_info "🧪 Final verification..."
echo "NGINX Status:"
sudo systemctl status nginx --no-pager -l | head -5

echo
echo "PM2 Status:"
pm2 status

echo
echo "Backend Health Check:"
if curl -f -s http://localhost:8080/api/health >/dev/null; then
    log_success "✅ Backend is responding"
else
    log_error "❌ Backend health check failed"
fi

echo
log_success "🎉 Production fixes completed!"
log_info "🌐 Test your site: https://${PRODUCTION_DOMAIN}/"
