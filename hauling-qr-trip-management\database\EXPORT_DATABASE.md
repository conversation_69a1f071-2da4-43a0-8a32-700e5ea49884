# Comprehensive Database Export Guide for Windows

## Overview
This guide provides multiple methods to export your PostgreSQL database as a fresh `init.sql` file for deployment.

## Method 1: Using pg_dump (Recommended)

### Step 1: Find PostgreSQL Installation
```cmd
# Check if PostgreSQL is in PATH
where pg_dump

# Common PostgreSQL locations on Windows:
# C:\Program Files\PostgreSQL\15\bin\pg_dump.exe
# C:\Program Files\PostgreSQL\14\bin\pg_dump.exe
# C:\Program Files\PostgreSQL\13\bin\pg_dump.exe
```

### Step 2: Export Database Schema
```cmd
# Navigate to your project directory
cd C:\Users\<USER>\Documents\Hauling-QR-Trip-System

# Export schema only (clean init.sql)
"C:\Program Files\PostgreSQL\15\bin\pg_dump.exe" -h localhost -p 5432 -U postgres -d hauling_qr_system --schema-only --no-owner --no-privileges --clean --if-exists > database\init-exported.sql

# Export with sample data
"C:\Program Files\PostgreSQL\15\bin\pg_dump.exe" -h localhost -p 5432 -U postgres -d hauling_qr_system --clean --if-exists --no-owner --no-privileges > database\init-with-data.sql
```

## Method 2: Using psql Interactive Export

### Create export script:
1. Open Command Prompt as Administrator
2. Run:
```cmd
"C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d hauling_qr_system -f - > database\init-exported.sql
```

### Then enter these commands:
```sql
\o database\init-exported.sql
\i database\export-schema.sql
\o
```

## Method 3: PowerShell Export Script

### Create export script:
Save as `export-db.ps1` in your project root:

```powershell
# PostgreSQL Export Script for Windows
param(
    [string]$DatabaseName = "hauling_qr_system",
    [string]$UserName = "postgres",
    [string]$HostName = "localhost",
    [string]$Port = "5432",
    [string]$OutputFile = "database\init-exported.sql"
)

# Find PostgreSQL installation
$pgPaths = @(
    "C:\Program Files\PostgreSQL\15\bin\pg_dump.exe",
    "C:\Program Files\PostgreSQL\14\bin\pg_dump.exe",
    "C:\Program Files\PostgreSQL\13\bin\pg_dump.exe",
    "C:\Program Files\PostgreSQL\12\bin\pg_dump.exe"
)

$pgDump = $pgPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $pgDump) {
    Write-Error "PostgreSQL not found. Please install PostgreSQL or add to PATH."
    exit 1
}

Write-Host "Using pg_dump from: $pgDump"

# Export database
& $pgDump -h $HostName -p $Port -U $UserName -d $DatabaseName `
    --schema-only `
    --no-owner `
    --no-privileges `
    --clean `
    --if-exists `
    > $OutputFile

Write-Host "Database exported to: $OutputFile"
```

### Run the script:
```powershell
.\export-db.ps1
```

## Method 4: Manual SQL Export

### Create comprehensive export:
Save as `database\export-manual.sql`:

```sql
-- Database Export Script
-- Run this in psql to generate init.sql

\o database\init-exported.sql

-- Header
SELECT '-- ============================================================================' AS line;
SELECT '-- QR Code-based Hauling Truck Trip Management System' AS line;
SELECT '-- Database Export - ' || CURRENT_TIMESTAMP AS line;
SELECT '-- ============================================================================' AS line;
SELECT '' AS line;

-- Drop existing objects
SELECT '-- Drop existing objects' AS line;
SELECT 'DROP SCHEMA IF EXISTS public CASCADE;' AS line;
SELECT 'CREATE SCHEMA public;' AS line;
SELECT '' AS line;

-- Extensions
SELECT '-- Extensions' AS line;
SELECT 'CREATE EXTENSION IF NOT EXISTS pg_trgm;' AS line;
SELECT '' AS line;

-- Export all tables, functions, and views
\dt
\df
\dv

-- Export actual schema
\i database\init.sql

-- Footer
SELECT '-- Export completed successfully' AS line;

\o
```

## Method 5: Docker Export (if using Docker)

```cmd
# Export from Docker container
docker exec -i postgres-container pg_dump -U postgres -d hauling_qr_system --schema-only --no-owner --no-privileges --clean --if-exists > database\init-exported.sql
```

## Verification Commands

### Check export:
```cmd
# Verify file was created
dir database\init*.sql

# Check file size
powershell -Command "(Get-Item 'database\init-exported.sql').Length / 1KB"

# Preview first 20 lines
powershell -Command "Get-Content 'database\init-exported.sql' -First 20"
```

## Quick Start Commands

### One-liner for most common setup:
```cmd
# Replace with your PostgreSQL version
"C:\Program Files\PostgreSQL\15\bin\pg_dump.exe" -h localhost -p 5432 -U postgres -d hauling_qr_system --schema-only --no-owner --no-privileges --clean --if-exists > database\init-exported.sql
```

### If PostgreSQL is in PATH:
```cmd
pg_dump -h localhost -p 5432 -U postgres -d hauling_qr_system --schema-only --no-owner --no-privileges --clean --if-exists > database\init-exported.sql
```

## Troubleshooting

### PostgreSQL not found:
1. Check installation: `dir "C:\Program Files\PostgreSQL"`
2. Add to PATH: `set PATH=%PATH%;C:\Program Files\PostgreSQL\15\bin`
3. Use full path as shown above

### Permission issues:
1. Run Command Prompt as Administrator
2. Ensure PostgreSQL service is running: `net start postgresql-x64-15`

### Database connection issues:
1. Check service status: `sc query postgresql-x64-15`
2. Verify database exists: `psql -U postgres -l`
3. Check connection: `psql -U postgres -d hauling_qr_system -c "\dt"`

## Usage
After export, use the generated `init-exported.sql` file for fresh deployments:

```cmd
psql -U postgres -d new_database_name -f database\init-exported.sql