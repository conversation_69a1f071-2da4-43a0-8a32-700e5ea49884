#!/bin/bash

# =============================================================================
# Hauling QR System - Post-Reboot Check Script
# =============================================================================
# This script checks and fixes common issues after VPS reboot
# Run this as ubuntu user after reboot if services aren't working
#
# Usage: ./post-reboot-check.sh
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/hauling-qr-system"
UBUNTU_USER="ubuntu"

echo -e "${BLUE}🔍 Hauling QR System - Post-Reboot Check${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as ubuntu user
if [[ "$USER" != "$UBUNTU_USER" ]]; then
   print_error "This script should be run as ubuntu user, not root"
   echo "Usage: ./post-reboot-check.sh"
   exit 1
fi

echo -e "${YELLOW}🔍 Step 1: System Status Check${NC}"

# Check system services
print_info "Checking system services..."
if systemctl is-active --quiet nginx; then
    print_status "Nginx is running"
else
    print_warning "Nginx is not running"
    echo "   Fix: sudo systemctl start nginx"
fi

if systemctl is-active --quiet postgresql; then
    print_status "PostgreSQL is running"
else
    print_warning "PostgreSQL is not running"
    echo "   Fix: sudo systemctl start postgresql"
fi

echo ""
echo -e "${YELLOW}🔍 Step 2: PM2 Status Check${NC}"

# Check PM2 status
print_info "Checking PM2 processes..."
PM2_STATUS=$(pm2 jlist 2>/dev/null | jq -r '.[0].pm2_env.status' 2>/dev/null || echo "not_found")

if [[ "$PM2_STATUS" == "online" ]]; then
    print_status "PM2 application is running"
    pm2 status
elif [[ "$PM2_STATUS" == "stopped" ]] || [[ "$PM2_STATUS" == "errored" ]]; then
    print_warning "PM2 application is $PM2_STATUS"
    echo "   Attempting to restart..."
    cd $APP_DIR
    pm2 restart hauling-qr-server || pm2 start ecosystem.config.js
else
    print_warning "PM2 application not found"
    echo "   Attempting to start..."
    cd $APP_DIR
    pm2 start ecosystem.config.js
fi

echo ""
echo -e "${YELLOW}🔍 Step 3: Process Ownership Check${NC}"

# Check if any root processes are running
ROOT_PROCESSES=$(ps aux | grep "node.*server" | grep "^root" | grep -v grep || true)
if [[ -n "$ROOT_PROCESSES" ]]; then
    print_error "Found Node.js processes running as root:"
    echo "$ROOT_PROCESSES"
    echo ""
    print_info "To fix this, run: sudo ./fix-permissions-ubuntu-user.sh"
else
    print_status "No root Node.js processes found"
fi

# Check current process owner
CURRENT_PROCESS=$(ps aux | grep "node.*server" | grep -v grep | head -1)
if [[ -n "$CURRENT_PROCESS" ]]; then
    PROCESS_USER=$(echo "$CURRENT_PROCESS" | awk '{print $1}')
    if [[ "$PROCESS_USER" == "$UBUNTU_USER" ]]; then
        print_status "Node.js process is running as ubuntu user"
    else
        print_warning "Node.js process is running as: $PROCESS_USER"
    fi
else
    print_warning "No Node.js processes found"
fi

echo ""
echo -e "${YELLOW}🔍 Step 4: Application Health Check${NC}"

# Test application endpoints
print_info "Testing application health..."
sleep 2

if curl -f http://localhost:8080/health >/dev/null 2>&1; then
    print_status "API health check passed"
else
    print_error "API health check failed"
    echo "   The application may not be running properly"
fi

# Check if port 8080 is in use (Cloudflare compatible)
PORT_CHECK=$(netstat -tlnp 2>/dev/null | grep ":8080 " || true)
if [[ -n "$PORT_CHECK" ]]; then
    print_status "Port 8080 is in use"
    echo "   $PORT_CHECK"
else
    print_warning "Port 8080 is not in use"
fi

echo ""
echo -e "${YELLOW}🔍 Step 5: File Permissions Check${NC}"

# Check application directory ownership
APP_OWNER=$(stat -c '%U' $APP_DIR 2>/dev/null || echo "unknown")
if [[ "$APP_OWNER" == "$UBUNTU_USER" ]]; then
    print_status "Application directory owned by ubuntu user"
else
    print_warning "Application directory owned by: $APP_OWNER"
    echo "   Fix: sudo chown -R ubuntu:ubuntu $APP_DIR"
fi

# Check PM2 directory ownership
PM2_DIR="/home/<USER>/.pm2"
if [[ -d "$PM2_DIR" ]]; then
    PM2_OWNER=$(stat -c '%U' $PM2_DIR)
    if [[ "$PM2_OWNER" == "$UBUNTU_USER" ]]; then
        print_status "PM2 directory owned by ubuntu user"
    else
        print_warning "PM2 directory owned by: $PM2_OWNER"
        echo "   Fix: sudo chown -R ubuntu:ubuntu $PM2_DIR"
    fi
else
    print_warning "PM2 directory not found"
fi

echo ""
echo -e "${YELLOW}🔧 Step 6: Quick Fixes${NC}"

# Offer quick fixes for common issues
if [[ "$PM2_STATUS" != "online" ]] || [[ -n "$ROOT_PROCESSES" ]]; then
    print_info "Detected issues that can be auto-fixed"
    echo ""
    echo -e "${BLUE}Available fixes:${NC}"
    echo -e "   1. ${YELLOW}sudo ./fix-permissions-ubuntu-user.sh${NC} - Complete permission fix"
    echo -e "   2. ${YELLOW}pm2 restart hauling-qr-server${NC} - Restart PM2 application"
    echo -e "   3. ${YELLOW}pm2 resurrect${NC} - Restore saved PM2 processes"
    echo ""
fi

echo ""
echo -e "${BLUE}📋 Current Status Summary:${NC}"
echo -e "   • System Services: $(systemctl is-active nginx) (nginx), $(systemctl is-active postgresql) (postgresql)"
echo -e "   • PM2 Status: $PM2_STATUS"
echo -e "   • Process Owner: ${PROCESS_USER:-none}"
echo -e "   • App Directory Owner: $APP_OWNER"
echo -e "   • Port 8080: $(if [[ -n "$PORT_CHECK" ]]; then echo "in use"; else echo "free"; fi)"
echo ""

# Final recommendations
if [[ "$PM2_STATUS" == "online" ]] && [[ "$PROCESS_USER" == "$UBUNTU_USER" ]] && [[ "$APP_OWNER" == "$UBUNTU_USER" ]]; then
    echo -e "${GREEN}🎉 Everything looks good! Your application should be working properly.${NC}"
else
    echo -e "${YELLOW}⚠️  Some issues detected. Consider running the fix script:${NC}"
    echo -e "   ${YELLOW}sudo ./fix-permissions-ubuntu-user.sh${NC}"
fi

echo ""
echo -e "${BLUE}📝 Useful commands:${NC}"
echo -e "   • Check PM2: ${YELLOW}pm2 status${NC}"
echo -e "   • View logs: ${YELLOW}pm2 logs hauling-qr-server${NC}"
echo -e "   • Monitor: ${YELLOW}pm2 monit${NC}"
echo -e "   • Test API: ${YELLOW}curl http://localhost:8080/health${NC}"
echo -e "   • Test website: ${YELLOW}curl -I https://truckhaul.top${NC}"