# Requirements Document

## Introduction

The deployment script in the `deploy-hauling-qr-ubuntu` directory needs to be updated to reflect the current codebase structure and dependencies. The current deployment script contains outdated package dependencies, incorrect startup commands, and missing configuration elements that don't align with the actual project structure.hauling-qr-ubuntu` directory needs to be updated to reflect the current codebase structure and dependencies. The current deployment script contains outdated package dependencies, incorrect startup commands, and missing configuration elements that don't align with the actual project structure.

## Requirements

### Requirement 1

**User Story:** As a system administrator, I want the deployment script to install the correct Node.js dependencies, so that the application can run properly after deployment.

#### Acceptance Criteria

1. WHEN the deployment script installs dependencies THEN it SHALL use the exact package.json files from the current codebase
2. WHEN installing server dependencies THEN the script SHALL include all packages from server/package.json including winston, joi, multer, sharp, uuid, and other current dependencies
3. WHEN installing client dependencies THEN the script SHALL include all packages from client/package.json including @yudiel/react-qr-scanner, chart.js, react-chartjs-2, and other current dependencies
4. WHEN installing root dependencies THEN the script SHALL include packages from the root package.json including winston-daily-rotate-file and ws

### Requirement 2

**User Story:** As a system administrator, I want the deployment script to use the correct startup commands, so that the application starts with the proper configuration.

#### Acceptance Criteria

1. WHEN starting the server THEN the script SHALL use "node server.js" from the server directory
2. WHEN building the client THEN the script SHALL use "npm run build" from the client directory
3. WHEN the deployment script creates PM2 configuration THEN it SHALL point to the correct server.js file location
4. WHEN the deployment script sets up startup scripts THEN it SHALL use the scripts from the scripts/ directory (start-dev.js, start-prod.js)

### Requirement 3

**User Story:** As a system administrator, I want the deployment script to create the correct database schema, so that the application can access all required tables and functions.

#### Acceptance Criteria

1. WHEN initializing the database THEN the script SHALL use the current database/init.sql file
2. WHEN running database migrations THEN the script SHALL use the database/run-migration.js script
3. WHEN creating the admin user THEN the script SHALL use the server/create-admin-user.js script
4. WHEN setting up database permissions THEN the script SHALL create the hauling_app user with correct privileges

### Requirement 4

**User Story:** As a system administrator, I want the deployment script to configure the environment variables correctly, so that the application can connect to services and operate properly.

#### Acceptance Criteria

1. WHEN creating the .env file THEN the script SHALL include all variables from the current .env template
2. WHEN setting database configuration THEN the script SHALL use the correct database name "hauling_qr_system"
3. WHEN configuring JWT settings THEN the script SHALL set appropriate JWT_SECRET values for production
4. WHEN setting up CORS THEN the script SHALL configure ALLOWED_ORIGINS appropriately for the deployment domain
5. WHEN configuring SSL THEN the script SHALL set ENABLE_HTTPS based on the SSL_MODE setting

### Requirement 5

**User Story:** As a system administrator, I want the deployment script to set up the correct directory structure, so that static files and uploads are served properly.

#### Acceptance Criteria

1. WHEN creating the application directory THEN the script SHALL create uploads/ directory for file uploads
2. WHEN setting up static files THEN the script SHALL ensure client/public/images/ directory exists for logo and assets
3. WHEN configuring nginx THEN the script SHALL serve static files from the correct client/build directory
4. WHEN setting up logs THEN the script SHALL create server/logs directory for application logs

### Requirement 6

**User Story:** As a system administrator, I want the deployment script to handle the unified configuration system, so that both client and server use consistent settings.

#### Acceptance Criteria

1. WHEN the deployment completes THEN the script SHALL run the config-loader.js to generate client environment variables
2. WHEN setting up the application THEN the script SHALL ensure the unified configuration system is properly initialized
3. WHEN configuring IP detection THEN the script SHALL set AUTO_DETECT_IP appropriately for the deployment environment
4. WHEN setting up SSL certificates THEN the script SHALL create the ssl/dev and ssl/production directories as needed

### Requirement 7

**User Story:** As a system administrator, I want the deployment script to include current monitoring and logging features, so that the application can be properly monitored in production.

#### Acceptance Criteria

1. WHEN setting up monitoring THEN the script SHALL configure winston logging with daily rotation
2. WHEN enabling health checks THEN the script SHALL ensure /api/health and /api/health/db endpoints are accessible
3. WHEN configuring performance monitoring THEN the script SHALL set appropriate LOG_LEVEL and monitoring intervals
4. WHEN setting up WebSocket THEN the script SHALL ensure WebSocket server is properly configured for real-time updates