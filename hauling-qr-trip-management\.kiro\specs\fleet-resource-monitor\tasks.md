# Implementation Plan

- [x] 1. Set up backend API foundation for fleet resource monitoring



  - Create new route file `server/routes/fleet-resources.js` with basic Express router setup
  - Add authentication middleware and basic error handling structure
  - Create placeholder endpoints for summary, loading locations, and utilization history
  - Update `server/server.js` to include the new fleet-resources routes
  - _Requirements: 1, 2, 3.1_




- [x] 2. Implement core database queries for resource counting

  - [x] 2.1 Create driver resource counting query

    - Create helper file `server/utils/FleetResourceQueries.js` for database query functions
    - Write SQL query to count total drivers from `drivers` table where status = 'active'
    - Write SQL query to count drivers assigned to active shifts in `driver_shifts` table
    - Calculate unassigned drivers (total - assigned to active shifts)

    - Create helper function to get unassigned driver details with names and employee IDs
    - _Requirements: 1_

  - [x] 2.2 Create truck resource counting query

    - Add to `server/utils/FleetResourceQueries.js` file
    - Write SQL query to count total trucks from `dump_trucks` table where status = 'active'



    - Write SQL query to count trucks assigned to active shifts in `driver_shifts` table
    - Calculate unassigned trucks (total - assigned to active shifts)
    - Create helper function to get unassigned truck details with truck numbers
    - _Requirements: 2_

- [x] 3. Implement loading location breakdown functionality

  - [x] 3.1 Create loading location assignment query

    - Add to `server/utils/FleetResourceQueries.js` file
    - Write SQL query to group trucks by loading location from active shifts and assignments
    - Join `driver_shifts`, `assignments`, `dump_trucks`, `drivers`, and `locations` tables
    - Return truck numbers, driver names, and location information

    - _Requirements: 2.1_

  - [x] 3.2 Add truck route status tracking

    - Add to `server/utils/FleetResourceQueries.js` file
    - Write SQL query to identify trucks currently on routes from `trip_logs` table
    - Join with assignments to get loading and unloading location information
    - Include trip start time and basic route information
    - _Requirements: 2.1, 2.2_



- [x] 4. Create fleet resource summary API endpoint

  - Implement `GET /api/fleet-resources/summary` endpoint in `server/routes/fleet-resources.js`
  - Import and use query functions from `server/utils/FleetResourceQueries.js`
  - Combine driver and truck counting queries into single response
  - Add basic alert logic for resource shortages and underutilization


  - Include error handling and input validation
  - Test endpoint with Postman or curl
  - _Requirements: 1, 2, 4_

- [x] 5. Create loading location breakdown API endpoint

  - Implement `GET /api/fleet-resources/loading-locations` endpoint in `server/routes/fleet-resources.js`

  - Use loading location queries from `server/utils/FleetResourceQueries.js`
  - Include on-route status for trucks currently in transit
  - Add pagination support for large datasets
  - Test endpoint functionality
  - _Requirements: 2.1, 2.2_

- [x] 6. Set up frontend page structure and routing


  - Create directory `client/src/pages/fleet/` if it doesn't exist
  - Create new page component `client/src/pages/fleet/FleetResourceMonitor.js`
  - Update `client/src/App.js` to add route configuration for `/fleet-resources` path
  - Create basic page layout with header and main content area
  - Update navigation component (likely `client/src/components/Navigation.js` or similar) to add Fleet Resource Monitor menu item
  - _Requirements: 1, 2, 6_

- [x] 7. Implement resource summary cards component


  - [x] 7.1 Create ResourceSummaryCards component

    - Create directory `client/src/components/fleet/` if it doesn't exist
    - Create component file `client/src/components/fleet/ResourceSummaryCards.js`
    - Build card layout showing driver totals, assigned, and unassigned counts
    - Build card layout showing truck totals, assigned, and unassigned counts
    - Add visual indicators for resource levels (good/warning states)
    - Implement responsive design for mobile devices
    - _Requirements: 1, 2, 6_

  - [x] 7.2 Add on-route trucks summary card

    - Add to `client/src/components/fleet/ResourceSummaryCards.js`
    - Create card showing count of trucks currently on routes
    - Add basic route information display
    - Include loading/unloading location summary
    - _Requirements: 2.1, 2.2_

- [x] 8. Create loading location breakdown component


  - Create component file `client/src/components/fleet/LoadingLocationBreakdown.js`
  - Build component with expandable sections for each loading location
  - Display truck count and truck numbers for each loading location
  - Show truck status (at location vs. on route) with visual indicators
  - Add search/filter functionality for specific locations
  - Make component touch-friendly for mobile use
  - _Requirements: 2.1, 6_

- [ ] 9. Implement detailed resource tables
  - [x] 9.1 Create unassigned drivers table


    - Create component file `client/src/components/fleet/ResourceDetailTables.js`
    - Build table component showing driver names, employee IDs, and availability status
    - Add sorting functionality by name or employee ID
    - Include last shift date information if available
    - _Requirements: 1, 6_

  - [ ] 9.2 Create unassigned trucks table
    - Add to `client/src/components/fleet/ResourceDetailTables.js`
    - Build table component showing truck numbers and basic truck information
    - Add sorting functionality by truck number
    - Include last maintenance or assignment date if available
    - _Requirements: 2, 6_

  - [ ] 9.3 Create trucks on route table
    - Add to `client/src/components/fleet/ResourceDetailTables.js`
    - Build table showing truck number, loading location, unloading location, and trip start time
    - Add sorting by trip start time or location
    - Include basic trip progress information
    - _Requirements: 2.1, 2.2, 6_

- [ ] 10. Add real-time WebSocket integration
  - [ ] 10.1 Set up WebSocket connection in main component
    - Add WebSocket connection logic to `client/src/pages/fleet/FleetResourceMonitor.js`
    - Establish WebSocket connection on component mount
    - Handle connection errors and reconnection logic
    - Add cleanup on component unmount
    - _Requirements: 1, 2_

  - [ ] 10.2 Implement real-time data updates
    - Update `client/src/pages/fleet/FleetResourceMonitor.js` with WebSocket event handlers
    - Listen for shift status changes and update resource counts
    - Listen for trip status changes and update on-route information
    - Update UI components when data changes without full page refresh
    - Add loading states during data updates
    - _Requirements: 1, 2, 2.1_

- [ ] 11. Add historical utilization trends component
  - Create component file `client/src/components/fleet/HistoricalTrends.js`
  - Implement API endpoint `GET /api/fleet-resources/utilization-history` in `server/routes/fleet-resources.js`
  - Add historical data query functions to `server/utils/FleetResourceQueries.js`
  - Build line charts showing driver and truck utilization patterns using Chart.js
  - Add date range selection for historical data
  - _Requirements: 5_

- [ ] 12. Implement export functionality
  - Add export button to `client/src/pages/fleet/FleetResourceMonitor.js`
  - Create API endpoint `GET /api/fleet-resources/export` in `server/routes/fleet-resources.js`
  - Create export utility functions in `server/utils/FleetResourceExport.js`
  - Include current resource status, assignments, and utilization data
  - Add timestamp and user information to exported reports
  - _Requirements: 7_

- [ ] 13. Add mobile responsiveness and touch optimization
  - Update all components in `client/src/components/fleet/` for mobile responsiveness
  - Update `client/src/pages/fleet/FleetResourceMonitor.js` for mobile layout
  - Ensure all components work properly on mobile devices (320px to 768px)
  - Implement touch-friendly controls with minimum 44px touch targets
  - Add swipe gestures for table navigation if needed
  - Test on actual mobile devices for usability
  - _Requirements: 6_

- [ ] 14. Implement error handling and offline support
  - Create error boundary component `client/src/components/fleet/FleetResourceErrorBoundary.js`
  - Update `client/src/pages/fleet/FleetResourceMonitor.js` with error handling
  - Implement graceful degradation when API calls fail
  - Add offline indicators when WebSocket connection is lost
  - Create fallback UI states for missing data
  - _Requirements: 6_

- [ ] 15. Add comprehensive testing
  - [ ] 15.1 Write unit tests for API endpoints
    - Create test file `server/tests/fleet-resources.test.js`
    - Test resource counting logic with mock database data
    - Test loading location breakdown functionality
    - Test error handling scenarios
    - _Requirements: All_

  - [ ] 15.2 Write component tests for frontend
    - Create test file `client/src/tests/FleetResourceMonitor.test.js`
    - Create test file `client/src/tests/components/fleet/ResourceSummaryCards.test.js`
    - Test resource summary cards rendering with mock data
    - Test loading location breakdown component functionality
    - Test WebSocket integration and real-time updates
    - _Requirements: 6_

- [ ] 16. Final integration and testing
  - Test complete workflow from database to UI
  - Verify real-time updates work correctly across all components
  - Test mobile responsiveness on various screen sizes
  - Perform load testing with realistic data volumes
  - Verify all requirements are met and functioning properly
  - _Requirements: All_