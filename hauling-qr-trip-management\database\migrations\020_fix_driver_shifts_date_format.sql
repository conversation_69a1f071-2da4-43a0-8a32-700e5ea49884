-- Migration 020: Fix Driver Shifts Date Format Issues
-- Fixes malformed date/time values in driver_shifts table that cause "Invalid time value" errors

BEGIN;

-- Function to safely convert malformed dates to proper format
CREATE OR REPLACE FUNCTION fix_malformed_date(input_date TEXT)
RETURNS DATE AS $fix_date$
BEGIN
    -- Handle NULL values
    IF input_date IS NULL THEN
        RETURN CURRENT_DATE;
    END IF;
    
    -- Try to parse as date directly first
    BEGIN
        RETURN input_date::DATE;
    EXCEPTION WHEN OTHERS THEN
        -- If direct conversion fails, try parsing as timestamp and extract date
        BEGIN
            RETURN (input_date::TIMESTAMP)::DATE;
        EXCEPTION WHEN OTHERS THEN
            -- If all parsing fails, return current date as fallback
            RETURN CURRENT_DATE;
        END;
    END;
END;
$fix_date$ LANGUAGE plpgsql;

-- Function to safely convert malformed times to proper format
CREATE OR REPLACE FUNCTION fix_malformed_time(input_time TEXT, default_time TIME DEFAULT '00:00:00')
R<PERSON>URNS TIME AS $fix_time$
BEGIN
    -- Handle NULL values
    IF input_time IS NULL THEN
        RETURN default_time;
    END IF;
    
    -- Try to parse as time directly
    BEGIN
        RETURN input_time::TIME;
    EXCEPTION WHEN OTHERS THEN
        -- If parsing fails, return default time
        RETURN default_time;
    END;
END;
$fix_time$ LANGUAGE plpgsql;

-- Create a backup table for safety
CREATE TABLE IF NOT EXISTS driver_shifts_backup_020 AS 
SELECT * FROM driver_shifts WHERE status = 'active';

-- Log the number of records that will be processed
DO $$
DECLARE
    active_count INTEGER;
    problematic_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO active_count FROM driver_shifts WHERE status = 'active';
    
    SELECT COUNT(*) INTO problematic_count 
    FROM driver_shifts 
    WHERE status = 'active' 
      AND (start_date IS NULL OR start_time IS NULL OR end_date IS NULL OR end_time IS NULL);
    
    RAISE NOTICE 'Migration 020: Processing % active shifts, % have NULL date/time values', 
                 active_count, problematic_count;
END $$;

-- Fix malformed date and time values in driver_shifts table (batch processing)
DO $$
DECLARE
    batch_size INTEGER := 1000;
    processed_count INTEGER := 0;
    total_count INTEGER;
    current_batch INTEGER;
BEGIN
    -- Get total count for progress tracking
    SELECT COUNT(*) INTO total_count
    FROM driver_shifts 
    WHERE status = 'active'
      AND (
        start_date IS NULL OR 
        start_time IS NULL OR 
        end_date IS NULL OR 
        end_time IS NULL OR
        start_date::TEXT LIKE '%GMT%' OR
        end_date::TEXT LIKE '%GMT%'
      );
    
    RAISE NOTICE 'Migration 020: Processing % records in batches of %', total_count, batch_size;
    
    -- Process in batches to avoid locking issues
    LOOP
        UPDATE driver_shifts 
        SET 
            start_date = fix_malformed_date(start_date::TEXT),
            end_date = fix_malformed_date(COALESCE(end_date::TEXT, start_date::TEXT)),
            start_time = fix_malformed_time(start_time::TEXT, '00:00:00'),
            end_time = fix_malformed_time(end_time::TEXT, '23:59:59'),
            updated_at = CURRENT_TIMESTAMP
        WHERE id IN (
            SELECT id FROM driver_shifts 
            WHERE status = 'active'
              AND (
                start_date IS NULL OR 
                start_time IS NULL OR 
                end_date IS NULL OR 
                end_time IS NULL OR
                start_date::TEXT LIKE '%GMT%' OR
                end_date::TEXT LIKE '%GMT%'
              )
            LIMIT batch_size
        );
        
        GET DIAGNOSTICS current_batch = ROW_COUNT;
        processed_count := processed_count + current_batch;
        
        RAISE NOTICE 'Migration 020: Processed % of % records (%.1f%%)', 
                     processed_count, total_count, 
                     (processed_count::FLOAT / GREATEST(total_count, 1) * 100);
        
        -- Exit when no more records to process
        EXIT WHEN current_batch = 0;
        
        -- Small delay to prevent overwhelming the database
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RAISE NOTICE 'Migration 020: Completed processing % records', processed_count;
END $$;

-- Verify the fix worked by checking for any remaining NULL values
DO $$
DECLARE
    remaining_nulls INTEGER;
BEGIN
    SELECT COUNT(*) INTO remaining_nulls 
    FROM driver_shifts 
    WHERE status = 'active' 
      AND (start_date IS NULL OR start_time IS NULL OR end_date IS NULL OR end_time IS NULL);
    
    IF remaining_nulls > 0 THEN
        RAISE WARNING 'Migration 020: % active shifts still have NULL date/time values after fix', remaining_nulls;
    ELSE
        RAISE NOTICE 'Migration 020: All active shifts now have valid date/time values';
    END IF;
END $$;

-- Validate data before adding constraints
DO $$
DECLARE
    null_start_dates INTEGER;
    null_start_times INTEGER;
BEGIN
    SELECT COUNT(*) INTO null_start_dates FROM driver_shifts WHERE start_date IS NULL;
    SELECT COUNT(*) INTO null_start_times FROM driver_shifts WHERE start_time IS NULL;
    
    IF null_start_dates > 0 THEN
        RAISE EXCEPTION 'Cannot add NOT NULL constraint: % records have NULL start_date', null_start_dates;
    END IF;
    
    IF null_start_times > 0 THEN
        RAISE EXCEPTION 'Cannot add NOT NULL constraint: % records have NULL start_time', null_start_times;
    END IF;
    
    RAISE NOTICE 'Migration 020: Data validation passed, adding NOT NULL constraints';
END $$;

-- Add constraints to prevent future NULL values in critical fields
ALTER TABLE driver_shifts 
ALTER COLUMN start_date SET NOT NULL,
ALTER COLUMN start_time SET NOT NULL;

-- Add a check constraint to ensure valid date/time combinations
ALTER TABLE driver_shifts 
ADD CONSTRAINT check_valid_datetime_combination 
CHECK (
    -- Ensure we can create a valid timestamp from start_date and start_time
    (start_date + start_time) IS NOT NULL
);

-- Create an index to improve performance of date/time queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_datetime_combo 
ON driver_shifts (start_date, start_time) 
WHERE status = 'active';

-- Add comments for documentation
COMMENT ON CONSTRAINT check_valid_datetime_combination ON driver_shifts 
IS 'Ensures start_date and start_time can be combined into a valid timestamp';

COMMENT ON INDEX idx_driver_shifts_datetime_combo 
IS 'Improves performance of date/time queries for active shifts';

-- Clean up helper functions (they're only needed for this migration)
DO $$
BEGIN
    DROP FUNCTION IF EXISTS fix_malformed_date(TEXT);
    DROP FUNCTION IF EXISTS fix_malformed_time(TEXT, TIME);
    RAISE NOTICE 'Migration 020: Cleaned up temporary functions';
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Migration 020: Could not clean up functions: %', SQLERRM;
END $$;

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 020 completed: Fixed driver_shifts date/time format issues';
END $$;

COMMIT;