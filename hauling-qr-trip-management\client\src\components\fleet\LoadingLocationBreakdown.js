import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { getApiBaseUrl } from '../../utils/network-utils';

const LoadingLocationBreakdown = () => {
  const [locationData, setLocationData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedLocations, setExpandedLocations] = useState(new Set());

  // Fetch loading location data
  const fetchLocationData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('hauling_token');
      const apiUrl = getApiBaseUrl();
      
      const response = await axios.get(`${apiUrl}/fleet-resources/loading-locations`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        setLocationData(response.data.data.locations || []);
        setError(null);
      } else {
        setError('Failed to fetch location data');
      }
    } catch (err) {
      console.error('Error fetching location data:', err);
      setError(err.response?.data?.error?.message || 'Failed to fetch location data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLocationData();
  }, []);

  // Toggle location expansion
  const toggleLocation = (locationId) => {
    const newExpanded = new Set(expandedLocations);
    if (newExpanded.has(locationId)) {
      newExpanded.delete(locationId);
    } else {
      newExpanded.add(locationId);
    }
    setExpandedLocations(newExpanded);
  };

  // Filter locations based on search term
  const filteredLocations = locationData.filter(location =>
    location.location_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get status color for truck status
  const getStatusColor = (status) => {
    switch (status) {
      case 'at_location':
        return 'bg-green-100 text-green-800';
      case 'on_route':
        return 'bg-yellow-100 text-yellow-800';
      case 'not_operating':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status icon for truck status
  const getStatusIcon = (status) => {
    switch (status) {
      case 'at_location':
        return '📍';
      case 'on_route':
        return '🛣️';
      case 'not_operating':
        return '⏸️';
      default:
        return '❓';
    }
  };

  // Get status text for truck status
  const getStatusText = (status) => {
    switch (status) {
      case 'at_location':
        return 'At Location';
      case 'on_route':
        return 'On Route';
      case 'not_operating':
        return 'Not Operating';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 rounded mb-4 w-1/3"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="text-center">
          <div className="text-red-500 text-4xl mb-2">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Locations</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button 
            onClick={fetchLocationData}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Loading Location Breakdown
          </h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {filteredLocations.length} location{filteredLocations.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400">🔍</span>
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Search locations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Location List */}
        <div className="space-y-3">
          {filteredLocations.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-2">📍</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? 'No Matching Locations' : 'No Loading Locations'}
              </h3>
              <p className="text-gray-500">
                {searchTerm 
                  ? `No locations match "${searchTerm}"`
                  : 'No trucks are currently assigned to loading locations.'
                }
              </p>
            </div>
          ) : (
            filteredLocations.map((location) => (
              <div key={location.location_id} className="border border-gray-200 rounded-lg">
                {/* Location Header */}
                <div 
                  className="px-4 py-3 cursor-pointer hover:bg-gray-50 transition-colors duration-150"
                  onClick={() => toggleLocation(location.location_id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm">📍</span>
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-900">
                          {location.location_name}
                        </h4>
                        <p className="text-sm text-gray-500">
                          {location.total_assigned} truck{location.total_assigned !== 1 ? 's' : ''} assigned
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      {/* Status Summary */}
                      <div className="flex items-center space-x-2 text-sm">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {location.currently_at_location} at location
                        </span>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          {location.on_route} on route
                        </span>
                      </div>
                      {/* Expand/Collapse Icon */}
                      <div className="text-gray-400">
                        {expandedLocations.has(location.location_id) ? '▼' : '▶'}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Expanded Truck Details */}
                {expandedLocations.has(location.location_id) && (
                  <div className="border-t border-gray-200 bg-gray-50">
                    <div className="px-4 py-3">
                      {location.assigned_trucks.length === 0 ? (
                        <p className="text-gray-500 text-sm">No trucks assigned to this location.</p>
                      ) : (
                        <div className="space-y-2">
                          {location.assigned_trucks.map((truck, index) => (
                            <div key={index} className="flex items-center justify-between py-2 px-3 bg-white rounded border">
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                  <div className="w-6 h-6 bg-gray-500 rounded flex items-center justify-center">
                                    <span className="text-white text-xs">🚛</span>
                                  </div>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">
                                    {truck.truck_number}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    Driver: {truck.driver_name || 'Not assigned'}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(truck.status)}`}>
                                  {getStatusIcon(truck.status)} {getStatusText(truck.status)}
                                </span>
                                {truck.current_trip && (
                                  <div className="text-xs text-gray-500">
                                    → {truck.current_trip.unloading_location}
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* Summary Footer */}
        {filteredLocations.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>
                Total: {filteredLocations.reduce((sum, loc) => sum + loc.total_assigned, 0)} trucks across {filteredLocations.length} locations
              </span>
              <button
                onClick={fetchLocationData}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Refresh
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadingLocationBreakdown;