import React, { useState, useEffect } from 'react';
import appearanceService from '../../services/appearanceService';

const Footer = () => {
  const [logoSettings, setLogoSettings] = useState(null);

  // Load logo settings on component mount
  useEffect(() => {
    const settings = appearanceService.getLogoSettings();
    setLogoSettings(settings);

    // Listen for appearance settings changes
    const handleStorageChange = (e) => {
      if (e.key === 'hauling_appearance_settings') {
        const newSettings = appearanceService.getLogoSettings();
        setLogoSettings(newSettings);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return (
    <footer className="bg-white border-t border-secondary-200 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
          {/* Logo and Company Info */}
          <div className="flex items-center space-x-3">
            {/* Custom Logo or Default Icon */}
            {logoSettings?.src ? (
              <img
                src={logoSettings.src}
                alt={logoSettings.alt}
                className="app-logo object-contain"
                style={{
                  width: `${Math.min(logoSettings.width, 32)}px`,
                  height: `${Math.min(logoSettings.height, 32)}px`
                }}
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'inline';
                }}
              />
            ) : null}
            <span className={`text-xl ${logoSettings?.src ? 'hidden' : ''}`}>🚛</span>
            
            <div>
              <p className="text-sm font-medium text-secondary-900 font-custom-footer">
                Hauling QR Trip Management System
              </p>
              <p className="text-xs text-secondary-500 font-custom-footer">
                Powered by AI Technology
              </p>
            </div>
          </div>

          {/* Links */}
          <div className="flex items-center space-x-6">
            <a
              href="/settings"
              className="text-sm text-secondary-600 hover:text-secondary-900 font-custom-footer transition-colors"
            >
              Settings
            </a>
            <a
              href="/analytics"
              className="text-sm text-secondary-600 hover:text-secondary-900 font-custom-footer transition-colors"
            >
              Analytics
            </a>
            <button
              type="button"
              className="text-sm text-secondary-600 hover:text-secondary-900 font-custom-footer transition-colors bg-transparent border-none cursor-pointer p-0"
              onClick={() => {
                // TODO: Implement help & support functionality
                console.log('Help & Support clicked');
              }}
            >
              Help & Support
            </button>
          </div>

          {/* Copyright */}
          <div className="text-center md:text-right">
            <p className="text-xs text-secondary-500 font-custom-footer">
              © {new Date().getFullYear()} Hauling QR System. All rights reserved.
            </p>
            <p className="text-xs text-secondary-400 font-custom-footer">
              Developer: Ariez-AI
            </p>
          </div>
        </div>

        {/* System Status Indicator */}
        <div className="mt-4 pt-4 border-t border-secondary-100">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-secondary-600 font-custom-footer">
                System Online
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-xs text-secondary-600 font-custom-footer">
                Real-time Monitoring Active
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-xs text-secondary-600 font-custom-footer">
                QR Scanner Ready
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;