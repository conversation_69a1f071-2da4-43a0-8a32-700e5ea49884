import React from 'react';

const RouteCard = ({ truck }) => {
  const getPhaseIcon = (phase) => {
    switch (phase) {
      case 'loading_start': return '⬆️';
      case 'loading_end': return '🚛';
      case 'unloading_start': return '⬇️';
      case 'unloading_end': return '🔄';
      case 'stopped': return '⏹️';
      default: return '⭕';
    }
  };

  const getPhaseColor = (phase, alertStatus) => {
    if (alertStatus === 'stopped') return 'border-red-500 bg-red-50';
    if (alertStatus === 'overdue') return 'border-yellow-500 bg-yellow-50';
    
    switch (phase) {
      case 'loading_start':
      case 'loading_end':
        return 'border-blue-500 bg-blue-50';
      case 'unloading_start':
      case 'unloading_end':
        return 'border-green-500 bg-green-50';
      default:
        return 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <div className={`rounded-lg border-2 p-4 ${getPhaseColor(truck.currentPhase, truck.alertStatus)}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <span className="text-2xl mr-2">{getPhaseIcon(truck.currentPhase)}</span>
          <div>
            <div className="font-medium text-secondary-900">
              {truck.truckNumber}
            </div>
            <div className="text-sm text-secondary-600">
              {truck.driverName}
            </div>
          </div>
        </div>
        
        {truck.isDynamicAssignment && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            📍 Dynamic
          </span>
        )}
      </div>
      
      <div className="space-y-2">
        <div className="text-sm">
          <span className="text-secondary-500">Current:</span>
          <span className="ml-2 text-secondary-900">{truck.currentLocationDisplay}</span>
        </div>
        
        {truck.routeDisplay && truck.routeDisplay !== 'Unknown → Unknown' && (
          <div className="text-sm">
            <span className="text-secondary-500">Route:</span>
            <span className="ml-2 text-secondary-900">{truck.routeDisplay}</span>
          </div>
        )}
        
        <div className="text-sm">
          <span className="text-secondary-500">Phase Time:</span>
          <span className="ml-2 text-secondary-900">
            {truck.timeInPhaseMinutes < 60 ? 
              `${Math.round(truck.timeInPhaseMinutes)}m` : 
              `${Math.floor(truck.timeInPhaseMinutes / 60)}h ${Math.round(truck.timeInPhaseMinutes % 60)}m`
            }
          </span>
        </div>
        
        {truck.estimatedMinutesRemaining && (
          <div className="text-sm">
            <span className="text-secondary-500">ETA:</span>
            <span className="ml-2 text-secondary-900">{truck.estimatedMinutesRemaining}m</span>
          </div>
        )}
        
        {truck.alertStatus !== 'normal' && (
          <div className="mt-2 pt-2 border-t border-current border-opacity-20">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              truck.alertStatus === 'stopped' ? 'bg-red-100 text-red-800' :
              truck.alertStatus === 'overdue' ? 'bg-yellow-100 text-yellow-800' :
              'bg-orange-100 text-orange-800'
            }`}>
              {truck.alertStatus === 'stopped' && '⏹️ Stopped'}
              {truck.alertStatus === 'overdue' && '⚠️ Overdue'}
              {truck.alertStatus === 'exception' && '⚡ Exception'}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

const PhaseDistributionChart = ({ summary }) => {
  if (!summary || !summary.byPhase) {
    return null;
  }

  const phases = [
    { key: 'loading', label: '⬆️ Loading', color: 'bg-blue-500' },
    { key: 'travelingToUnload', label: '🚛 To Unload', color: 'bg-yellow-500' },
    { key: 'unloading', label: '⬇️ Unloading', color: 'bg-green-500' },
    { key: 'travelingToLoad', label: '🔄 To Load', color: 'bg-purple-500' },
    { key: 'stopped', label: '⏹️ Stopped', color: 'bg-red-500' },
    { key: 'idle', label: '⭕ Idle', color: 'bg-gray-500' }
  ];

  const total = Object.values(summary.byPhase).reduce((sum, count) => sum + count, 0);

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
      <h3 className="text-lg font-medium text-secondary-900 mb-4">
        Fleet Phase Distribution
      </h3>
      
      <div className="space-y-3">
        {phases.map(phase => {
          const count = summary.byPhase[phase.key] || 0;
          const percentage = total > 0 ? (count / total) * 100 : 0;
          
          return (
            <div key={phase.key} className="flex items-center">
              <div className="w-24 text-sm text-secondary-600">
                {phase.label}
              </div>
              <div className="flex-1 mx-4">
                <div className="w-full bg-secondary-200 rounded-full h-4">
                  <div 
                    className={`h-4 rounded-full ${phase.color}`}
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
              </div>
              <div className="w-16 text-right">
                <div className="text-sm font-medium text-secondary-900">{count}</div>
                <div className="text-xs text-secondary-500">{percentage.toFixed(0)}%</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const RouteVisualization = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse bg-secondary-200 h-48 rounded-lg"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse bg-secondary-200 h-32 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!data || !data.operations || data.operations.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-8 text-center">
        <span className="text-4xl block mb-2">🗺️</span>
        <p className="text-secondary-500">No active routes to visualize</p>
      </div>
    );
  }

  // Filter active operations (not idle)
  const activeOperations = data.operations.filter(truck => 
    truck.currentPhase && truck.currentPhase !== 'idle'
  );

  return (
    <div className="space-y-6">
      {/* Phase Distribution Chart */}
      <PhaseDistributionChart summary={data.summary} />
      
      {/* Active Routes Grid */}
      <div className="bg-white rounded-lg shadow border border-secondary-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-secondary-900">
            Active Routes ({activeOperations.length})
          </h3>
          <div className="text-sm text-secondary-500">
            Last updated: {new Date(data.lastUpdated).toLocaleTimeString()}
          </div>
        </div>
        
        {activeOperations.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {activeOperations.map((truck, index) => (
              <RouteCard key={truck.truckId || `route-${index}`} truck={truck} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-secondary-500">
            <span className="text-4xl block mb-2">🚛</span>
            <p>No trucks currently in active operations</p>
          </div>
        )}
      </div>
      
      {/* Dynamic Assignment Summary */}
      {data.summary?.dynamicAssignments > 0 && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-purple-600 text-xl mr-3">📍</span>
            <div>
              <h4 className="text-sm font-medium text-purple-800">
                Dynamic Route Discovery Active
              </h4>
              <p className="text-sm text-purple-700">
                {data.summary.dynamicAssignments} trucks are currently using dynamic route discovery
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RouteVisualization;
