-- Migration 018: Driver QR Code System
-- Adds driver QR code functionality and optimizes driver_shifts table for QR-based operations

-- Add driver_qr_code JSONB field to drivers table
ALTER TABLE drivers 
ADD COLUMN IF NOT EXISTS driver_qr_code JSONB;

-- Add GIN index for fast QR code lookups on drivers table
CREATE INDEX IF NOT EXISTS idx_drivers_qr_code_gin 
ON drivers USING gin (driver_qr_code) 
WHERE (driver_qr_code IS NOT NULL);

-- Add composite index for driver_shifts queries by driver_id and status
CREATE INDEX IF NOT EXISTS idx_driver_shifts_driver_status 
ON driver_shifts (driver_id, status);

-- Add unique constraint to prevent multiple active shifts per driver
-- This ensures only one active shift per driver at any time
ALTER TABLE driver_shifts 
DROP CONSTRAINT IF EXISTS unique_active_driver_shift;

ALTER TABLE driver_shifts 
ADD CONSTRAINT unique_active_driver_shift 
EXCLUDE (driver_id WITH =) 
WHERE (status = 'active');

-- Add index for truck_id and status combination for handover scenarios
CREATE INDEX IF NOT EXISTS idx_driver_shifts_truck_status_active
ON driver_shifts (truck_id, status)
WHERE status = 'active';

-- Add index for efficient date-based queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_start_date_status
ON driver_shifts (start_date, status);

-- PERFORMANCE OPTIMIZATION: Additional indexes for common query patterns
-- Index for attendance reporting queries (date range + driver)
CREATE INDEX IF NOT EXISTS idx_driver_shifts_attendance_lookup
ON driver_shifts (driver_id, start_date, status)
WHERE status IN ('active', 'completed');

-- Index for truck assignment queries (truck + date + status)
CREATE INDEX IF NOT EXISTS idx_driver_shifts_truck_date_status
ON driver_shifts (truck_id, start_date, status);

-- Index for auto-created shift queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_auto_created
ON driver_shifts (auto_created, created_at)
WHERE auto_created = true;

-- Partial index for active shifts only (most frequently queried)
CREATE INDEX IF NOT EXISTS idx_driver_shifts_active_only
ON driver_shifts (truck_id, driver_id, created_at)
WHERE status = 'active';

-- Index for shift duration calculations
CREATE INDEX IF NOT EXISTS idx_driver_shifts_duration_calc
ON driver_shifts (start_date, start_time, end_date, end_time)
WHERE status = 'completed' AND end_date IS NOT NULL AND end_time IS NOT NULL;

-- Function to validate driver QR code structure
CREATE OR REPLACE FUNCTION validate_driver_qr_code(qr_data JSONB)
RETURNS BOOLEAN AS $$
DECLARE
    required_fields TEXT[] := ARRAY['id', 'driver_id', 'employee_id', 'generated_date'];
    field TEXT;
BEGIN
    -- Check if qr_data is not null
    IF qr_data IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check if all required fields exist
    FOREACH field IN ARRAY required_fields
    LOOP
        IF NOT (qr_data ? field) THEN
            RETURN FALSE;
        END IF;
    END LOOP;
    
    -- Validate field types and values
    IF NOT (
        (qr_data->>'driver_id')::TEXT ~ '^\d+$' AND
        (qr_data->>'employee_id')::TEXT != '' AND
        (qr_data->>'id')::TEXT != '' AND
        (qr_data->>'generated_date')::TEXT ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}'
    ) THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get active driver shift for a truck
CREATE OR REPLACE FUNCTION get_active_driver_shift_for_truck(p_truck_id INTEGER)
RETURNS TABLE(
    shift_id INTEGER,
    driver_id INTEGER,
    employee_id VARCHAR,
    full_name VARCHAR,
    start_date DATE,
    start_time TIME
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.id as shift_id,
        ds.driver_id,
        d.employee_id,
        d.full_name,
        ds.start_date,
        ds.start_time
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id 
      AND ds.status = 'active'
      AND d.status = 'active'
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to end active shift and create new shift (for handovers)
CREATE OR REPLACE FUNCTION handover_driver_shift(
    p_truck_id INTEGER,
    p_new_driver_id INTEGER,
    p_handover_notes TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    v_old_shift_id INTEGER;
    v_new_shift_id INTEGER;
    v_current_timestamp TIMESTAMP := CURRENT_TIMESTAMP;
    v_current_date DATE := CURRENT_DATE;
    v_current_time TIME := CURRENT_TIME;
BEGIN
    -- End any existing active shift for this truck
    UPDATE driver_shifts 
    SET 
        status = 'completed',
        end_date = v_current_date,
        end_time = v_current_time,
        handover_notes = COALESCE(p_handover_notes, 'Automatic handover via QR system'),
        handover_completed_at = v_current_timestamp,
        updated_at = v_current_timestamp
    WHERE truck_id = p_truck_id 
      AND status = 'active'
    RETURNING id INTO v_old_shift_id;
    
    -- Create new active shift for the new driver
    INSERT INTO driver_shifts (
        truck_id,
        driver_id,
        shift_type,
        start_date,
        end_date,
        start_time,
        end_time,
        status,
        previous_shift_id,
        auto_created,
        created_at,
        updated_at
    ) VALUES (
        p_truck_id,
        p_new_driver_id,
        'custom',
        v_current_date,
        v_current_date,
        v_current_time,
        '23:59:59'::TIME, -- Default end time, will be updated on checkout
        'active',
        v_old_shift_id,
        true,
        v_current_timestamp,
        v_current_timestamp
    ) RETURNING id INTO v_new_shift_id;
    
    -- Log the handover event
    PERFORM log_system_event(
        'DRIVER_HANDOVER',
        'Driver shift handover completed via QR system',
        jsonb_build_object(
            'truck_id', p_truck_id,
            'old_shift_id', v_old_shift_id,
            'new_shift_id', v_new_shift_id,
            'new_driver_id', p_new_driver_id,
            'handover_time', v_current_timestamp
        )
    );
    
    RETURN v_new_shift_id;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON COLUMN drivers.driver_qr_code IS 'JSONB field storing driver QR code data with structure: {id, driver_id, employee_id, generated_date}';
COMMENT ON INDEX idx_drivers_qr_code_gin IS 'GIN index for fast QR code lookups on drivers table';
COMMENT ON INDEX idx_driver_shifts_driver_status IS 'Composite index for efficient driver shift queries by driver and status';
COMMENT ON CONSTRAINT unique_active_driver_shift ON driver_shifts IS 'Ensures only one active shift per driver at any time';
COMMENT ON FUNCTION validate_driver_qr_code(JSONB) IS 'Validates the structure and content of driver QR code data';
COMMENT ON FUNCTION get_active_driver_shift_for_truck(INTEGER) IS 'Returns active driver shift information for a specific truck';
COMMENT ON FUNCTION handover_driver_shift(INTEGER, INTEGER, TEXT) IS 'Handles automatic driver shift handovers when scanning different trucks';

-- Add role permissions for driver QR system pages
INSERT INTO role_permissions (role_name, page_key, has_access) VALUES
('admin', 'driver_connect', true),
('admin', 'driver_attendance', true),
('supervisor', 'driver_connect', true),
('supervisor', 'driver_attendance', true),
('operator', 'driver_connect', false),
('operator', 'driver_attendance', false)
ON CONFLICT (role_name, page_key) DO NOTHING;