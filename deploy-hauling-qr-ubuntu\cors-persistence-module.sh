#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - CORS PERSISTENCE MODULE
# =============================================================================
# Version: 1.0.0 - Standalone CORS persistence and validation module
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# Description: Comprehensive CORS configuration persistence across VPS reboots
# Usage: ./cors-persistence-module.sh [install|validate|fix|status]
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================
readonly SCRIPT_NAME="cors-persistence-module.sh"
readonly LOG_DIR="/var/log/hauling-qr-deployment"
readonly LOG_FILE="${LOG_DIR}/cors-persistence-$(date +%Y%m%d-%H%M%S).log"

# Application Configuration
readonly APP_DIR="/var/www/hauling-qr-system"
readonly PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"
readonly SERVER_HTTP_PORT=8080

# CORS Persistence Scripts
readonly CORS_FIX_SCRIPT="/usr/local/bin/hauling-qr-cors-fix.sh"
readonly PM2_ENV_FIX_SCRIPT="/usr/local/bin/hauling-qr-pm2-env-fix.sh"

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Create log directory with proper permissions
sudo mkdir -p "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"
sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true

ts() { date '+%Y-%m-%d %H:%M:%S'; }
log() { echo "[$(ts)] $*" | tee -a "$LOG_FILE"; }

log_info() {
  echo -e "${BLUE}[$(ts)] INFO  | $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
  echo -e "${GREEN}[$(ts)] OK    | $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
  echo -e "${YELLOW}[$(ts)] WARN  | $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
  echo -e "${RED}[$(ts)] ERROR | $1${NC}" | tee -a "$LOG_FILE"
}

log_debug() {
  echo -e "${CYAN}[$(ts)] DEBUG | $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# CORS VALIDATION FUNCTIONS
# =============================================================================
validate_nginx_cors_config() {
  log_info "🔍 Validating NGINX CORS configuration..."

  # Check if NGINX configuration file exists
  local nginx_config="/etc/nginx/sites-available/hauling-qr-system"
  if [[ ! -f "$nginx_config" ]]; then
    log_error "❌ NGINX configuration file not found: $nginx_config"
    return 1
  fi

  # Check for CORS headers in configuration
  local cors_checks=(
    "Access-Control-Allow-Origin"
    "Access-Control-Allow-Credentials"
    "proxy_hide_header.*Access-Control-Allow-Origin"
  )

  local missing_cors=()
  for check in "${cors_checks[@]}"; do
    if ! grep -q "$check" "$nginx_config"; then
      missing_cors+=("$check")
    fi
  done

  if [[ ${#missing_cors[@]} -eq 0 ]]; then
    log_success "✅ NGINX CORS configuration is complete"
    return 0
  else
    log_warning "⚠️ Missing CORS configuration: ${missing_cors[*]}"
    return 1
  fi
}

validate_pm2_cors_environment() {
  log_info "🔍 Validating PM2 CORS environment variables..."

  # Check if PM2 application is running
  if ! sudo -u ubuntu bash -c "cd '$APP_DIR' && pm2 list" | grep -q "hauling-qr-server.*online"; then
    log_warning "⚠️ PM2 application is not running"
    return 1
  fi

  # Check environment variables
  local env_output
  env_output=$(sudo -u ubuntu bash -c "cd '$APP_DIR' && pm2 show hauling-qr-server" 2>/dev/null || echo "")

  local required_vars=(
    "NGINX_PROXY_MODE.*true"
    "EXPRESS_CORS_DISABLED.*true"
    "CORS_HANDLED_BY_NGINX.*true"
  )

  local missing_vars=()
  for var in "${required_vars[@]}"; do
    if ! echo "$env_output" | grep -q "$var"; then
      missing_vars+=("$var")
    fi
  done

  if [[ ${#missing_vars[@]} -eq 0 ]]; then
    log_success "✅ PM2 CORS environment variables are correctly configured"
    return 0
  else
    log_warning "⚠️ Missing PM2 environment variables: ${missing_vars[*]}"
    return 1
  fi
}

test_cors_functionality() {
  log_info "🧪 Testing CORS functionality..."

  local test_url="http://localhost:8080/api/health"
  local cors_origin="https://${PRODUCTION_DOMAIN}"

  # Test preflight OPTIONS request
  local options_response
  options_response=$(curl -s -X OPTIONS \
    -H "Origin: $cors_origin" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type" \
    -w "%{http_code}" \
    "$test_url" 2>/dev/null || echo "000")

  if [[ "$options_response" =~ 204$ ]]; then
    log_success "✅ CORS preflight request successful"
  else
    log_warning "⚠️ CORS preflight request failed (HTTP: ${options_response: -3})"
    return 1
  fi

  # Test actual request with CORS headers
  local cors_response
  cors_response=$(curl -s -H "Origin: $cors_origin" \
    -w "%{http_code}" \
    "$test_url" 2>/dev/null || echo "000")

  if [[ "$cors_response" =~ 200$ ]]; then
    log_success "✅ CORS actual request successful"
    return 0
  else
    log_warning "⚠️ CORS actual request failed (HTTP: ${cors_response: -3})"
    return 1
  fi
}

# =============================================================================
# CORS PERSISTENCE INSTALLATION
# =============================================================================
install_cors_persistence() {
  log_info "🔧 Installing CORS persistence system..."

  # Check if scripts already exist
  if [[ -f "$CORS_FIX_SCRIPT" ]] && [[ -f "$PM2_ENV_FIX_SCRIPT" ]]; then
    log_info "CORS persistence scripts already exist - validating..."
    
    if validate_cors_persistence_installation; then
      log_success "✅ CORS persistence system is already installed and functional"
      return 0
    else
      log_warning "⚠️ CORS persistence system needs reinstallation"
    fi
  fi

  # Install CORS fix script (already created by 5_install-nginx.sh)
  if [[ ! -f "$CORS_FIX_SCRIPT" ]]; then
    log_error "❌ CORS fix script not found: $CORS_FIX_SCRIPT"
    log_error "   This should have been created by 5_install-nginx.sh"
    return 1
  fi

  # Install PM2 environment fix script (already created by 6_install-pm2.sh)
  if [[ ! -f "$PM2_ENV_FIX_SCRIPT" ]]; then
    log_error "❌ PM2 environment fix script not found: $PM2_ENV_FIX_SCRIPT"
    log_error "   This should have been created by 6_install-pm2.sh"
    return 1
  fi

  # Verify systemd services are installed
  local services=(
    "hauling-qr-cors-fix.service"
    "hauling-qr-cors-fix.timer"
    "hauling-qr-pm2-env-fix.service"
    "hauling-qr-pm2-env-fix.timer"
  )

  local missing_services=()
  for service in "${services[@]}"; do
    if ! systemctl list-unit-files | grep -q "$service"; then
      missing_services+=("$service")
    fi
  done

  if [[ ${#missing_services[@]} -gt 0 ]]; then
    log_error "❌ Missing systemd services: ${missing_services[*]}"
    log_error "   These should have been created by the deployment scripts"
    return 1
  fi

  log_success "✅ CORS persistence system installation validated"
  return 0
}

validate_cors_persistence_installation() {
  log_info "🔍 Validating CORS persistence installation..."

  local validation_errors=0

  # Check scripts exist and are executable
  local scripts=("$CORS_FIX_SCRIPT" "$PM2_ENV_FIX_SCRIPT")
  for script in "${scripts[@]}"; do
    if [[ -f "$script" ]] && [[ -x "$script" ]]; then
      log_success "✅ Script exists and is executable: $script"
    else
      log_error "❌ Script missing or not executable: $script"
      ((validation_errors++))
    fi
  done

  # Check systemd services are enabled
  local services=(
    "hauling-qr-cors-fix.service"
    "hauling-qr-cors-fix.timer"
    "hauling-qr-pm2-env-fix.service"
    "hauling-qr-pm2-env-fix.timer"
  )

  for service in "${services[@]}"; do
    if systemctl is-enabled "$service" >/dev/null 2>&1; then
      log_success "✅ Service is enabled: $service"
    else
      log_error "❌ Service is not enabled: $service"
      ((validation_errors++))
    fi
  done

  if [[ $validation_errors -eq 0 ]]; then
    log_success "✅ CORS persistence installation validation passed"
    return 0
  else
    log_error "❌ CORS persistence installation validation failed ($validation_errors errors)"
    return 1
  fi
}

# =============================================================================
# CORS FIXING FUNCTIONS
# =============================================================================
fix_cors_configuration() {
  log_info "🔧 Fixing CORS configuration..."

  local fix_errors=0

  # Run NGINX CORS fix
  log_info "Running NGINX CORS fix..."
  if [[ -x "$CORS_FIX_SCRIPT" ]]; then
    if "$CORS_FIX_SCRIPT"; then
      log_success "✅ NGINX CORS fix completed successfully"
    else
      log_error "❌ NGINX CORS fix failed"
      ((fix_errors++))
    fi
  else
    log_error "❌ NGINX CORS fix script not found or not executable: $CORS_FIX_SCRIPT"
    ((fix_errors++))
  fi

  # Run PM2 environment fix
  log_info "Running PM2 environment fix..."
  if [[ -x "$PM2_ENV_FIX_SCRIPT" ]]; then
    if "$PM2_ENV_FIX_SCRIPT"; then
      log_success "✅ PM2 environment fix completed successfully"
    else
      log_error "❌ PM2 environment fix failed"
      ((fix_errors++))
    fi
  else
    log_error "❌ PM2 environment fix script not found or not executable: $PM2_ENV_FIX_SCRIPT"
    ((fix_errors++))
  fi

  if [[ $fix_errors -eq 0 ]]; then
    log_success "✅ CORS configuration fix completed successfully"
    return 0
  else
    log_error "❌ CORS configuration fix failed ($fix_errors errors)"
    return 1
  fi
}

# =============================================================================
# STATUS REPORTING
# =============================================================================
show_cors_status() {
  log_info "📊 CORS Persistence System Status Report"
  echo ""
  
  # NGINX CORS Status
  echo "🌐 NGINX CORS Configuration:"
  if validate_nginx_cors_config >/dev/null 2>&1; then
    echo "   ✅ NGINX CORS headers configured correctly"
  else
    echo "   ❌ NGINX CORS configuration issues detected"
  fi
  
  # PM2 Environment Status
  echo ""
  echo "🔄 PM2 Environment Variables:"
  if validate_pm2_cors_environment >/dev/null 2>&1; then
    echo "   ✅ PM2 CORS environment variables configured correctly"
  else
    echo "   ❌ PM2 environment variable issues detected"
  fi
  
  # CORS Functionality Status
  echo ""
  echo "🧪 CORS Functionality Test:"
  if test_cors_functionality >/dev/null 2>&1; then
    echo "   ✅ CORS functionality working correctly"
  else
    echo "   ❌ CORS functionality test failed"
  fi
  
  # Persistence System Status
  echo ""
  echo "🛡️ Persistence System:"
  if validate_cors_persistence_installation >/dev/null 2>&1; then
    echo "   ✅ CORS persistence system installed and functional"
  else
    echo "   ❌ CORS persistence system issues detected"
  fi
  
  # Service Status
  echo ""
  echo "🔧 Systemd Services:"
  local services=(
    "hauling-qr-cors-fix.service"
    "hauling-qr-cors-fix.timer"
    "hauling-qr-pm2-env-fix.service"
    "hauling-qr-pm2-env-fix.timer"
  )
  
  for service in "${services[@]}"; do
    if systemctl is-active "$service" >/dev/null 2>&1; then
      echo "   ✅ $service: Active"
    elif systemctl is-enabled "$service" >/dev/null 2>&1; then
      echo "   🟡 $service: Enabled (not active)"
    else
      echo "   ❌ $service: Disabled"
    fi
  done
  
  echo ""
}

# =============================================================================
# MAIN EXECUTION FUNCTIONS
# =============================================================================
show_help() {
  cat << EOF
Hauling QR Trip System - CORS Persistence Module v1.0.0

DESCRIPTION:
  Comprehensive CORS configuration persistence across VPS reboots.
  Ensures NGINX CORS headers and PM2 environment variables persist correctly.

USAGE:
  $0 [COMMAND]

COMMANDS:
  install     Install CORS persistence system (validate existing installation)
  validate    Validate CORS configuration and functionality
  fix         Fix CORS configuration issues
  status      Show comprehensive CORS system status
  help        Show this help message

EXAMPLES:
  $0 install      # Install/validate CORS persistence system
  $0 validate     # Check if CORS is working correctly
  $0 fix          # Fix any CORS configuration issues
  $0 status       # Show detailed status report

ENVIRONMENT VARIABLES:
  PRODUCTION_DOMAIN    Production domain (default: truckhaul.top)

FILES:
  $CORS_FIX_SCRIPT
  $PM2_ENV_FIX_SCRIPT

SYSTEMD SERVICES:
  hauling-qr-cors-fix.service
  hauling-qr-cors-fix.timer
  hauling-qr-pm2-env-fix.service
  hauling-qr-pm2-env-fix.timer

EOF
}

validate_cors_system() {
  log_info "🔍 Validating complete CORS system..."

  local validation_errors=0

  # Validate NGINX CORS configuration
  if validate_nginx_cors_config; then
    log_success "✅ NGINX CORS configuration validation passed"
  else
    log_error "❌ NGINX CORS configuration validation failed"
    ((validation_errors++))
  fi

  # Validate PM2 CORS environment
  if validate_pm2_cors_environment; then
    log_success "✅ PM2 CORS environment validation passed"
  else
    log_error "❌ PM2 CORS environment validation failed"
    ((validation_errors++))
  fi

  # Test CORS functionality
  if test_cors_functionality; then
    log_success "✅ CORS functionality test passed"
  else
    log_error "❌ CORS functionality test failed"
    ((validation_errors++))
  fi

  # Validate persistence installation
  if validate_cors_persistence_installation; then
    log_success "✅ CORS persistence installation validation passed"
  else
    log_error "❌ CORS persistence installation validation failed"
    ((validation_errors++))
  fi

  if [[ $validation_errors -eq 0 ]]; then
    log_success "🎉 Complete CORS system validation passed!"
    return 0
  else
    log_error "❌ CORS system validation failed ($validation_errors errors)"
    return 1
  fi
}

main() {
  local command="${1:-help}"

  log_info "🚀 Starting CORS Persistence Module"
  log_info "📝 Script: $SCRIPT_NAME"
  log_info "📄 Log file: $LOG_FILE"
  log_info "🌐 Production domain: $PRODUCTION_DOMAIN"
  log_info "🎯 Command: $command"

  case "$command" in
    "install")
      log_info "🔧 Installing CORS persistence system..."
      if install_cors_persistence; then
        log_success "✅ CORS persistence system installation completed"
        exit 0
      else
        log_error "❌ CORS persistence system installation failed"
        exit 1
      fi
      ;;
    "validate")
      log_info "🔍 Validating CORS system..."
      if validate_cors_system; then
        log_success "✅ CORS system validation completed successfully"
        exit 0
      else
        log_error "❌ CORS system validation failed"
        exit 1
      fi
      ;;
    "fix")
      log_info "🔧 Fixing CORS configuration..."
      if fix_cors_configuration; then
        log_success "✅ CORS configuration fix completed successfully"
        # Run validation after fix
        if validate_cors_system; then
          log_success "✅ CORS system is now working correctly"
          exit 0
        else
          log_warning "⚠️ CORS fix completed but validation still shows issues"
          exit 1
        fi
      else
        log_error "❌ CORS configuration fix failed"
        exit 1
      fi
      ;;
    "status")
      show_cors_status
      exit 0
      ;;
    "help"|"--help"|"-h")
      show_help
      exit 0
      ;;
    *)
      log_error "❌ Unknown command: $command"
      echo ""
      show_help
      exit 1
      ;;
  esac
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
