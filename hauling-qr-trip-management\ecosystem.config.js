/**
 * PM2 Ecosystem Configuration for Hauling QR Trip System
 * Optimized for production deployment with intelligent logging and resource management
 * 
 * Features:
 * - Cluster mode with optimal worker count
 * - Log rotation and management
 * - Memory and CPU optimization
 * - Health monitoring and auto-restart
 * - Environment-specific configurations
 */

module.exports = {
  apps: [{
    // Application Configuration
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: process.env.NODE_ENV === 'production' ? '/var/www/hauling-qr-system' : process.cwd(),
    
    // Cluster Configuration - Optimized for 4 vCPU VPS
    instances: process.env.NODE_ENV === 'production' ? 4 : 1,
    exec_mode: process.env.NODE_ENV === 'production' ? 'cluster' : 'fork',
    
    // Environment Variables
    env: {
      NODE_ENV: 'development',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false,

      // CRITICAL: CORS Configuration - NGINX Proxy Mode (prevents duplicate headers)
      // These variables disable Express.js CORS when behind NGINX proxy
      NGINX_PROXY_MODE: 'false',
      EXPRESS_CORS_DISABLED: 'false',
      CORS_HANDLED_BY_NGINX: 'false',

      // Dynamic Domain Configuration
      PRODUCTION_DOMAIN: process.env.PRODUCTION_DOMAIN || 'localhost',
      API_BASE_URL: process.env.API_BASE_URL || `http://localhost:8080`,
      FRONTEND_URL: process.env.FRONTEND_URL || `http://localhost:3000`,
      CLIENT_URL: process.env.CLIENT_URL || `http://localhost:3000`,

      // Database Configuration
      DB_HOST: 'localhost',
      DB_PORT: '5432',
      DB_NAME: 'hauling_qr_system',
      DB_USER: 'postgres',
      DB_PASSWORD: 'PostgreSQLPassword123',

      // JWT Configuration
      JWT_SECRET: 'hauling_qr_jwt_secret_2025_secure_key_for_development',

      // Intelligent Logging Configuration
      LOG_LEVEL: 'info',
      DEV_ENABLE_CORS_LOGGING: 'true',
      CORS_DEBUG_MODE: 'false',
      SUPPRESS_CORS_CONFIG_MESSAGES: 'false',

      // Monitoring Configuration
      MONITORING_DEDUPLICATION_ENABLED: 'true',
      MONITORING_DEDUPLICATION_WINDOW_MS: '300000', // 5 minutes
      MONITORING_MAX_DUPLICATES: '3',
      CORS_LOG_THROTTLE_MINUTES: '5'
    },

    // Production Environment
    env_production: {
      NODE_ENV: 'production',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false,

      // CRITICAL: CORS Configuration - NGINX Proxy Mode (prevents duplicate headers)
      // These variables disable Express.js CORS when behind NGINX proxy
      // FIXED: Static values to ensure persistence after reboot
      NGINX_PROXY_MODE: 'true',
      EXPRESS_CORS_DISABLED: 'true',
      CORS_HANDLED_BY_NGINX: 'true',

      // Dynamic Domain Configuration - Environment Variable Based
      PRODUCTION_DOMAIN: process.env.PRODUCTION_DOMAIN || 'truckhaul.top',
      API_BASE_URL: process.env.API_BASE_URL || `https://api.${process.env.PRODUCTION_DOMAIN || 'truckhaul.top'}`,
      FRONTEND_URL: process.env.FRONTEND_URL || `https://${process.env.PRODUCTION_DOMAIN || 'truckhaul.top'}`,
      CLIENT_URL: process.env.CLIENT_URL || `https://${process.env.PRODUCTION_DOMAIN || 'truckhaul.top'}`,

      // Database Configuration
      DB_HOST: 'localhost',
      DB_PORT: '5432',
      DB_NAME: 'hauling_qr_system',
      DB_USER: 'postgres',
      DB_PASSWORD: 'PostgreSQLPassword123',

      // JWT Configuration
      JWT_SECRET: 'hauling_qr_jwt_secret_2025_secure_key_for_production',

      // Production Logging Configuration - CRITICAL FOR LOG REDUCTION
      LOG_LEVEL: 'warn',
      LOG_LEVEL_PRODUCTION: 'warn',
      DEV_ENABLE_CORS_LOGGING: 'false',
      CORS_DEBUG_MODE: 'false',
      SUPPRESS_CORS_CONFIG_MESSAGES: 'true',
      SUPPRESS_CORS_REQUEST_LOGS: 'true',
      SUPPRESS_CORS_PRODUCTION_LOGS: 'true',

      // Production Monitoring Configuration
      MONITORING_DEDUPLICATION_ENABLED: 'true',
      MONITORING_DEDUPLICATION_WINDOW_MS: '600000', // 10 minutes in production
      MONITORING_MAX_DUPLICATES: '1', // Only allow 1 duplicate in production
      CORS_LOG_THROTTLE_MINUTES: '10', // Longer throttle in production

      // Performance Optimization
      SUPPRESS_DATABASE_QUERY_LOGS: 'true',
      SUPPRESS_SYNC_SUCCESS_MESSAGES: 'true',
      SUPPRESS_CLIENT_AUTH_MESSAGES: 'true',
      SUPPRESS_CONNECTION_MESSAGES: 'true',
      SUPPRESS_SHIFT_QUERY_DEBUG: 'true',
      SUPPRESS_SHIFT_COMPLETION_MESSAGES: 'true',
      SUPPRESS_DRIVER_SYNC_LOGS: 'true'
    },
    
    // Logging Configuration with Rotation
    log_file: process.env.NODE_ENV === 'production' 
      ? '/home/<USER>/.pm2/logs/hauling-qr-server.log'
      : './logs/hauling-qr-server.log',
    out_file: process.env.NODE_ENV === 'production'
      ? '/home/<USER>/.pm2/logs/hauling-qr-server-out.log'
      : './logs/hauling-qr-server-out.log',
    error_file: process.env.NODE_ENV === 'production'
      ? '/home/<USER>/.pm2/logs/hauling-qr-server-error.log'
      : './logs/hauling-qr-server-error.log',
    
    // Log Management
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // Log Rotation Configuration - CRITICAL FOR DISK SPACE MANAGEMENT
    max_memory_restart: '1500M', // Restart if memory exceeds 1.5GB
    
    // Performance Optimization
    node_args: [
      '--max-old-space-size=1024',  // Limit heap to 1GB
      '--optimize-for-size'         // Optimize for memory usage
    ],
    
    // Health Monitoring
    health_check_grace_period: 3000,
    
    // Auto-restart Configuration
    autorestart: true,
    watch: false, // Disable file watching in production
    max_restarts: 10,
    min_uptime: '10s',
    
    // Advanced Options
    source_map_support: false,
    disable_trace: true,
    
    // Kill Timeout
    kill_timeout: 5000,
    
    // Listen Timeout
    listen_timeout: 8000,
    
    // Startup Delay
    restart_delay: 4000
  }],
  
  // PM2 Deploy Configuration (optional)
  deploy: {
    production: {
      user: 'ubuntu',
      host: ['**************'],
      ref: 'origin/main',
      repo: 'https://github.com/mightybadz18/hauling-qr-trip-management.git',
      path: '/home/<USER>/hauling-qr-trip-management',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      env: {
        NODE_ENV: 'production'
      }
    }
  },
  
  // Global PM2 Settings
  pm2_serve_path: './client/build',
  pm2_serve_port: 3000,
  pm2_serve_spa: true,
  
  // Log Rotation Settings (PM2 Plus)
  log_type: 'json',
  
  // Monitoring Configuration
  monitoring: {
    http: true,
    https: false,
    port: false,
    
    // Custom Metrics
    custom_probes: [
      {
        name: 'Memory Usage',
        value: 'process.memoryUsage().heapUsed'
      },
      {
        name: 'Event Loop Lag',
        value: 'Math.round(require("@nodejs/event-loop-lag")())'
      },
      {
        name: 'Active Connections',
        value: 'process._getActiveHandles().length'
      }
    ]
  }
};
