#!/bin/bash

# Test Deployment Fixes for Hauling QR Trip System
# Verifies that the modular deployment fixes work correctly

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🧪 TESTING DEPLOYMENT FIXES"
echo "📅 $(date)"
echo

# Test 1: Check if ecosystem.config.js has proper variable substitution
log_info "Test 1: Checking ecosystem.config.js variable substitution..."

if [[ -f "/var/www/hauling-qr-system/ecosystem.config.js" ]]; then
    if grep -q "PRODUCTION_DOMAIN: 'truckhaul.top'" /var/www/hauling-qr-system/ecosystem.config.js; then
        log_success "✅ ecosystem.config.js has proper domain substitution"
    else
        log_error "❌ ecosystem.config.js still has variable placeholders"
        echo "Current content:"
        grep -A 5 -B 5 "PRODUCTION_DOMAIN" /var/www/hauling-qr-system/ecosystem.config.js || true
    fi
else
    log_error "❌ ecosystem.config.js not found"
fi

# Test 2: Check PM2 user
log_info "Test 2: Checking PM2 user..."

pm2_user=$(ps aux | grep "PM2 v" | grep -v grep | awk '{print $1}' | head -1 || echo "none")
if [[ "$pm2_user" == "ubuntu" ]]; then
    log_success "✅ PM2 is running as ubuntu user"
elif [[ "$pm2_user" == "root" ]]; then
    log_error "❌ PM2 is running as root user"
else
    log_error "❌ PM2 user could not be determined"
fi

# Test 3: Check PM2 environment variables
log_info "Test 3: Checking PM2 environment variables..."

if sudo -u ubuntu pm2 show hauling-qr-server 2>/dev/null | grep -q "NGINX_PROXY_MODE.*true"; then
    log_success "✅ NGINX_PROXY_MODE=true found in PM2 environment"
else
    log_error "❌ NGINX_PROXY_MODE not properly set in PM2"
    echo "PM2 environment variables:"
    sudo -u ubuntu pm2 show hauling-qr-server 2>/dev/null | grep -A 10 "env:" || true
fi

# Test 4: Check NGINX configuration
log_info "Test 4: Checking NGINX configuration..."

if grep -q "server_name truckhaul.top www.truckhaul.top api.truckhaul.top" /etc/nginx/sites-available/hauling-qr-system; then
    log_success "✅ NGINX server_name is properly configured"
else
    log_error "❌ NGINX server_name has issues"
    echo "Current server_name:"
    grep "server_name" /etc/nginx/sites-available/hauling-qr-system || true
fi

# Test 5: Check CORS headers in NGINX
log_info "Test 5: Checking CORS headers in NGINX..."

if grep -q "Access-Control-Allow-Origin.*https://truckhaul.top" /etc/nginx/sites-available/hauling-qr-system; then
    log_success "✅ NGINX CORS headers are properly configured"
else
    log_error "❌ NGINX CORS headers have issues"
    echo "Current CORS headers:"
    grep -n "Access-Control-Allow-Origin" /etc/nginx/sites-available/hauling-qr-system || true
fi

# Test 6: Service status
log_info "Test 6: Checking service status..."

if systemctl is-active --quiet nginx; then
    log_success "✅ NGINX is running"
else
    log_error "❌ NGINX is not running"
fi

if sudo -u ubuntu pm2 list 2>/dev/null | grep -q "hauling-qr-server.*online"; then
    log_success "✅ PM2 hauling-qr-server is online"
else
    log_error "❌ PM2 hauling-qr-server is not online"
    echo "PM2 status:"
    sudo -u ubuntu pm2 list 2>/dev/null || true
fi

# Test 7: Backend health check
log_info "Test 7: Backend health check..."

if curl -f -s http://localhost:8080/api/health >/dev/null; then
    log_success "✅ Backend health check passed"
else
    log_error "❌ Backend health check failed"
fi

echo
echo "🎯 SUMMARY:"
echo "- ecosystem.config.js variable substitution"
echo "- PM2 running as ubuntu user (not root)"
echo "- PM2 environment variables (NGINX_PROXY_MODE=true)"
echo "- NGINX configuration with proper domain"
echo "- CORS headers using dynamic domain"
echo "- Service status verification"
echo "- Backend health check"
echo
echo "🌐 Test your site: https://truckhaul.top/"
