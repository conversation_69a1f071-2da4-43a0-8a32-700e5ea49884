-- Migration 022: Fix QR Shift Status and Type Issues
-- Fixes:
-- 1. Status turning to 'scheduled' when end_time is NULL
-- 2. shift_type should be set based on time range, not just display_type

-- Update the evaluate_shift_status function to handle QR-created shifts properly
CREATE OR REPLACE FUNCTION evaluate_shift_status(
    p_shift_id INTEGER, 
    p_reference_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) RETURNS TEXT AS $$
DECLARE
    v_shift_type TEXT;
    v_start_date DATE;
    v_end_date DATE;
    v_start_time TIME;
    v_end_time TIME;
    v_current_status TEXT;
    v_auto_created BOOLEAN;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_shift_end_datetime TIMESTAMP;
    v_is_past_completion BOOLEAN;
BEGIN
    -- Get shift details including auto_created flag
    SELECT 
        shift_type,
        start_date,
        end_date,
        start_time,
        end_time,
        status::TEXT,
        auto_created,
        start_time > COALESCE(end_time, '23:59:59'::TIME)
    INTO
        v_shift_type,
        v_start_date,
        v_end_date,
        v_start_time,
        v_end_time,
        v_current_status,
        v_auto_created,
        v_is_overnight
    FROM driver_shifts
    WHERE id = p_shift_id;
    
    -- If shift not found or cancelled, return error
    IF v_shift_type IS NULL OR v_current_status = 'cancelled' THEN
        RETURN 'error';
    END IF;
    
    -- If shift is already completed, keep it completed
    IF v_current_status = 'completed' THEN
        RETURN 'completed';
    END IF;
    
    -- For QR-created shifts (auto_created = true) with NULL end_time, keep them active
    -- This handles the case where drivers scan QR codes to start shifts
    IF v_auto_created = true AND v_end_time IS NULL AND v_current_status = 'active' THEN
        RETURN 'active';
    END IF;
    
    -- Extract date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;
    
    -- If end_time is NULL, we can't calculate completion, so keep current status if active
    IF v_end_time IS NULL THEN
        IF v_current_status = 'active' THEN
            RETURN 'active';
        ELSE
            RETURN 'scheduled';
        END IF;
    END IF;
    
    -- Calculate the actual shift end datetime for proper completion logic
    IF v_is_overnight THEN
        -- For overnight shifts: end_time on the day after end_date
        v_shift_end_datetime := (v_end_date + INTERVAL '1 day')::DATE + v_end_time;
    ELSE
        -- For day shifts: end_time on the same day as end_date
        v_shift_end_datetime := v_end_date::DATE + v_end_time;
    END IF;
    
    -- Check if we're past the shift's actual end datetime
    v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    
    -- Apply business rules for status determination
    IF v_is_past_completion THEN
        -- Shift has ended (both date and time conditions met)
        RETURN 'completed';
    ELSIF v_current_date BETWEEN v_start_date AND v_end_date THEN
        -- Shift is within its date range
        IF v_is_overnight THEN
            -- Night shift: active if time >= start_time OR time <= end_time
            v_is_within_time_window := (v_current_time >= v_start_time OR v_current_time <= v_end_time);
        ELSE
            -- Day shift: active if time between start_time and end_time
            v_is_within_time_window := (v_current_time BETWEEN v_start_time AND v_end_time);
        END IF;
        
        IF v_is_within_time_window THEN
            RETURN 'active';
        ELSE
            RETURN 'scheduled';
        END IF;
    ELSE
        -- Future shifts
        RETURN 'scheduled';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create a function to determine shift_type based on start_time
CREATE OR REPLACE FUNCTION determine_shift_type_by_time(p_start_time TIME)
RETURNS shift_type AS $$
BEGIN
    -- If starting during typical day hours (6 AM - 6 PM), classify as day
    IF EXTRACT(HOUR FROM p_start_time) BETWEEN 6 AND 17 THEN
        RETURN 'day';
    -- If starting during typical night hours (6 PM - 6 AM), classify as night
    ELSIF EXTRACT(HOUR FROM p_start_time) >= 18 OR EXTRACT(HOUR FROM p_start_time) <= 5 THEN
        RETURN 'night';
    ELSE
        RETURN 'custom';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Update the set_display_type_trigger to also set shift_type for QR-created shifts
CREATE OR REPLACE FUNCTION set_display_type_trigger() RETURNS TRIGGER AS $$
BEGIN
    -- For auto-created shifts (QR-based), set shift_type based on start_time
    IF NEW.auto_created = true AND NEW.shift_type = 'custom' THEN
        NEW.shift_type := determine_shift_type_by_time(NEW.start_time);
    END IF;
    
    -- If display_type is not explicitly set, compute it intelligently
    IF NEW.display_type IS NULL THEN
        -- If user selected day or night explicitly, respect that
        IF NEW.shift_type IN ('day', 'night') THEN
            NEW.display_type := NEW.shift_type;
        ELSE
            -- For custom shifts, use intelligent classification
            NEW.display_type := classify_shift_by_time(NEW.start_time, NEW.end_time);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update existing QR-created shifts to have proper shift_type
UPDATE driver_shifts 
SET 
    shift_type = determine_shift_type_by_time(start_time),
    updated_at = CURRENT_TIMESTAMP
WHERE auto_created = true 
  AND shift_type = 'custom'
  AND status = 'active';

-- Add comments for documentation
COMMENT ON FUNCTION evaluate_shift_status(INTEGER, TIMESTAMP WITH TIME ZONE) IS 'Enhanced shift status evaluation that properly handles QR-created shifts with NULL end_time';
COMMENT ON FUNCTION determine_shift_type_by_time(TIME) IS 'Determines shift_type based on start_time: 6AM-5PM = day, 6PM-5AM = night, else custom';
COMMENT ON FUNCTION set_display_type_trigger() IS 'Enhanced trigger that sets both shift_type and display_type for QR-created shifts';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 022 completed: Fixed QR shift status and type issues - shifts stay active, proper shift_type classification';
END $$;