import React, { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { useAuth } from '../../context/AuthContext';
import shiftService from '../../services/shiftService';
import { getApiBaseUrl } from '../../utils/network-utils';
import useWebSocket from '../../hooks/useWebSocket';

import { getWeekStart, getWeekEnd, getMonthStart, getMonthEnd, getToday, getDaysInRange, calculateDuration } from '../../utils/dateHelpers';

// Constants for timing and configuration
const WEBSOCKET_REFRESH_DELAY = 1000; // ms - Allow time for database updates
const STATUS_CHANGE_DELAY = 500; // ms - Faster refresh for status changes
const RESULTS_LIMIT = 100; // Maximum results per page

// Safe token getter with error handling
const getAuthToken = () => {
  try {
    return localStorage.getItem('hauling_token');
  } catch (error) {
    console.error('❌ Error accessing localStorage:', error);
    return null;
  }
};

const SimplifiedShiftManagement = () => {
  const { user } = useAuth();
  const [shifts, setShifts] = useState([]);
  const [trucks, setTrucks] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingShift, setEditingShift] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [shiftToDelete, setShiftToDelete] = useState(null);
  const [pagination, setPagination] = useState({});

  // Enhanced filter state - using single values to match UI
  const [filters, setFilters] = useState({
    truck_id: '',
    driver_id: '',
    status: '',
    shift_type: '',
    date_from: getWeekStart(),
    date_to: getWeekEnd(),
    sort_by: 'start_date',
    sort_order: 'DESC'
  });

  // WebSocket integration for real-time updates
  const { isConnected: wsConnected, lastMessage } = useWebSocket(user);

  const loadShifts = useCallback(async () => {
    try {
      setLoading(true);

      // Reset shifts to empty array to force UI refresh
      setShifts([]);

      // Build query parameters from current filters
      const queryParams = {};

      if (filters.truck_id) {
        queryParams.truck_id = filters.truck_id;
      }

      if (filters.driver_id) {
        queryParams.driver_id = filters.driver_id;
      }

      if (filters.status) {
        queryParams.status = filters.status;
      }

      if (filters.shift_type) {
        queryParams.shift_type = filters.shift_type;
      }

      if (filters.date_from) {
        queryParams.date_from = filters.date_from;
      }

      if (filters.date_to) {
        queryParams.date_to = filters.date_to;
      }

      queryParams.sort_by = filters.sort_by;
      queryParams.sort_order = filters.sort_order;
      queryParams.limit = RESULTS_LIMIT;

      //
      // Perform API call with filters
      const response = await shiftService.getShifts(queryParams);

      // Normalize dates to Asia/Manila immediately to avoid any downstream TZ drift
      const fmtYMD = new Intl.DateTimeFormat('en-CA', {
        timeZone: 'Asia/Manila', year: 'numeric', month: '2-digit', day: '2-digit'
      });
      const fmtUS = new Intl.DateTimeFormat('en-US', {
        timeZone: 'Asia/Manila', month: 'short', day: 'numeric', year: 'numeric'
      });
      const normalizedData = (response.data || []).map(s => {
        const normalize = (val) => {
          if (!val) return { ymd: null, display: 'N/A' };
          const d = val instanceof Date ? val : new Date(val);
          if (isNaN(d.getTime())) return { ymd: null, display: 'Invalid Date' };
          return { ymd: fmtYMD.format(d), display: fmtUS.format(d) };
        };
        const start = normalize(s.start_date);
        const end = normalize(s.end_date);
        return {
          ...s,
          start_date_local_ymd: start.ymd,
          start_date_display: start.display,
          end_date_local_ymd: end.ymd,
          end_date_display: end.display
        };
      });

      setShifts(normalizedData);
      setPagination(response.pagination || {});

    } catch (error) {
      console.error('❌ Error loading shifts:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        filters: filters
      });
      toast.error(`Failed to load shifts: ${error.message}`);
      // Ensure shifts is set to empty array on error
      setShifts([]);
      setPagination({});
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const loadTrucks = useCallback(async () => {
    try {
      const apiUrl = getApiBaseUrl();
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch(`${apiUrl}/trucks`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!response.ok) {
        throw new Error(`Failed to load trucks: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setTrucks(data.data || []);
    } catch (error) {
      console.error('❌ Error loading trucks:', error);
      toast.error('Failed to load trucks data');
      setTrucks([]); // Ensure state is reset on error
    }
  }, []);

  const loadDrivers = useCallback(async () => {
    try {
      const apiUrl = getApiBaseUrl();
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch(`${apiUrl}/drivers`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!response.ok) {
        throw new Error(`Failed to load drivers: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setDrivers(data.data || []);
    } catch (error) {
      console.error('❌ Error loading drivers:', error);
      toast.error('Failed to load drivers data');
      setDrivers([]); // Ensure state is reset on error
    }
  }, []);

  // Handle WebSocket messages for real-time updates
  useEffect(() => {
    if (!lastMessage) return;

    // Handle shift-related events
    const shiftRelatedEvents = [
      'driver_connected',
      'driver_disconnected',
      'driver_handover',
      'bulk_shifts_created',
      'shift_status_changed'
    ];

    if (shiftRelatedEvents.includes(lastMessage.type)) {
      // Show user feedback
      if (lastMessage.title && lastMessage.message) {
        toast.success(
          <div>
            <div className="font-medium">{lastMessage.title}</div>
            <div className="text-sm mt-1">{lastMessage.message}</div>
          </div>,
          { duration: 4000 }
        );
      } else if (lastMessage.message) {
        toast.success(lastMessage.message);
      }

      // Refresh shifts after a short delay to ensure database is updated
      const refreshDelay = lastMessage.type === 'shift_status_changed' ? STATUS_CHANGE_DELAY : WEBSOCKET_REFRESH_DELAY;
      setTimeout(() => {
        loadShifts();
      }, refreshDelay);
    }
  }, [lastMessage, loadShifts]);

  useEffect(() => {
    loadShifts();
    loadTrucks();
    loadDrivers();
  }, [loadDrivers, loadShifts, loadTrucks]);



  // Data state tracking effect removed for cleaner console output

  // Filter management functions
  const updateFilter = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      truck_id: '',
      driver_id: '',
      status: '',
      shift_type: '',
      date_from: getWeekStart(),
      date_to: getWeekEnd(),
      sort_by: 'start_date',
      sort_order: 'DESC'
    });
  };

  const setDatePreset = (preset) => {
    const today = getToday();
    switch (preset) {
      case 'today':
        updateFilter('date_from', today);
        updateFilter('date_to', today);
        break;
      case 'week':
        updateFilter('date_from', getWeekStart());
        updateFilter('date_to', getWeekEnd());
        break;
      case 'month':
        updateFilter('date_from', getMonthStart());
        updateFilter('date_to', getMonthEnd());
        break;
      default:
        break;
    }
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.truck_id) count++;
    if (filters.driver_id) count++;
    if (filters.status) count++;
    if (filters.shift_type) count++;
    return count;
  };

  // Using utility function for date range calculation
  const getFilterDaysInRange = () => getDaysInRange(filters.date_from, filters.date_to);

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { bg: 'bg-green-100', text: 'text-green-800', label: 'Active', icon: '🟢' },
      completed: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Completed', icon: '✅' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', label: 'Cancelled', icon: '❌' }
    };

    const config = statusConfig[status] || { bg: 'bg-secondary-100', text: 'text-secondary-800', label: status, icon: 'ℹ️' };
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text} flex items-center gap-1`}>
        <span>{config.icon}</span>
        {config.label}
      </span>
    );
  };

  const handleEditShift = (shift) => {
    setEditingShift(shift);
    setShowCreateModal(true);
  };

  const handleDeleteShift = (shift) => {
    setShiftToDelete(shift);
    setShowDeleteModal(true);
  };

  const confirmDeleteShift = async () => {
    if (!shiftToDelete) return;

    try {
      const response = await shiftService.deleteShift(shiftToDelete.id);

      // Handle different response types based on backend action
      if (response.action === 'cancelled') {
        toast(response.message || 'Shift cancelled instead of deleted due to associated trip logs', {
          icon: '⚠️',
          style: { background: '#FEF3C7', color: '#92400E' }
        });
      } else if (response.data && response.data.cancelled_shifts && response.data.cancelled_shifts.length > 0) {
        const cancelledCount = response.data.cancelled_shifts.length;
        const deletedCount = response.data.deleted_shifts.length;
        if (cancelledCount > 0 && deletedCount > 0) {
          toast(`${deletedCount} shifts deleted, ${cancelledCount} shifts cancelled (have associated trips)`, {
            icon: '⚠️',
            style: { background: '#FEF3C7', color: '#92400E' }
          });
        } else if (cancelledCount > 0) {
          toast(`${cancelledCount} shifts cancelled instead of deleted (have associated trip logs)`, {
            icon: '⚠️',
            style: { background: '#FEF3C7', color: '#92400E' }
          });
        } else {
          toast.success(response.message || 'Shifts processed successfully');
        }
      } else {
        toast.success(response.message || 'Shift deleted successfully!');
      }

      setShowDeleteModal(false);
      setShiftToDelete(null);
      loadShifts();
    } catch (error) {
      console.error('Error deleting shift:', error);
      toast.error(error.message || 'Failed to delete shift');
    }
  };

  const handleStatusChange = async (shiftId, newStatus) => {
    try {
      // Immediate UI update before API call
      setShifts(prevShifts =>
        prevShifts.map(shift =>
          shift.id === shiftId ? { ...shift, status: newStatus } : shift
        )
      );

      // Make API call
      let apiResponse;
      if (newStatus === 'cancelled') {
        apiResponse = await shiftService.cancelShift(shiftId);
        toast.success('Shift cancelled successfully!');
      } else if (newStatus === 'completed') {
        apiResponse = await shiftService.completeShift(shiftId);
        toast.success('Shift completed successfully!');
      } else {
        apiResponse = await shiftService.updateShift(shiftId, { status: newStatus });
        toast.success(`Shift ${newStatus} successfully!`);
      }

      // Optimized refresh - wait for database consistency then update only the changed shift
      await new Promise(resolve => setTimeout(resolve, STATUS_CHANGE_DELAY));

      setShifts(prevShifts =>
        prevShifts.map(shift =>
          shift.id === shiftId
            ? { ...shift, ...apiResponse.data }
            : shift
        )
      );

    } catch (error) {
      console.error('❌ Error updating shift status:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        shiftId,
        newStatus,
        time: new Date().toISOString()
      });
      toast.error(error.message || 'Failed to update shift status');

      // Revert UI update if API call fails
      setShifts(prevShifts =>
        prevShifts.map(shift =>
          shift.id === shiftId ? { ...shift, status: shift.status } : shift
        )
      );
    }
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-secondary-900">
                🔄 Enhanced Shift Management
              </h1>
              <p className="mt-2 text-secondary-600">
                Comprehensive shift scheduling with advanced filtering
                {wsConnected && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-green-100 text-green-800">
                    <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></span>
                    Live Updates
                  </span>
                )}
                {!wsConnected && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-yellow-100 text-yellow-800">
                    <span className="w-1.5 h-1.5 bg-yellow-500 rounded-full mr-1"></span>
                    Manual Refresh
                  </span>
                )}
              </p>
              {pagination.total_count !== undefined && (
                <p className="mt-1 text-sm text-secondary-500">
                  Showing {pagination.returned_count} of {pagination.total_count} shifts
                  {getActiveFilterCount() > 0 && ` (${getActiveFilterCount()} filters active)`}
                </p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={loadShifts}
                disabled={loading}
                className="bg-secondary-100 text-secondary-700 px-3 py-2 rounded-md hover:bg-secondary-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                title="Refresh shift data"
              >
                <svg className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {loading ? 'Refreshing...' : 'Refresh'}
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors"
              >
                + Create Shift
              </button>
            </div>
          </div>
        </div>

        {/* Horizontal Filter Bar - Matching Assignment Management Design */}
        <div className="mb-6 bg-white rounded-lg shadow border border-secondary-200 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">

            {/* Date Range Filter */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Date Range ({getFilterDaysInRange()} days)
              </label>
              <div className="space-y-2">
                <div className="flex space-x-1">
                  <input
                    type="date"
                    value={filters.date_from}
                    onChange={(e) => updateFilter('date_from', e.target.value)}
                    className="input text-sm flex-1"
                    placeholder="From"
                  />
                  <input
                    type="date"
                    value={filters.date_to}
                    onChange={(e) => updateFilter('date_to', e.target.value)}
                    className="input text-sm flex-1"
                    placeholder="To"
                  />
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => setDatePreset('today')}
                    className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
                  >
                    Today
                  </button>
                  <button
                    onClick={() => setDatePreset('week')}
                    className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
                  >
                    Week
                  </button>
                  <button
                    onClick={() => setDatePreset('month')}
                    className="px-2 py-1 text-xs bg-secondary-100 text-secondary-600 rounded hover:bg-secondary-200 transition-colors"
                  >
                    Month
                  </button>
                </div>
              </div>
            </div>

            {/* Truck Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Truck
              </label>
              <select
                value={filters.truck_id}
                onChange={(e) => updateFilter('truck_id', e.target.value)}
                className="input text-sm w-full"
              >
                <option value="">All Trucks</option>
                {trucks.map(truck => (
                  <option key={truck.id} value={truck.id}>
                    {truck.truck_number}
                  </option>
                ))}
              </select>
            </div>

            {/* Driver Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Driver
              </label>
              <select
                value={filters.driver_id}
                onChange={(e) => updateFilter('driver_id', e.target.value)}
                className="input text-sm w-full"
              >
                <option value="">All Drivers</option>
                {drivers.map(driver => (
                  <option key={driver.id} value={driver.id}>
                    {driver.full_name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => updateFilter('status', e.target.value)}
                className="input text-sm w-full"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            {/* Shift Type Filter */}
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Shift Type
              </label>
              <select
                value={filters.shift_type}
                onChange={(e) => updateFilter('shift_type', e.target.value)}
                className="input text-sm w-full"
              >
                <option value="">All Types</option>
                <option value="day">☀️ Day Shift</option>
                <option value="night">🌙 Night Shift</option>
              </select>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <button
                onClick={clearAllFilters}
                disabled={getActiveFilterCount() === 0}
                className="btn btn-secondary w-full disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                Clear Filters
                {getActiveFilterCount() > 0 && (
                  <span className="ml-1 bg-primary-100 text-primary-800 text-xs rounded-full px-2 py-0.5">
                    {getActiveFilterCount()}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Filter Summary */}
          {getActiveFilterCount() > 0 && (
            <div className="mt-3 pt-3 border-t border-secondary-200">
              <div className="text-sm text-secondary-600">
                <span className="font-medium">Active Filters:</span>
                {filters.truck_id && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                    Truck: {trucks.find(t => t.id === parseInt(filters.truck_id))?.truck_number || filters.truck_id}
                  </span>
                )}
                {filters.driver_id && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-green-100 text-green-800">
                    Driver: {drivers.find(d => d.id === parseInt(filters.driver_id))?.full_name || filters.driver_id}
                  </span>
                )}
                {filters.status && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-yellow-100 text-yellow-800">
                    Status: {filters.status}
                  </span>
                )}
                {filters.shift_type && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs bg-purple-100 text-purple-800">
                    Type: {filters.shift_type}
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Shifts Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-secondary-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-secondary-900">
                  Shifts {filters.date_from === filters.date_to
                    ? `for ${formatShiftDate(filters.date_from)}`
                    : `from ${formatShiftDate(filters.date_from)} to ${formatShiftDate(filters.date_to)}`
                  }
                </h3>
                {loading && (
                  <p className="text-sm text-secondary-500 mt-1">Loading shifts...</p>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <select
                  value={`${filters.sort_by}_${filters.sort_order}`}
                  onChange={(e) => {
                    const [sort_by, sort_order] = e.target.value.split('_');
                    updateFilter('sort_by', sort_by);
                    updateFilter('sort_order', sort_order);
                  }}
                  className="text-sm border border-secondary-300 rounded-md px-2 py-1"
                >
                  <option value="start_date_DESC">Shift Date (Newest)</option>
                  <option value="start_date_ASC">Shift Date (Oldest)</option>
                  <option value="start_date_DESC">Start Date (Newest)</option>
                  <option value="start_date_ASC">Start Date (Oldest)</option>
                  <option value="end_date_DESC">End Date (Newest)</option>
                  <option value="end_date_ASC">End Date (Oldest)</option>
                  <option value="truck_number_ASC">Truck (A-Z)</option>
                  <option value="truck_number_DESC">Truck (Z-A)</option>
                  <option value="driver_name_ASC">Driver (A-Z)</option>
                  <option value="driver_name_DESC">Driver (Z-A)</option>
                  <option value="status_ASC">Status (A-Z)</option>
                  <option value="status_DESC">Status (Z-A)</option>
                </select>
              </div>
            </div>
          </div>

          {(() => {
            if (loading) {
              return (
                <div className="p-6 text-center">
                  <div className="inline-flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2"></div>
                    Loading shifts...
                  </div>
                </div>
              );
            }

            if (shifts.length === 0) {
              return (
                <div className="p-6 text-center text-secondary-500">
                  <div className="text-4xl mb-2">📅</div>
                  <p className="text-lg font-medium mb-2">No shifts found</p>
                  <p className="text-sm">
                    {getActiveFilterCount() > 0
                      ? 'Try adjusting your filters or create a new shift'
                      : `No shifts found for the selected date range`
                    }
                  </p>
                  {getActiveFilterCount() > 0 && (
                    <button
                      onClick={clearAllFilters}
                      className="mt-3 text-primary-600 hover:text-primary-800 text-sm underline"
                    >
                      Clear all filters
                    </button>
                  )}
                </div>
              );
            }

            return (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="bg-secondary-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Truck
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Driver
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Shift Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Start Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      End Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {shifts.map((shift) => {
                    const truck = trucks.find(t => t.id === shift.truck_id);
                    const driver = drivers.find(d => d.id === shift.driver_id);
                    const duration = calculateDuration(
                      shift.start_date_local_ymd ?? shift.start_date,
                      shift.start_time,
                      shift.end_date_local_ymd ?? shift.end_date,
                      shift.end_time
                    );

                    return (
                      <tr key={shift.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {truck?.truck_number || 'Unknown'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {driver?.full_name || 'Unknown'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${shift.shift_type === 'day' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'
                            }`}>
                            {shift.shift_type === 'day' ? '☀️ Day' : '🌙 Night'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {(() => {
                            // 🔍 DEBUG: Log each shift's date processing in the table
                            const result = shift.start_date_display ?? formatShiftDate(shift.start_date);
                            return result;
                          })()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          <ShiftDateRangeDisplay
                            startDate={shift.start_date}
                            endDate={shift.end_date}
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {shift.start_time?.substring(0, 5)} - {shift.end_time?.substring(0, 5)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                          {duration}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(shift.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            {/* Edit Button */}
                            <button
                              onClick={() => handleEditShift(shift)}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              title="Edit Shift"
                            >
                              ✏️ Edit
                            </button>

                            {/* Status Action Buttons */}
                            {shift.status === 'active' && (
                              <button
                                onClick={() => handleStatusChange(shift.id, 'completed')}
                                className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                                title="Complete Shift"
                              >
                                ✅ Complete
                              </button>
                            )}

                            {shift.status === 'active' && (
                              <button
                                onClick={() => handleStatusChange(shift.id, 'cancelled')}
                                className="text-orange-600 hover:text-orange-800 text-sm font-medium"
                                title="Cancel Shift"
                              >
                                ⏸️ Cancel
                              </button>
                            )}

                            {/* Delete Button - only for non-active shifts */}
                            {shift.status !== 'active' && (
                              <button
                                onClick={() => handleDeleteShift(shift)}
                                className="text-red-600 hover:text-red-800 text-sm font-medium"
                                title="Delete Shift"
                              >
                                🗑️ Delete
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
            );
          })()}
        </div>
      </div>

      {/* Create/Edit Shift Modal */}
      {showCreateModal && (
        <CreateShiftModal
          trucks={trucks}
          drivers={drivers}
          defaultDate={filters.date_from}
          editingShift={editingShift}
          onClose={() => {
            setShowCreateModal(false);
            setEditingShift(null);
          }}
          onSuccess={() => {
            setShowCreateModal(false);
            setEditingShift(null);
            loadShifts();
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <DeleteConfirmationModal
          shift={shiftToDelete}
          onClose={() => {
            setShowDeleteModal(false);
            setShiftToDelete(null);
          }}
          onConfirm={confirmDeleteShift}
        />
      )}
    </div>
  );
};

// Component to display shift date range with visual indicators
const ShiftDateRangeDisplay = ({ startDate, endDate }) => {
  if (!startDate || !endDate) {
    return <span className="text-secondary-400">N/A</span>;
  }

  try {
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Check if it's a single day shift
    const isSingleDay = start.toDateString() === end.toDateString();

    if (isSingleDay) {
      return (
        <div className="flex items-center">
          <span className="text-green-600 mr-1">📅</span>
          <span className="text-xs text-green-700 font-medium">Same day</span>
        </div>
      );
    } else {
      // Multi-day shift
      const endFormatted = end.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });

      // Calculate number of days
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

      return (
        <div className="flex flex-col">
          <div className="flex items-center">
            <span className="text-blue-600 mr-1">📅</span>
            <span className="text-xs font-medium text-blue-700">{endFormatted}</span>
          </div>
          <span className="text-xs text-secondary-500">({diffDays} days total)</span>
        </div>
      );
    }
  } catch (error) {
    return <span className="text-red-500 text-xs">Invalid Date</span>;
  }
};

// Helper function to format shift date - timezone-safe (Asia/Manila)
const formatShiftDate = (dateInput) => {
  if (!dateInput) return 'N/A';

  try {
    let date;

    // Normalize input to a Date object without relying on browser local timezone
    if (dateInput instanceof Date) {
      date = dateInput;
    } else if (typeof dateInput === 'string') {
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
        // Pure DATE from DB → build UTC midnight, then format in Asia/Manila
        const [y, m, d] = dateInput.split('-').map(n => parseInt(n, 10));
        date = new Date(Date.UTC(y, m - 1, d, 0, 0, 0));
      } else {
        // ISO or other string → let JS parse, then format in Asia/Manila
        date = new Date(dateInput);
      }
    } else {
      date = new Date(dateInput);
    }

    if (isNaN(date.getTime())) return 'Invalid Date';

    // Always format explicitly in Asia/Manila to avoid environment TZ drift
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: 'Asia/Manila',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });

    const result = formatter.format(date);
    return result;
  } catch (error) {
    console.error('Error formatting date:', error, dateInput);
    return 'Invalid Date';
  }
};

// (formatShiftDateRange removed as it was unused)




// Enhanced Create/Edit Shift Modal with Unified Date Range Approach
const CreateShiftModal = ({ trucks, drivers, defaultDate, editingShift, onClose, onSuccess }) => {
  // Helper function to get appropriate dates for editing
  const getEditDates = (shift) => {
    if (!shift) return { start_date: defaultDate, end_date: '' };

    // Use start_date/end_date when present
    if (shift.start_date && shift.end_date) {
      return { start_date: shift.start_date, end_date: shift.end_date };
    }
    if (shift.start_date && !shift.end_date) {
      return { start_date: shift.start_date, end_date: '' };
    }

    // Default fallback with optional end_date
    return { start_date: defaultDate, end_date: '' };
  };

  const editDates = getEditDates(editingShift);

  // Initialize times based on shift type with driver-connect presets
  const initialStartTime = editingShift?.start_time?.substring(0, 5)
    || ((editingShift?.shift_type || 'day') === 'night' ? '18:01' : '06:00');
  const initialEndTime = editingShift?.end_time?.substring(0, 5) || '';

  const [formData, setFormData] = useState({
    truck_id: editingShift?.truck_id || '',
    driver_id: editingShift?.driver_id || '',
    shift_type: editingShift?.shift_type || 'day',
    start_date: editDates.start_date,
    end_date: editDates.end_date,
    start_time: initialStartTime,
    end_time: initialEndTime,
    handover_notes: editingShift?.handover_notes || '',
    create_bulk_daily_shifts: false // Default to single multi-day shift
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Preset times based on shift type aligned with driver-connect (day 06:00–18:00, night 18:01–05:59)
  const handleShiftTypeChange = (shiftType) => {
    setFormData(prev => ({
      ...prev,
      shift_type: shiftType,
      start_time: shiftType === 'day' ? '06:00' : '18:01',
      // Keep end_time optional; do not auto-fill unless user sets it
      end_time: prev.end_time || ''
    }));
  };



  const validateForm = () => {
    const newErrors = {};
    if (!formData.truck_id) newErrors.truck_id = 'Please select a truck';
    if (!formData.driver_id) newErrors.driver_id = 'Please select a driver';
    if (!formData.start_date) newErrors.start_date = 'Start date is required';
    // end_date is optional to align with driver-connect PWA

    // Validate date range only if end_date is provided
    if (formData.start_date && formData.end_date) {
      if (new Date(formData.end_date) < new Date(formData.start_date)) {
        newErrors.end_date = 'End date must be after or equal to start date';
      }
      // Note: No max-day limit for open/unified shifts. Limit applies only to legacy bulk daily creation.
    }

    // Time validations aligned with driver-connect presets & ShiftTypeDetector
    const normalizeTime = (t) => {
      if (!t || typeof t !== 'string') return null;
      return t.length === 5 ? `${t}:00` : t; // ensure HH:MM:SS
    };
    const toMinutes = (hhmm) => {
      const [hh, mm] = hhmm.split(':');
      return parseInt(hh, 10) * 60 + parseInt(mm, 10);
    };

    const startNorm = normalizeTime(formData.start_time);
    const endNorm = normalizeTime(formData.end_time);

    if (!startNorm) newErrors.start_time = 'Start time is required';
    // end_time is optional to align with driver-connect PWA

    if (startNorm && endNorm) {
      const st = startNorm.slice(0, 5);
      const et = endNorm.slice(0, 5);
      const stMin = toMinutes(st);
      const etMin = toMinutes(et);

      if (formData.shift_type === 'day') {
        const minDay = toMinutes('06:00');
        const maxDay = toMinutes('18:00');
        const withinDay = stMin >= minDay && etMin <= maxDay && etMin >= stMin;
        if (!withinDay) {
          newErrors.time = 'Day shift must be between 06:00 and 18:00 with no overnight span';
        }
      } else if (formData.shift_type === 'night') {
        // Night shift: allow overnight window 18:01–05:59
        const nightStart = toMinutes('18:01');
        const morningEnd = toMinutes('05:59');
        const validStartNight = stMin >= nightStart;
        const validEndMorning = etMin <= morningEnd;
        if (!(validStartNight || validEndMorning)) {
          newErrors.time = 'Night shift must start after 18:01 or end before 05:59';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Build core payload fields (no status here to avoid unintended transitions)
      const baseShiftCore = {
        truck_id: parseInt(formData.truck_id),
        driver_id: parseInt(formData.driver_id),
        shift_type: formData.shift_type,
        start_time: `${formData.start_time}:00`,
        // end_time optional: send null if empty to match driver-connect structure
        end_time: formData.end_time ? `${formData.end_time}:00` : null
      };

      // Unified data structure using start_date/end_date for all operations
      const unifiedCore = {
        ...baseShiftCore,
        start_date: formData.start_date,
        // end_date optional: send null if empty to match driver-connect structure
        end_date: formData.end_date || null
      };



      if (editingShift) {
        // Update existing shift without forcing status changes
        await shiftService.updateShift(editingShift.id, unifiedCore);

        const isRangeEdit = formData.start_date !== formData.end_date;
        const message = isRangeEdit
          ? `Shift updated to ${getDaysInRange(formData.start_date, formData.end_date)} days successfully!`
          : 'Shift updated successfully!';
        toast.success(message);
      } else {
        // Create new shift with unified approach and conflict resolution
        try {
          await shiftService.createShift(unifiedCore);

          const isRangeCreate = formData.start_date !== formData.end_date;
          const message = isRangeCreate
            ? `${getDaysInRange(formData.start_date, formData.end_date)} shifts created successfully!`
            : 'Shift created successfully!';
          toast.success(message);
        } catch (createError) {
          // Handle conflict resolution
          if (createError.response?.status === 400 && createError.response.data?.conflicts) {
            const conflictData = createError.response.data;
            const conflictCount = conflictData.conflicts?.length || 0;

            const userChoice = window.confirm(
              `Conflicts found on ${conflictCount} date(s).\n\n` +
              `Conflicting dates:\n${conflictData.conflicts.map(c => `• ${c.date}: ${c.message}`).join('\n')}\n\n` +
              `Choose an option:\n` +
              `• OK: Overwrite existing shifts\n` +
              `• Cancel: Keep existing shifts and cancel operation`
            );

            if (userChoice) {
              // User chose to overwrite - retry with overwrite_existing flag
              const overwriteData = {
                ...unifiedCore,
                overwrite_existing: true
              };

              await shiftService.createShift(overwriteData);

              const isRangeCreate = formData.start_date !== formData.end_date;
              const message = isRangeCreate
                ? `${getDaysInRange(formData.start_date, formData.end_date)} shifts created successfully (${conflictCount} existing shifts overwritten)!`
                : 'Shift created successfully (existing shift overwritten)!';
              toast.success(message);
            } else {
              // User chose to cancel
              toast.info('Shift creation cancelled - existing shifts preserved');
              return; // Don't call onSuccess()
            }
          } else {
            // Re-throw other errors
            throw createError;
          }
        }
      }

      onSuccess();
    } catch (error) {
      console.error(`Error ${editingShift ? 'updating' : 'creating'} shift:`, error);
      toast.error(error.message || `Failed to ${editingShift ? 'update' : 'create'} shift`);
    } finally {
      setLoading(false);
    }
  };



  // Helper function for unified date range summary
  const getUnifiedDateRangeSummary = () => {
    if (!formData.start_date) {
      return 'Please select dates';
    }

    if (!formData.end_date) {
      return `Open-ended from ${new Date(formData.start_date).toLocaleDateString()}`;
    }

    const isSingleDay = formData.start_date === formData.end_date;

    if (isSingleDay) {
      const date = new Date(formData.start_date);
      return `Single shift on ${date.toLocaleDateString()}`;
    } else {
      const days = getDaysInRange(formData.start_date, formData.end_date);
      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.end_date);
      return `${days} shifts from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`;
    }
  };

  const duration = formData.end_date && formData.end_time
      ? calculateDuration(formData.start_date, formData.start_time, formData.end_date, formData.end_time)
      : 'N/A';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-secondary-900">
            {editingShift ? 'Edit Shift' : 'Create New Shift'}
          </h3>
          <button onClick={onClose} className="text-secondary-400 hover:text-secondary-600">✕</button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Truck Selection */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Dump Truck *
            </label>
            <select
              value={formData.truck_id}
              onChange={(e) => setFormData(prev => ({ ...prev, truck_id: e.target.value }))}
              className={`w-full border rounded-md px-3 py-2 ${errors.truck_id ? 'border-red-500' : 'border-secondary-300'}`}
            >
              <option value="">Select a truck</option>
              {trucks.map(truck => (
                <option key={truck.id} value={truck.id}>
                  {truck.truck_number} - {truck.license_plate}
                </option>
              ))}
            </select>
            {errors.truck_id && <p className="text-red-500 text-xs mt-1">{errors.truck_id}</p>}
          </div>

          {/* Driver Selection */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Driver *
            </label>
            <select
              value={formData.driver_id}
              onChange={(e) => setFormData(prev => ({ ...prev, driver_id: e.target.value }))}
              className={`w-full border rounded-md px-3 py-2 ${errors.driver_id ? 'border-red-500' : 'border-secondary-300'}`}
            >
              <option value="">Select a driver</option>
              {drivers.map(driver => (
                <option key={driver.id} value={driver.id}>
                  {driver.full_name} - {driver.employee_id}
                </option>
              ))}
            </select>
            {errors.driver_id && <p className="text-red-500 text-xs mt-1">{errors.driver_id}</p>}
          </div>

          {/* Shift Type */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Shift Type
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => handleShiftTypeChange('day')}
                className={`p-3 rounded-md border text-center ${formData.shift_type === 'day'
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-secondary-300 bg-white text-secondary-700'
                  }`}
              >
                <div className="text-lg">☀️</div>
                <div className="text-sm font-medium">Day Shift</div>
                <div className="text-xs text-secondary-500">6:00 AM - 6:00 PM</div>
              </button>
              <button
                type="button"
                onClick={() => handleShiftTypeChange('night')}
                className={`p-3 rounded-md border text-center ${formData.shift_type === 'night'
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-secondary-300 bg-white text-secondary-700'
                  }`}
              >
                <div className="text-lg">🌙</div>
                <div className="text-sm font-medium">Night Shift</div>
                <div className="text-xs text-secondary-500">6:00 PM - 6:00 AM</div>
              </button>
            </div>
          </div>

          {/* Unified Date Range Selection */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              📅 Date Range
              <span className="text-xs text-secondary-500 ml-2">
                (Set same start and end date for single-day shifts)
              </span>
            </label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-secondary-600 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    start_date: e.target.value,
                    // Auto-set end_date to start_date if it's currently empty or before start_date
                    end_date: !prev.end_date || new Date(prev.end_date) < new Date(e.target.value)
                      ? e.target.value
                      : prev.end_date
                  }))}
                  className={`input text-sm ${errors.start_date ? 'border-red-500' : ''}`}
                  required
                />
                {errors.start_date && (
                  <p className="text-red-500 text-xs mt-1">{errors.start_date}</p>
                )}
              </div>
              <div>
                <label className="block text-xs font-medium text-secondary-600 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={formData.end_date}
                  placeholder="N/A"
                  onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                  className={`input text-sm ${errors.end_date ? 'border-red-500' : ''}`}
                  min={formData.start_date}
                />
                {errors.end_date && (
                  <p className="text-red-500 text-xs mt-1">{errors.end_date}</p>
                )}
              </div>
            </div>
          </div>

          {/* Unified Schedule Summary */}
          <div className="bg-blue-50 p-3 rounded-md">
            <div className="text-sm font-medium text-blue-700">
              📋 Schedule Summary: {getUnifiedDateRangeSummary()}
            </div>
            <div className="text-xs text-blue-600 mt-1">
              {formData.start_date === formData.end_date
                ? "Single-day shift using unified date range approach."
                : "Multi-day shift spanning the entire date range."
              }
            </div>
          </div>

          {/* Optional: Bulk Daily Shifts for Legacy Support */}
          {formData.start_date && formData.end_date && formData.start_date !== formData.end_date && (
            <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.create_bulk_daily_shifts}
                  onChange={(e) => setFormData(prev => ({ ...prev, create_bulk_daily_shifts: e.target.checked }))}
                  className="mr-2"
                />
                <span className="text-sm text-amber-700">
                  <strong>Legacy Mode:</strong> Create individual daily shifts instead of unified multi-day shift
                </span>
              </label>
              <div className="text-xs text-amber-600 mt-1">
                ⚠️ Only use this for compatibility with legacy systems that expect daily shift rows.
              </div>
            </div>
          )}

          {/* Time Range */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Start Time
              </label>
              <input
                type="time"
                value={formData.start_time}
                onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
                className="w-full border border-secondary-300 rounded-md px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                End Time
              </label>
              <input
                type="time"
                value={formData.end_time}
                placeholder="N/A"
                onChange={(e) => setFormData(prev => ({ ...prev, end_time: e.target.value }))}
                className="w-full border border-secondary-300 rounded-md px-3 py-2"
              />
            </div>
          </div>

          {/* Duration Display */}
          <div className="bg-secondary-50 p-3 rounded-md">
            <div className="text-sm font-medium text-secondary-700">
              Duration: <span className="text-primary-600">{duration}</span>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-secondary-700 border border-secondary-300 rounded-md hover:bg-secondary-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
            >
              {loading ? (editingShift ? 'Updating...' : 'Creating...') : (editingShift ? 'Update Shift' : 'Create Shift')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Delete Confirmation Modal
const DeleteConfirmationModal = ({ shift, onClose, onConfirm }) => {
  const truck = shift ? `Truck ${shift.truck_id}` : '';
  const driver = shift ? `Driver ${shift.driver_id}` : '';

  // Enhanced date display for delete confirmation
  const getDateRangeDisplay = () => {
    if (!shift) return '';

    const startDate = shift.start_date;
    const endDate = shift.end_date;

    if (!startDate) return 'No date';

    const start = new Date(startDate);
    const end = new Date(endDate);
    const isSingleDay = start.toDateString() === end.toDateString();

    if (isSingleDay) {
      return start.toLocaleDateString();
    } else {
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      return `${start.toLocaleDateString()} to ${end.toLocaleDateString()} (${diffDays} days)`;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-secondary-900">Confirm Delete</h3>
          <button onClick={onClose} className="text-secondary-400 hover:text-secondary-600">✕</button>
        </div>

        <div className="mb-6">
          <div className="flex items-center mb-3">
            <span className="text-2xl mr-3">⚠️</span>
            <div>
              <p className="text-secondary-900 font-medium">Delete this shift?</p>
              <p className="text-secondary-600 text-sm">This action cannot be undone.</p>
            </div>
          </div>

          <div className="bg-secondary-50 p-3 rounded-md">
            <p className="text-sm text-secondary-700">
              <strong>Shift Details:</strong><br />
              {truck} - {driver}<br />
              📅 {getDateRangeDisplay()}<br />
              🕐 {shift?.start_time?.substring(0, 5)} - {shift?.end_time?.substring(0, 5)}<br />
              Status: {shift?.status}
            </p>
          </div>

          {shift?.status === 'active' && (
            <div className="bg-red-50 border border-red-200 p-3 rounded-md mt-3">
              <p className="text-red-700 text-sm">
                ⚠️ <strong>Warning:</strong> This shift is currently active. Deleting it may affect ongoing operations.
              </p>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-secondary-700 border border-secondary-300 rounded-md hover:bg-secondary-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Delete Shift
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedShiftManagement;
