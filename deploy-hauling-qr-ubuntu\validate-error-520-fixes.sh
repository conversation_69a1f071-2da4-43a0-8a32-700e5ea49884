#!/bin/bash

# =============================================================================
# ERROR 520 FIXES VALIDATION SCRIPT
# =============================================================================
# Description: Validates that Error 520 fixes are properly integrated
# Usage: ./validate-error-520-fixes.sh
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[VALIDATION]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[VALIDATION]${NC} $1"
}

log_error() {
    echo -e "${RED}[VALIDATION]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[VALIDATION]${NC} $1"
}

# Configuration
APP_DIR="/var/www/hauling-qr-system"
PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"

echo "🔍 ERROR 520 FIXES VALIDATION"
echo "=============================="
echo "Domain: $PRODUCTION_DOMAIN"
echo "App Directory: $APP_DIR"
echo "Date: $(date)"
echo

# Test 1: Validate ecosystem.config.js has 1 instance
log_info "Test 1: Validating ecosystem.config.js instance configuration..."
if [[ -f "$APP_DIR/ecosystem.config.js" ]]; then
    if grep -q "instances: 1" "$APP_DIR/ecosystem.config.js"; then
        log_success "✅ ecosystem.config.js configured for 1 instance (Error 520 fix applied)"
    else
        log_error "❌ ecosystem.config.js still uses multiple instances (Error 520 risk)"
        echo "Expected: instances: 1"
        echo "Found: $(grep -n "instances:" "$APP_DIR/ecosystem.config.js" || echo "instances line not found")"
    fi
else
    log_error "❌ ecosystem.config.js not found at $APP_DIR"
fi

# Test 2: Validate PM2 is running with 1 instance
log_info "Test 2: Validating PM2 process configuration..."
if command -v pm2 >/dev/null 2>&1; then
    if pm2 list | grep -q "hauling-qr-server"; then
        local instance_count=$(pm2 list | grep "hauling-qr-server" | wc -l)
        if [[ $instance_count -eq 1 ]]; then
            log_success "✅ PM2 running with 1 instance (Error 520 prevention active)"
        else
            log_error "❌ PM2 running with $instance_count instances (Error 520 risk)"
        fi
    else
        log_warning "⚠️ hauling-qr-server not found in PM2 (may not be started yet)"
    fi
else
    log_error "❌ PM2 not installed or not in PATH"
fi

# Test 3: Validate port 8080 is listening
log_info "Test 3: Validating port 8080 is listening..."
if netstat -tuln | grep -q ":8080 "; then
    log_success "✅ Port 8080 is listening (Error 520 prevention confirmed)"
else
    log_error "❌ Port 8080 is not listening (Error 520 risk)"
fi

# Test 4: Validate backend health endpoint
log_info "Test 4: Validating backend health endpoint..."
if curl -f -s http://localhost:8080/api/health >/dev/null 2>&1; then
    log_success "✅ Backend health endpoint responding (Error 520 prevention confirmed)"
else
    log_warning "⚠️ Backend health endpoint not responding (may cause issues)"
fi

# Test 5: Validate PM2 environment variables
log_info "Test 5: Validating PM2 environment variables..."
if pm2 show hauling-qr-server 2>/dev/null | grep -q "NGINX_PROXY_MODE.*true"; then
    log_success "✅ NGINX_PROXY_MODE=true set in PM2 (CORS fix active)"
else
    log_error "❌ NGINX_PROXY_MODE not set correctly (CORS issues possible)"
fi

# Test 6: Validate NGINX configuration
log_info "Test 6: Validating NGINX configuration..."
if [[ -f "/etc/nginx/sites-available/hauling-qr-system" ]]; then
    if nginx -t >/dev/null 2>&1; then
        log_success "✅ NGINX configuration is valid"
    else
        log_error "❌ NGINX configuration has errors"
    fi
else
    log_error "❌ NGINX site configuration not found"
fi

# Test 7: Validate deployment script fixes
log_info "Test 7: Validating deployment script fixes..."
local pm2_script="deploy-hauling-qr-ubuntu/6_install-pm2.sh"
if [[ -f "$pm2_script" ]]; then
    if grep -q "instances: 1" "$pm2_script"; then
        log_success "✅ PM2 deployment script has Error 520 fix"
    else
        log_error "❌ PM2 deployment script still uses multiple instances"
    fi
else
    log_warning "⚠️ PM2 deployment script not found (may be in different location)"
fi

echo
echo "🏁 VALIDATION SUMMARY"
echo "===================="
echo "✅ = Fix properly applied"
echo "❌ = Issue detected (needs attention)"
echo "⚠️ = Warning (may need investigation)"
echo
echo "If all tests show ✅, Error 520 fixes are properly integrated."
echo "If any tests show ❌, manual intervention may be required."
