# Implementation Plan

- [x] 1. Create database migration for role permissions





  - **CREATE**: `database/migrations/017_role_based_access_control.sql`
    - Create role_permissions table with role_name and page_key columns
    - Add PostgreSQL function to add new values to user_role enum
    - Insert default permissions for existing roles (admin gets all access)
  - _Requirements: 7.1, 7.6_

- [x] 2. Create backend API for role and permission management



  - **CREATE**: `server/routes/roles.js`
    - Add GET /api/roles endpoint to get all user_role enum values
    - Add POST /api/roles endpoint to add new role to enum
    - Add DELETE /api/roles/:name endpoint to remove role
  - **CREATE**: `server/routes/permissions.js`
    - Add GET /api/permissions endpoint to get all role-page permissions
    - Add POST /api/permissions endpoint to bulk update permissions
  - **MODIFY**: `server/server.js` (add new route imports)
  - _Requirements: 5.1, 5.4, 5.5, 2.1, 3.1, 3.2_

- [x] 3. Create UserRolesManagement component for Settings page



  - **CREATE**: `client/src/pages/settings/components/UserRolesManagement.js`
    - Build single component with role CRUD and permission matrix
    - Add role creation/deletion with user count validation
    - Create checkbox matrix for role-page permissions
  - **MODIFY**: `client/src/pages/settings/Settings.js`
    - Add UserRolesManagement to settingsMenus array
    - Import and integrate new component
  - _Requirements: 1.1, 1.2, 1.5, 2.2, 2.3, 2.4, 5.2, 5.3_

- [ ] 4. Implement permission checking and route protection
  - **CREATE**: `client/src/hooks/usePermissions.js`
    - Add permission checking hook for frontend
  - **CREATE**: `server/middleware/permissions.js`
    - Create middleware for backend route protection
  - **MODIFY**: `client/src/components/layout/Sidebar.js`
    - Hide navigation items based on user permissions
  - **MODIFY**: `client/src/components/layout/DashboardLayout.js`
    - Add route protection to existing routes
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 6.1, 6.2, 6.3_

- [ ] 5. Test and integrate with existing authentication system
  - **MODIFY**: `server/routes/auth.js`
    - Include user permissions in login response
  - **MODIFY**: `client/src/context/AuthContext.js`
    - Add permission checking to auth context
  - **TEST**: Create test scenarios for role CRUD and permission enforcement
  - _Requirements: 1.4, 4.5, 4.6, 6.4, 6.5, 6.6_