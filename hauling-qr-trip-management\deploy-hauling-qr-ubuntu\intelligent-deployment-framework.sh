#!/bin/bash

# =============================================================================
# INTELLIGENT DEPLOYMENT FRAMEWORK - SHARED LIBRARY
# =============================================================================
# Version: 1.0.0 - Comprehensive intelligent deployment patterns
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# Description: Shared intelligent deployment functions for all deployment scripts
# =============================================================================

# =============================================================================
# INTELLIGENT DEPLOYMENT CONFIGURATION
# =============================================================================

# Progress tracking configuration
readonly PROGRESS_DIR="/var/log/hauling-qr-deployment"
readonly PROGRESS_FILE="${PROGRESS_DIR}/deployment-progress.json"
readonly RETRY_FILE="${PROGRESS_DIR}/retry-attempts.json"
readonly FAILED_COMPONENTS_FILE="${PROGRESS_DIR}/failed-components.json"
readonly DEPLOYMENT_STATE_FILE="${PROGRESS_DIR}/deployment-state.json"

# ENHANCED: Health monitoring configuration
readonly HEALTH_CHECK_FILE="${PROGRESS_DIR}/deployment-health.json"
readonly PHASE_MARKERS_DIR="${PROGRESS_DIR}/phase-markers"
readonly DEPLOYMENT_LOCK_FILE="${PROGRESS_DIR}/deployment.lock"

# Retry configuration
readonly MAX_ESSENTIAL_RETRIES=5
readonly MAX_OPTIONAL_RETRIES=3
readonly BASE_RETRY_DELAY=5
readonly MAX_RETRY_DELAY=60

# OPTIMIZED: Timeout configuration for sub-15-minute deployment
readonly DEFAULT_COMPONENT_TIMEOUT=600   # 10 minutes (reduced from 30)
readonly CRITICAL_COMPONENT_TIMEOUT=720  # 12 minutes (reduced from 40)
readonly OPTIONAL_COMPONENT_TIMEOUT=300  # 5 minutes (reduced from 15)

# ENHANCED: Performance monitoring configuration
readonly PERFORMANCE_LOG_FILE="${PROGRESS_DIR}/deployment-performance.log"
readonly TIMING_DATA_FILE="${PROGRESS_DIR}/deployment-timing.json"

# Performance timing storage - Initialize arrays early
declare -A PERFORMANCE_TIMERS
declare -A PERFORMANCE_START_TIMES

# =============================================================================
# INTELLIGENT PROGRESS TRACKING SYSTEM
# =============================================================================

init_intelligent_progress_tracking() {
  local component_name="$1"
  local component_type="${2:-optional}"  # essential|optional
  
  log_info "🔧 Initializing intelligent progress tracking for $component_name..."
  
  # Ensure progress directory exists
  sudo mkdir -p "$PROGRESS_DIR" 2>/dev/null || true
  sudo chmod 755 "$PROGRESS_DIR" 2>/dev/null || true
  
  # Initialize progress tracking files
  if [[ ! -f "$PROGRESS_FILE" ]]; then
    echo '{"completed_components": [], "failed_components": [], "current_component": "", "start_time": "", "last_update": ""}' | sudo tee "$PROGRESS_FILE" >/dev/null
  fi
  
  if [[ ! -f "$RETRY_FILE" ]]; then
    echo '{}' | sudo tee "$RETRY_FILE" >/dev/null
  fi
  
  if [[ ! -f "$FAILED_COMPONENTS_FILE" ]]; then
    echo '{"failed": [], "skipped": [], "recovered": []}' | sudo tee "$FAILED_COMPONENTS_FILE" >/dev/null
  fi
  
  if [[ ! -f "$DEPLOYMENT_STATE_FILE" ]]; then
    echo '{"phase": "initialization", "status": "in_progress", "components": {}}' | sudo tee "$DEPLOYMENT_STATE_FILE" >/dev/null
  fi
  
  # Update current component
  update_deployment_progress "$component_name" "started" "$component_type"
  
  log_success "✅ Intelligent progress tracking initialized for $component_name"
  return 0
}

update_deployment_progress() {
  local component_name="$1"
  local status="$2"  # started|completed|failed|skipped
  local component_type="${3:-optional}"
  local error_message="${4:-}"
  
  local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
  
  # Update progress using Python for reliable JSON manipulation
  python3 -c "
import json
import sys
from datetime import datetime

try:
    # Read current progress
    with open('$PROGRESS_FILE', 'r') as f:
        progress = json.load(f)
    
    # Update progress
    progress['current_component'] = '$component_name'
    progress['last_update'] = '$timestamp'
    
    if '$status' == 'started':
        progress['start_time'] = progress.get('start_time', '$timestamp')
    elif '$status' == 'completed':
        if '$component_name' not in progress['completed_components']:
            progress['completed_components'].append('$component_name')
        # Remove from failed if it was there
        progress['failed_components'] = [c for c in progress['failed_components'] if c != '$component_name']
    elif '$status' == 'failed':
        if '$component_name' not in progress['failed_components']:
            progress['failed_components'].append('$component_name')
    
    # Write updated progress
    with open('$PROGRESS_FILE', 'w') as f:
        json.dump(progress, f, indent=2)
    
    # Update deployment state
    with open('$DEPLOYMENT_STATE_FILE', 'r') as f:
        state = json.load(f)
    
    state['components']['$component_name'] = {
        'status': '$status',
        'type': '$component_type',
        'timestamp': '$timestamp',
        'error': '$error_message' if '$error_message' else None
    }
    
    with open('$DEPLOYMENT_STATE_FILE', 'w') as f:
        json.dump(state, f, indent=2)
        
except Exception as e:
    print(f'Error updating progress: {e}', file=sys.stderr)
    sys.exit(1)
" 2>/dev/null || true
  
  return 0
}

# =============================================================================
# ENHANCED DEPLOYMENT HEALTH MONITORING
# =============================================================================

init_deployment_health_monitoring() {
  local deployment_id="${1:-$(date +%Y%m%d-%H%M%S)}"

  log_info "🏥 Initializing deployment health monitoring..."

  # Ensure health monitoring directory exists
  sudo mkdir -p "$PHASE_MARKERS_DIR" 2>/dev/null || true
  sudo chmod 755 "$PHASE_MARKERS_DIR" 2>/dev/null || true

  # Initialize health check file
  local health_data="{
    \"deployment_id\": \"$deployment_id\",
    \"start_time\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",
    \"last_heartbeat\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",
    \"current_phase\": \"initialization\",
    \"health_status\": \"healthy\",
    \"resource_usage\": {
      \"memory_mb\": $(free -m | awk 'NR==2{printf "%.0f", $3}'),
      \"disk_usage_percent\": $(df / | awk 'NR==2{printf "%.0f", $5}' | sed 's/%//'),
      \"load_average\": \"$(uptime | awk -F'load average:' '{print $2}' | xargs)\"
    },
    \"warnings\": [],
    \"errors\": []
  }"

  echo "$health_data" | sudo tee "$HEALTH_CHECK_FILE" >/dev/null

  # Create deployment lock
  echo "$deployment_id" | sudo tee "$DEPLOYMENT_LOCK_FILE" >/dev/null

  log_success "✅ Deployment health monitoring initialized (ID: $deployment_id)"
  return 0
}

update_deployment_health() {
  local phase="$1"
  local status="${2:-healthy}"  # healthy|warning|error
  local message="${3:-}"

  if [[ ! -f "$HEALTH_CHECK_FILE" ]]; then
    log_warning "⚠️ Health check file not found, initializing..."
    init_deployment_health_monitoring
  fi

  # Update health status using Python for reliable JSON manipulation
  python3 -c "
import json
import sys
from datetime import datetime

try:
    with open('$HEALTH_CHECK_FILE', 'r') as f:
        health = json.load(f)

    # Update basic info
    health['last_heartbeat'] = datetime.utcnow().isoformat() + 'Z'
    health['current_phase'] = '$phase'
    health['health_status'] = '$status'

    # Update resource usage
    import subprocess
    try:
        memory_mb = int(subprocess.check_output(['free', '-m']).decode().split('\n')[1].split()[2])
        health['resource_usage']['memory_mb'] = memory_mb
    except:
        pass

    try:
        disk_output = subprocess.check_output(['df', '/']).decode().split('\n')[1].split()
        disk_percent = int(disk_output[4].replace('%', ''))
        health['resource_usage']['disk_usage_percent'] = disk_percent
    except:
        pass

    # Add message if provided
    if '$message':
        if '$status' == 'warning' and '$message' not in health.get('warnings', []):
            health.setdefault('warnings', []).append('$message')
        elif '$status' == 'error' and '$message' not in health.get('errors', []):
            health.setdefault('errors', []).append('$message')

    with open('$HEALTH_CHECK_FILE', 'w') as f:
        json.dump(health, f, indent=2)

except Exception as e:
    print(f'Error updating health: {e}', file=sys.stderr)
    sys.exit(1)
" 2>/dev/null || true

  return 0
}

check_deployment_health() {
  local max_memory_mb="${1:-6000}"  # 6GB default limit
  local max_disk_percent="${2:-85}" # 85% disk usage limit

  if [[ ! -f "$HEALTH_CHECK_FILE" ]]; then
    log_warning "⚠️ No health data available"
    return 1
  fi

  # Check resource usage and deployment health
  local health_status=$(python3 -c "
import json
import sys
from datetime import datetime, timedelta

try:
    with open('$HEALTH_CHECK_FILE', 'r') as f:
        health = json.load(f)

    issues = []

    # Check memory usage
    memory_mb = health.get('resource_usage', {}).get('memory_mb', 0)
    if memory_mb > $max_memory_mb:
        issues.append(f'High memory usage: {memory_mb}MB > ${max_memory_mb}MB')

    # Check disk usage
    disk_percent = health.get('resource_usage', {}).get('disk_usage_percent', 0)
    if disk_percent > $max_disk_percent:
        issues.append(f'High disk usage: {disk_percent}% > ${max_disk_percent}%')

    # Check last heartbeat (should be within 5 minutes)
    last_heartbeat = datetime.fromisoformat(health['last_heartbeat'].replace('Z', '+00:00'))
    if datetime.now(last_heartbeat.tzinfo) - last_heartbeat > timedelta(minutes=5):
        issues.append('Deployment heartbeat is stale (>5 minutes)')

    # Check for errors
    errors = health.get('errors', [])
    if errors:
        issues.extend([f'Error: {error}' for error in errors[-3:]])  # Last 3 errors

    if issues:
        print('unhealthy')
        for issue in issues:
            print(f'ISSUE: {issue}', file=sys.stderr)
    else:
        print('healthy')

except Exception as e:
    print('unknown')
    print(f'Health check error: {e}', file=sys.stderr)
" 2>/dev/null)

  echo "$health_status"
  return 0
}

create_phase_marker() {
  local phase_name="$1"
  local status="${2:-started}"  # started|completed|failed

  local marker_file="${PHASE_MARKERS_DIR}/${phase_name}-${status}-$(date +%Y%m%d-%H%M%S)"
  echo "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" | sudo tee "$marker_file" >/dev/null

  # Update health monitoring
  update_deployment_health "$phase_name" "healthy" "Phase $phase_name $status"

  return 0
}

cleanup_deployment_health() {
  log_info "🧹 Cleaning up deployment health monitoring..."

  # Remove deployment lock
  sudo rm -f "$DEPLOYMENT_LOCK_FILE" 2>/dev/null || true

  # Archive health data
  if [[ -f "$HEALTH_CHECK_FILE" ]]; then
    local archive_file="${PROGRESS_DIR}/health-archive-$(date +%Y%m%d-%H%M%S).json"
    sudo mv "$HEALTH_CHECK_FILE" "$archive_file" 2>/dev/null || true
  fi

  log_success "✅ Deployment health monitoring cleaned up"
  return 0
}

# =============================================================================
# ENHANCED PERFORMANCE MONITORING AND LOGGING
# =============================================================================

init_performance_monitoring() {
  log_info "📊 Initializing performance monitoring..."

  # Create performance log file
  sudo mkdir -p "$(dirname "$PERFORMANCE_LOG_FILE")" 2>/dev/null || true

  # Initialize timing data
  local timing_data="{
    \"deployment_start\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",
    \"phases\": {},
    \"components\": {},
    \"total_duration_seconds\": 0,
    \"resource_snapshots\": []
  }"

  echo "$timing_data" | sudo tee "$TIMING_DATA_FILE" >/dev/null

  # Log initial system state
  log_performance_snapshot "deployment-start"

  log_success "✅ Performance monitoring initialized"
  return 0
}

log_performance_snapshot() {
  local event_name="$1"
  local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

  # Gather system metrics
  local memory_usage=$(free -m | awk 'NR==2{printf "%.1f", $3/1024}')
  local memory_total=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
  local disk_usage=$(df / | awk 'NR==2{printf "%.1f", $5}' | sed 's/%//')
  local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
  local cpu_count=$(nproc)

  # Log to performance file
  echo "[$timestamp] $event_name | Memory: ${memory_usage}GB/${memory_total}GB | Disk: ${disk_usage}% | Load: ${load_avg}/${cpu_count} CPUs" | sudo tee -a "$PERFORMANCE_LOG_FILE" >/dev/null

  return 0
}

start_component_timing() {
  local component_name="$1"
  local start_time=$(date +%s)

  # Log component start
  log_performance_snapshot "component-start-$component_name"

  # Store start time in a simple way
  echo "$start_time" > "/tmp/timing_${component_name}_start" 2>/dev/null || true

  return 0
}

end_component_timing() {
  local component_name="$1"
  local status="${2:-completed}"  # completed|failed
  local end_time=$(date +%s)

  # Log component end
  log_performance_snapshot "component-end-$component_name"

  # Calculate duration
  local start_time_file="/tmp/timing_${component_name}_start"
  if [[ -f "$start_time_file" ]]; then
    local start_time=$(cat "$start_time_file" 2>/dev/null || echo "$end_time")
    local duration=$((end_time - start_time))

    # Log timing info
    local mins=$((duration / 60))
    local secs=$((duration % 60))
    echo "[$component_name] Duration: ${mins}m ${secs}s | Status: $status" | sudo tee -a "$PERFORMANCE_LOG_FILE" >/dev/null

    # Cleanup
    rm -f "$start_time_file" 2>/dev/null || true
  fi

  return 0
}

get_retry_count() {
  local component_name="$1"
  
  local retry_count=$(python3 -c "
import json
import sys

try:
    with open('$RETRY_FILE', 'r') as f:
        retries = json.load(f)
    print(retries.get('$component_name', 0))
except:
    print(0)
" 2>/dev/null || echo "0")
  
  echo "$retry_count"
}

increment_retry_count() {
  local component_name="$1"
  
  python3 -c "
import json
import sys

try:
    with open('$RETRY_FILE', 'r') as f:
        retries = json.load(f)
    
    retries['$component_name'] = retries.get('$component_name', 0) + 1
    
    with open('$RETRY_FILE', 'w') as f:
        json.dump(retries, f, indent=2)
        
except Exception as e:
    print(f'Error updating retry count: {e}', file=sys.stderr)
    sys.exit(1)
" 2>/dev/null || true
  
  return 0
}

# =============================================================================
# INTELLIGENT RETRY SYSTEM
# =============================================================================

intelligent_retry_wrapper() {
  local component_name="$1"
  local component_type="$2"  # essential|optional
  local max_retries="$3"
  local timeout_seconds="$4"
  shift 4
  local command_to_run=("$@")
  
  local current_retry=0
  local retry_delay="$BASE_RETRY_DELAY"
  
  log_info "🔄 Starting intelligent retry for $component_name (type: $component_type)"
  log_info "📊 Max retries: $max_retries, Timeout: ${timeout_seconds}s"
  
  while [[ $current_retry -le $max_retries ]]; do
    current_retry=$((current_retry + 1))
    
    if [[ $current_retry -gt 1 ]]; then
      log_info "🔄 Retry attempt $current_retry/$((max_retries + 1)) for $component_name"
      log_info "⏳ Waiting ${retry_delay}s before retry..."
      sleep "$retry_delay"
      
      # Exponential backoff with jitter
      retry_delay=$((retry_delay * 2))
      if [[ $retry_delay -gt $MAX_RETRY_DELAY ]]; then
        retry_delay=$MAX_RETRY_DELAY
      fi
      # Add jitter (±25%)
      local jitter=$((retry_delay / 4))
      retry_delay=$((retry_delay + (RANDOM % (jitter * 2)) - jitter))
    fi
    
    log_info "🚀 Executing $component_name (attempt $current_retry/$((max_retries + 1)))"
    
    # Execute command with segmentation fault prevention
    local start_time=$(date +%s)
    local exit_code=0

    log_info "🔧 Command: ${command_to_run[*]}"

    # Special handling for install-system-dependencies to avoid segfaults and add completion detection
    if [[ "$component_name" == "install-system-dependencies" || "$component_name" == "1_install-system-dependencies" ]]; then
      log_info "🔧 Using enhanced execution for install-system-dependencies (segfault prevention + completion detection)"
      set +e  # Temporarily disable exit on error

      # Execute the command in background to allow monitoring
      "${command_to_run[@]}" &
      local cmd_pid=$!

      # Monitor for completion markers while command runs
      local monitor_timeout=$((timeout_seconds - 30))  # Reserve 30s for cleanup
      local check_interval=5
      local elapsed=0

      while [[ $elapsed -lt $monitor_timeout ]]; do
        # Check if process is still running
        if ! kill -0 "$cmd_pid" 2>/dev/null; then
          # Process finished, get exit code
          wait "$cmd_pid"
          exit_code=$?
          break
        fi

        # Check for completion markers
        if ls "${PROGRESS_DIR}"/phase-1-completed-* >/dev/null 2>&1; then
          log_info "🎯 Phase completion marker detected - process completing successfully"
          wait "$cmd_pid"
          exit_code=$?
          break
        fi

        # Check for failure markers
        if ls "${PROGRESS_DIR}"/phase-1-failed-* >/dev/null 2>&1; then
          log_warning "⚠️ Phase failure marker detected - process failed"
          wait "$cmd_pid"
          exit_code=$?
          break
        fi

        sleep "$check_interval"
        elapsed=$((elapsed + check_interval))

        # Log progress every 30 seconds
        if [[ $((elapsed % 30)) -eq 0 ]]; then
          log_info "⏳ System dependencies installation in progress... (${elapsed}s elapsed)"
        fi
      done

      # If we timed out, kill the process
      if [[ $elapsed -ge $monitor_timeout ]] && kill -0 "$cmd_pid" 2>/dev/null; then
        log_warning "⏰ System dependencies installation timed out, terminating process..."
        kill -TERM "$cmd_pid" 2>/dev/null || true
        sleep 5
        kill -KILL "$cmd_pid" 2>/dev/null || true
        wait "$cmd_pid" 2>/dev/null || true
        exit_code=124  # Timeout exit code
      fi

      set -e  # Re-enable exit on error
    else
      # Use alternative execution method to prevent segmentation faults
      # Create a temporary script to avoid timeout array expansion issues
      local temp_script="/tmp/deploy_cmd_$$.sh"

      # Write command to temporary script
      {
        echo "#!/bin/bash"
        echo "set -euo pipefail"
        printf '%q ' "${command_to_run[@]}"
        echo ""
      } > "$temp_script"

      chmod +x "$temp_script"

      # Execute with proper error handling using script instead of array
      set +e  # Temporarily disable exit on error
      timeout "$timeout_seconds" bash "$temp_script"
      exit_code=$?
      set -e  # Re-enable exit on error

      # Clean up temporary script
      rm -f "$temp_script" 2>/dev/null || true
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    if [[ $exit_code -eq 0 ]]; then
      log_success "✅ $component_name completed successfully in ${duration}s"
      update_deployment_progress "$component_name" "completed" "$component_type"
      return 0
    else
      increment_retry_count "$component_name"

      # Handle specific error codes
      if [[ $exit_code -eq 139 ]]; then
        log_error "💥 SEGMENTATION FAULT detected in $component_name (exit code 139)"
        log_error "This indicates memory corruption or system instability"
        log_info "🔧 Attempting recovery with alternative execution method..."
      elif [[ $exit_code -eq 124 ]]; then
        log_warning "⏰ $component_name timed out after ${timeout_seconds}s (attempt $current_retry)"
      else
        log_warning "❌ $component_name failed with exit code $exit_code after ${duration}s (attempt $current_retry)"
      fi
      
      # For essential components, continue retrying
      if [[ "$component_type" == "essential" && $current_retry -le $max_retries ]]; then
        log_info "🔧 Essential component failed, preparing for retry..."
        continue
      fi
      
      # For optional components or max retries reached
      if [[ $current_retry -gt $max_retries ]]; then
        log_error "💥 $component_name failed after $((max_retries + 1)) attempts"
        update_deployment_progress "$component_name" "failed" "$component_type" "Max retries exceeded"
        
        if [[ "$component_type" == "essential" ]]; then
          log_error "🚨 Essential component $component_name failed - deployment cannot continue"
          return 1
        else
          log_warning "⚠️ Optional component $component_name failed - continuing deployment"
          return 2  # Special exit code for optional component failure
        fi
      fi
    fi
  done
  
  return 1
}

# =============================================================================
# INTELLIGENT PACKAGE MANAGEMENT SYSTEM
# =============================================================================

intelligent_package_cleanup() {
  log_info "🧹 Performing intelligent package cleanup..."

  # Check for hanging package manager processes
  local hanging_pids=$(pgrep -f "apt-get\|dpkg\|apt\|aptitude" 2>/dev/null || echo "")
  if [[ -n "$hanging_pids" ]]; then
    log_warning "Found hanging package manager processes: $hanging_pids"
    log_info "Terminating hanging processes..."

    # Try graceful termination first
    for pid in $hanging_pids; do
      if sudo kill -TERM "$pid" 2>/dev/null; then
        log_info "Sent TERM signal to process $pid"
        sleep 2
      fi
    done

    # Force kill if still running
    sleep 3
    for pid in $hanging_pids; do
      if kill -0 "$pid" 2>/dev/null; then
        if sudo kill -KILL "$pid" 2>/dev/null; then
          log_warning "Force killed hanging process $pid"
        fi
      fi
    done
  fi

  # Check and remove lock files if no processes are using them
  local lock_files=(
    "/var/lib/dpkg/lock-frontend"
    "/var/lib/dpkg/lock"
    "/var/cache/apt/archives/lock"
    "/var/lib/apt/lists/lock"
  )

  for lock_file in "${lock_files[@]}"; do
    if [[ -f "$lock_file" ]]; then
      # Check if any process is actually using the lock
      local lock_pid=$(lsof "$lock_file" 2>/dev/null | awk 'NR>1 {print $2}' | head -1)
      if [[ -z "$lock_pid" ]]; then
        log_info "Removing stale lock file: $lock_file"
        sudo rm -f "$lock_file" 2>/dev/null || true
      else
        log_info "Lock file $lock_file is in use by process $lock_pid"
      fi
    fi
  done

  # Configure dpkg if needed
  sudo dpkg --configure -a >/dev/null 2>&1 || true

  # Fix broken packages
  sudo apt-get -f install -y >/dev/null 2>&1 || true

  log_success "✅ Package cleanup completed"
  return 0
}

intelligent_package_update() {
  local max_attempts="${1:-3}"
  local component_name="${2:-package-update}"

  log_info "📦 Performing intelligent package list update..."

  local attempt=1
  while [[ $attempt -le $max_attempts ]]; do
    log_info "Package update attempt $attempt/$max_attempts"

    # Clean up before each attempt
    intelligent_package_cleanup

    if timeout 120 sudo apt-get update -qq 2>/dev/null; then
      log_success "✅ Package lists updated successfully"
      return 0
    else
      log_warning "Package update attempt $attempt failed"
      if [[ $attempt -lt $max_attempts ]]; then
        local wait_time=$((attempt * 5))
        log_info "Waiting ${wait_time}s before next attempt..."
        sleep "$wait_time"
      fi
    fi

    attempt=$((attempt + 1))
  done

  log_warning "⚠️ Package update failed after $max_attempts attempts, continuing anyway..."
  return 1
}

intelligent_package_install() {
  local packages=("$@")
  local last_arg="${!#}"
  local component_type="optional"

  # Check if last argument is component type
  if [[ "$last_arg" == "essential" || "$last_arg" == "optional" ]]; then
    component_type="$last_arg"
    # Remove component type from packages array
    unset 'packages[-1]'
  fi

  if [[ ${#packages[@]} -eq 0 ]]; then
    log_error "No packages specified for installation"
    return 1
  fi

  log_info "📦 Installing packages with intelligent retry: ${packages[*]}"
  log_info "Component type: $component_type"

  local max_attempts=3
  if [[ "$component_type" == "essential" ]]; then
    max_attempts=5
  fi

  local attempt=1
  while [[ $attempt -le $max_attempts ]]; do
    log_info "Package installation attempt $attempt/$max_attempts"

    # Clean up before each attempt
    intelligent_package_cleanup

    # Try to install packages
    if timeout 300 sudo apt-get install -y "${packages[@]}" 2>/dev/null; then
      log_success "✅ Packages installed successfully: ${packages[*]}"
      return 0
    else
      local exit_code=$?
      log_warning "Package installation attempt $attempt failed (exit code: $exit_code)"

      if [[ $attempt -lt $max_attempts ]]; then
        local wait_time=$((attempt * 10))
        log_info "Waiting ${wait_time}s before next attempt..."
        sleep "$wait_time"
      fi
    fi

    attempt=$((attempt + 1))
  done

  if [[ "$component_type" == "essential" ]]; then
    log_error "❌ Essential packages failed to install: ${packages[*]}"
    return 1
  else
    log_warning "⚠️ Optional packages failed to install: ${packages[*]}"
    return 2
  fi
}

# =============================================================================
# INTELLIGENT SERVICE MANAGEMENT
# =============================================================================

intelligent_service_management() {
  local service_name="$1"
  local action="$2"  # start|stop|restart|enable|disable|status
  local component_type="${3:-optional}"
  local max_attempts="${4:-3}"

  log_info "🔧 Managing service $service_name: $action"

  local attempt=1
  while [[ $attempt -le $max_attempts ]]; do
    log_info "Service $action attempt $attempt/$max_attempts for $service_name"

    case "$action" in
      "start"|"restart")
        if sudo systemctl "$action" "$service_name" 2>/dev/null; then
          # Verify service is actually running
          sleep 2
          if sudo systemctl is-active "$service_name" >/dev/null 2>&1; then
            log_success "✅ Service $service_name $action successful"
            return 0
          else
            log_warning "Service $service_name $action command succeeded but service not active"
          fi
        fi
        ;;
      "stop")
        if sudo systemctl stop "$service_name" 2>/dev/null; then
          # Verify service is actually stopped
          sleep 2
          if ! sudo systemctl is-active "$service_name" >/dev/null 2>&1; then
            log_success "✅ Service $service_name stopped successfully"
            return 0
          else
            log_warning "Service $service_name stop command succeeded but service still active"
          fi
        fi
        ;;
      "enable"|"disable")
        if sudo systemctl "$action" "$service_name" 2>/dev/null; then
          log_success "✅ Service $service_name $action successful"
          return 0
        fi
        ;;
      "status")
        if sudo systemctl status "$service_name" >/dev/null 2>&1; then
          log_success "✅ Service $service_name is active"
          return 0
        else
          log_info "Service $service_name is not active"
          return 1
        fi
        ;;
    esac

    if [[ $attempt -lt $max_attempts ]]; then
      local wait_time=$((attempt * 5))
      log_warning "Service $action failed, waiting ${wait_time}s before retry..."
      sleep "$wait_time"
    fi

    attempt=$((attempt + 1))
  done

  if [[ "$component_type" == "essential" ]]; then
    log_error "❌ Essential service $service_name $action failed after $max_attempts attempts"
    return 1
  else
    log_warning "⚠️ Optional service $service_name $action failed after $max_attempts attempts"
    return 2
  fi
}

# =============================================================================
# INTELLIGENT DEPLOYMENT STATUS AND RECOVERY
# =============================================================================

show_deployment_status() {
  local component_filter="${1:-all}"  # all|failed|completed|in_progress

  echo ""
  echo "🔍 INTELLIGENT DEPLOYMENT STATUS REPORT"
  echo "========================================"

  if [[ ! -f "$PROGRESS_FILE" ]]; then
    echo "❌ No deployment progress found"
    return 1
  fi

  # Use Python to parse and display status
  python3 -c "
import json
import sys
from datetime import datetime

try:
    with open('$PROGRESS_FILE', 'r') as f:
        progress = json.load(f)

    with open('$DEPLOYMENT_STATE_FILE', 'r') as f:
        state = json.load(f)

    print(f\"📊 Deployment Phase: {state.get('phase', 'unknown')}\")
    print(f\"🔄 Current Status: {state.get('status', 'unknown')}\")
    print(f\"🎯 Current Component: {progress.get('current_component', 'none')}\")
    print(f\"⏰ Last Update: {progress.get('last_update', 'unknown')}\")
    print()

    completed = progress.get('completed_components', [])
    failed = progress.get('failed_components', [])
    components = state.get('components', {})

    if '$component_filter' in ['all', 'completed'] and completed:
        print('✅ COMPLETED COMPONENTS:')
        for comp in completed:
            comp_info = components.get(comp, {})
            comp_type = comp_info.get('type', 'unknown')
            timestamp = comp_info.get('timestamp', 'unknown')
            print(f\"   • {comp} ({comp_type}) - {timestamp}\")
        print()

    if '$component_filter' in ['all', 'failed'] and failed:
        print('❌ FAILED COMPONENTS:')
        for comp in failed:
            comp_info = components.get(comp, {})
            comp_type = comp_info.get('type', 'unknown')
            error = comp_info.get('error', 'unknown error')
            timestamp = comp_info.get('timestamp', 'unknown')
            print(f\"   • {comp} ({comp_type}) - {error} - {timestamp}\")
        print()

    if '$component_filter' in ['all', 'in_progress']:
        in_progress = []
        for comp, info in components.items():
            if info.get('status') == 'started' and comp not in completed and comp not in failed:
                in_progress.append((comp, info))

        if in_progress:
            print('🔄 IN PROGRESS COMPONENTS:')
            for comp, info in in_progress:
                comp_type = info.get('type', 'unknown')
                timestamp = info.get('timestamp', 'unknown')
                print(f\"   • {comp} ({comp_type}) - {timestamp}\")
            print()

    # Summary
    total_components = len(components)
    completed_count = len(completed)
    failed_count = len(failed)
    success_rate = (completed_count / total_components * 100) if total_components > 0 else 0

    print(f\"📈 SUMMARY: {completed_count}/{total_components} completed ({success_rate:.1f}% success rate)\")
    if failed_count > 0:
        print(f\"⚠️  {failed_count} components failed\")

except Exception as e:
    print(f'Error reading deployment status: {e}', file=sys.stderr)
    sys.exit(1)
"

  return 0
}

reset_deployment_progress() {
  local component_name="${1:-all}"

  if [[ "$component_name" == "all" ]]; then
    log_info "🔄 Resetting all deployment progress..."

    # Reset all progress files
    echo '{"completed_components": [], "failed_components": [], "current_component": "", "start_time": "", "last_update": ""}' | sudo tee "$PROGRESS_FILE" >/dev/null
    echo '{}' | sudo tee "$RETRY_FILE" >/dev/null
    echo '{"failed": [], "skipped": [], "recovered": []}' | sudo tee "$FAILED_COMPONENTS_FILE" >/dev/null
    echo '{"phase": "reset", "status": "ready", "components": {}}' | sudo tee "$DEPLOYMENT_STATE_FILE" >/dev/null

    log_success "✅ All deployment progress reset"
  else
    log_info "🔄 Resetting progress for component: $component_name"

    # Reset specific component using Python
    python3 -c "
import json
import sys

try:
    # Update progress file
    with open('$PROGRESS_FILE', 'r') as f:
        progress = json.load(f)

    # Remove from completed and failed lists
    progress['completed_components'] = [c for c in progress['completed_components'] if c != '$component_name']
    progress['failed_components'] = [c for c in progress['failed_components'] if c != '$component_name']

    with open('$PROGRESS_FILE', 'w') as f:
        json.dump(progress, f, indent=2)

    # Update deployment state
    with open('$DEPLOYMENT_STATE_FILE', 'r') as f:
        state = json.load(f)

    if '$component_name' in state.get('components', {}):
        del state['components']['$component_name']

    with open('$DEPLOYMENT_STATE_FILE', 'w') as f:
        json.dump(state, f, indent=2)

    # Reset retry count
    with open('$RETRY_FILE', 'r') as f:
        retries = json.load(f)

    if '$component_name' in retries:
        del retries['$component_name']

    with open('$RETRY_FILE', 'w') as f:
        json.dump(retries, f, indent=2)

except Exception as e:
    print(f'Error resetting component progress: {e}', file=sys.stderr)
    sys.exit(1)
" 2>/dev/null || true

    log_success "✅ Component $component_name progress reset"
  fi

  return 0
}

resume_deployment() {
  local target_component="${1:-}"

  log_info "🔄 Analyzing deployment state for resume..."

  if [[ ! -f "$PROGRESS_FILE" ]]; then
    log_error "❌ No deployment progress found - cannot resume"
    return 1
  fi

  # Show current status
  show_deployment_status

  if [[ -n "$target_component" ]]; then
    log_info "🎯 Preparing to resume from component: $target_component"
    # Reset the target component and all subsequent components
    reset_deployment_progress "$target_component"
  fi

  log_info "✅ Deployment ready for resume"
  return 0
}

# =============================================================================
# INTELLIGENT DEPLOYMENT COMMAND LINE INTERFACE
# =============================================================================

intelligent_deployment_cli() {
  local action="$1"
  local component="${2:-}"

  case "$action" in
    "status")
      show_deployment_status "$component"
      ;;
    "reset")
      reset_deployment_progress "$component"
      ;;
    "resume")
      resume_deployment "$component"
      ;;
    "failed")
      show_deployment_status "failed"
      ;;
    "completed")
      show_deployment_status "completed"
      ;;
    "help"|"--help"|"-h")
      echo "Intelligent Deployment Framework CLI"
      echo "Usage: $0 <action> [component]"
      echo ""
      echo "Actions:"
      echo "  status [filter]    - Show deployment status (all|failed|completed|in_progress)"
      echo "  reset [component]  - Reset deployment progress (all or specific component)"
      echo "  resume [component] - Resume deployment from specific component"
      echo "  failed            - Show only failed components"
      echo "  completed         - Show only completed components"
      echo "  help              - Show this help message"
      echo ""
      echo "Examples:"
      echo "  $0 status                    # Show all deployment status"
      echo "  $0 status failed            # Show only failed components"
      echo "  $0 reset install-nodejs     # Reset specific component"
      echo "  $0 reset all                # Reset all progress"
      echo "  $0 resume install-postgresql # Resume from PostgreSQL installation"
      ;;
    *)
      echo "❌ Unknown action: $action"
      echo "Use '$0 help' for usage information"
      return 1
      ;;
  esac
}

# =============================================================================
# ENHANCED PERFORMANCE MONITORING FUNCTIONS
# =============================================================================

# Start performance timer for a specific operation
start_performance_timer() {
  local timer_name="$1"
  local start_time=$(date +%s)

  PERFORMANCE_START_TIMES["$timer_name"]="$start_time"
  log_info "⏱️ Started timer: $timer_name"
}

# End performance timer and log duration
end_performance_timer() {
  local timer_name="$1"
  local end_time=$(date +%s)

  if [[ -n "${PERFORMANCE_START_TIMES[$timer_name]:-}" ]]; then
    local start_time="${PERFORMANCE_START_TIMES[$timer_name]}"
    local duration=$((end_time - start_time))

    PERFORMANCE_TIMERS["$timer_name"]="$duration"
    log_info "⏱️ $timer_name completed in ${duration}s"

    # Store in performance log
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $timer_name: ${duration}s" >> "$PERFORMANCE_LOG_FILE"

    # Clean up
    unset PERFORMANCE_START_TIMES["$timer_name"]
  else
    log_warning "⚠️ No start time found for timer: $timer_name"
  fi
}

# Generate comprehensive performance report
generate_performance_report() {
  log_info "📊 DEPLOYMENT PERFORMANCE REPORT:"

  # Calculate total deployment time
  local total_time=0
  for timer in "${!PERFORMANCE_TIMERS[@]}"; do
    local duration="${PERFORMANCE_TIMERS[$timer]}"
    total_time=$((total_time + duration))

    # Format duration for display
    local minutes=$((duration / 60))
    local seconds=$((duration % 60))
    if [[ $minutes -gt 0 ]]; then
      log_info "   • $timer: ${duration}s (${minutes}m ${seconds}s)"
    else
      log_info "   • $timer: ${duration}s"
    fi
  done

  # Display total time
  local total_minutes=$((total_time / 60))
  local total_seconds=$((total_time % 60))
  if [[ $total_minutes -gt 0 ]]; then
    log_info "   • TOTAL DEPLOYMENT TIME: ${total_time}s (${total_minutes}m ${total_seconds}s)"
  else
    log_info "   • TOTAL DEPLOYMENT TIME: ${total_time}s"
  fi
}

# Initialize markers system for intelligent deployment
init_markers_system() {
  log_info "🔧 Initializing intelligent deployment markers system..."

  # Create markers directory
  sudo mkdir -p "$PHASE_MARKERS_DIR" 2>/dev/null || true
  sudo chmod 755 "$PHASE_MARKERS_DIR" 2>/dev/null || true
  sudo chown ubuntu:ubuntu "$PHASE_MARKERS_DIR" 2>/dev/null || true

  # Initialize performance monitoring
  init_performance_monitoring

  log_info "✅ Intelligent deployment markers system initialized"
}
