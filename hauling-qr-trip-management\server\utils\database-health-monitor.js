/**
 * Database Health Monitoring Utilities
 * 
 * Provides comprehensive database health monitoring including:
 * - Connection pool monitoring
 * - Query performance tracking
 * - Index usage analysis
 * - Table statistics
 * - Connection recovery
 */

const { pool, query } = require('../config/database');

/**
 * Get comprehensive database health metrics
 * @returns {Object} Database health metrics
 */
async function getDatabaseHealthMetrics() {
  try {
    const startTime = Date.now();
    
    // Run all checks in parallel for efficiency
    const [
      connectionStatus,
      poolMetrics,
      tableStats,
      indexUsage,
      slowQueries,
      activeConnections
    ] = await Promise.all([
      checkConnectionStatus(),
      getPoolMetrics(),
      getTableStatistics(),
      getIndexUsageStatistics(),
      getSlowQueryStatistics(),
      getActiveConnections()
    ]);
    
    const responseTime = Date.now() - startTime;
    
    // Calculate overall health status
    const status = calculateOverallStatus({
      connectionStatus,
      poolMetrics,
      slowQueries
    });
    
    return {
      status,
      timestamp: new Date().toISOString(),
      response_time_ms: responseTime,
      connection: connectionStatus,
      pool: poolMetrics,
      tables: tableStats,
      indexes: indexUsage,
      queries: slowQueries,
      active_connections: activeConnections
    };
  } catch (error) {
    console.error('Error getting database health metrics:', error);
    return {
      status: 'critical',
      timestamp: new Date().toISOString(),
      error: error.message,
      error_stack: error.stack
    };
  }
}

/**
 * Check basic database connection status
 * @returns {Object} Connection status
 */
async function checkConnectionStatus() {
  try {
    const startTime = Date.now();
    const result = await query('SELECT version(), current_database(), current_user');
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'operational',
      response_time_ms: responseTime,
      version: result.rows[0].version,
      database: result.rows[0].current_database,
      user: result.rows[0].current_user,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'critical',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get connection pool metrics
 * @returns {Object} Pool metrics
 */
async function getPoolMetrics() {
  try {
    // Get pool statistics
    const poolStats = {
      total: pool.totalCount,
      idle: pool.idleCount,
      waiting: pool.waitingCount,
      max: pool.options.max
    };
    
    // Calculate utilization percentages
    const utilization = {
      total_percent: Math.round((poolStats.total / poolStats.max) * 100),
      idle_percent: poolStats.total > 0 ? Math.round((poolStats.idle / poolStats.total) * 100) : 0,
      active_percent: poolStats.total > 0 ? Math.round(((poolStats.total - poolStats.idle) / poolStats.total) * 100) : 0
    };
    
    // Determine status based on utilization
    let status = 'operational';
    const issues = [];
    
    if (utilization.total_percent > 90) {
      status = 'warning';
      issues.push({
        type: 'high_utilization',
        severity: 'medium',
        description: `Connection pool utilization is high (${utilization.total_percent}%)`
      });
    }
    
    if (poolStats.waiting > 0) {
      status = 'critical';
      issues.push({
        type: 'connection_waiting',
        severity: 'high',
        description: `${poolStats.waiting} clients waiting for database connections`
      });
    }
    
    return {
      status,
      stats: poolStats,
      utilization,
      issues,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'critical',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get table statistics
 * @returns {Object} Table statistics
 */
async function getTableStatistics() {
  try {
    const result = await query(`
      SELECT
        schemaname,
        relname as table_name,
        n_live_tup as row_count,
        n_dead_tup as dead_tuples,
        last_vacuum,
        last_autovacuum,
        last_analyze,
        last_autoanalyze
      FROM pg_stat_user_tables
      ORDER BY n_live_tup DESC
      LIMIT 20
    `);
    
    // Check for tables that need vacuuming
    const tablesNeedingMaintenance = result.rows.filter(table => {
      const deadRatio = table.row_count > 0 ? 
        (table.dead_tuples / table.row_count) : 0;
      
      // Tables with > 20% dead tuples need vacuuming
      return deadRatio > 0.2;
    });
    
    // Format the results
    const tables = result.rows.map(table => ({
      schema: table.schemaname,
      name: table.table_name,
      row_count: parseInt(table.row_count),
      dead_tuples: parseInt(table.dead_tuples),
      dead_ratio: table.row_count > 0 ? 
        ((table.dead_tuples / table.row_count) * 100).toFixed(2) + '%' : '0%',
      last_vacuum: table.last_vacuum,
      last_autovacuum: table.last_autovacuum,
      last_analyze: table.last_analyze,
      last_autoanalyze: table.last_autoanalyze
    }));
    
    // Determine status based on maintenance needs
    let status = 'operational';
    const issues = [];
    
    if (tablesNeedingMaintenance.length > 0) {
      status = 'warning';
      tablesNeedingMaintenance.forEach(table => {
        issues.push({
          type: 'needs_vacuum',
          severity: 'medium',
          description: `Table ${table.table_name} has high dead tuple ratio (${((table.dead_tuples / table.row_count) * 100).toFixed(2)}%)`
        });
      });
    }
    
    return {
      status,
      tables,
      issues,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'warning',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get index usage statistics
 * @returns {Object} Index usage statistics
 */
async function getIndexUsageStatistics() {
  try {
    const result = await query(`
      SELECT
        schemaname,
        relname as table_name,
        indexrelname as index_name,
        idx_scan as index_scans,
        idx_tup_read as tuples_read,
        idx_tup_fetch as tuples_fetched,
        pg_size_pretty(pg_relation_size(indexrelid)) as index_size
      FROM pg_stat_user_indexes
      ORDER BY idx_scan DESC
      LIMIT 20
    `);
    
    // Check for unused indexes
    const unusedIndexes = await query(`
      SELECT
        schemaname,
        relname as table_name,
        indexrelname as index_name,
        idx_scan as index_scans,
        pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
        pg_relation_size(indexrelid) as index_size_bytes
      FROM pg_stat_user_indexes
      WHERE idx_scan = 0
      AND indexrelname NOT LIKE '%_pkey'
      AND indexrelname NOT LIKE '%_unique'
      ORDER BY pg_relation_size(indexrelid) DESC
      LIMIT 10
    `);
    
    // Format the results
    const indexes = result.rows.map(index => ({
      schema: index.schemaname,
      table: index.table_name,
      name: index.index_name,
      scans: parseInt(index.index_scans),
      tuples_read: parseInt(index.tuples_read),
      tuples_fetched: parseInt(index.tuples_fetched),
      size: index.index_size
    }));
    
    // Determine status based on unused indexes
    let status = 'operational';
    const issues = [];
    
    if (unusedIndexes.rows.length > 0) {
      status = 'warning';
      
      // Calculate total wasted space
      const totalWastedBytes = unusedIndexes.rows.reduce(
        (sum, index) => sum + parseInt(index.index_size_bytes), 0
      );
      
      // Only report if wasted space is significant (> 10MB)
      if (totalWastedBytes > 10 * 1024 * 1024) {
        issues.push({
          type: 'unused_indexes',
          severity: 'low',
          description: `${unusedIndexes.rows.length} unused indexes found, wasting approximately ${Math.round(totalWastedBytes / (1024 * 1024))} MB of space`
        });
        
        // Add specific large unused indexes
        unusedIndexes.rows.forEach(index => {
          if (parseInt(index.index_size_bytes) > 5 * 1024 * 1024) { // > 5MB
            issues.push({
              type: 'large_unused_index',
              severity: 'medium',
              description: `Unused index ${index.index_name} on ${index.table_name} is using ${index.index_size} of space`
            });
          }
        });
      }
    }
    
    return {
      status,
      indexes,
      unused_indexes: unusedIndexes.rows,
      issues,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'warning',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get slow query statistics
 * @returns {Object} Slow query statistics
 */
async function getSlowQueryStatistics() {
  try {
    // Check if pg_stat_statements extension is available
    const extensionCheck = await query(`
      SELECT COUNT(*) as exists_count
      FROM pg_extension
      WHERE extname = 'pg_stat_statements'
    `);
    
    const hasStatStatements = parseInt(extensionCheck.rows[0].exists_count) > 0;
    
    if (!hasStatStatements) {
      return {
        status: 'warning',
        available: false,
        message: 'pg_stat_statements extension not installed',
        timestamp: new Date().toISOString()
      };
    }
    
    // Get slow query statistics
    const result = await query(`
      SELECT
        query,
        calls,
        total_time / 1000 as total_seconds,
        min_time / 1000 as min_seconds,
        max_time / 1000 as max_seconds,
        mean_time / 1000 as mean_seconds,
        rows
      FROM pg_stat_statements
      WHERE mean_time > 1000  -- queries taking more than 1 second on average
      ORDER BY mean_time DESC
      LIMIT 10
    `);
    
    // Format the results
    const slowQueries = result.rows.map(query => ({
      query: query.query,
      calls: parseInt(query.calls),
      total_seconds: parseFloat(query.total_seconds).toFixed(2),
      min_seconds: parseFloat(query.min_seconds).toFixed(2),
      max_seconds: parseFloat(query.max_seconds).toFixed(2),
      mean_seconds: parseFloat(query.mean_seconds).toFixed(2),
      rows: parseInt(query.rows)
    }));
    
    // Determine status based on slow queries
    let status = 'operational';
    const issues = [];
    
    if (slowQueries.length > 0) {
      // Check for very slow queries (> 5 seconds)
      const verySlowQueries = slowQueries.filter(q => parseFloat(q.mean_seconds) > 5);
      
      if (verySlowQueries.length > 0) {
        status = 'warning';
        issues.push({
          type: 'very_slow_queries',
          severity: 'high',
          description: `${verySlowQueries.length} queries with average execution time > 5 seconds detected`
        });
      } else if (slowQueries.length > 5) {
        status = 'warning';
        issues.push({
          type: 'multiple_slow_queries',
          severity: 'medium',
          description: `${slowQueries.length} queries with average execution time > 1 second detected`
        });
      }
    }
    
    return {
      status,
      available: true,
      slow_queries: slowQueries,
      issues,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'warning',
      available: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get active connections and what they're doing
 * @returns {Object} Active connections
 */
async function getActiveConnections() {
  try {
    const result = await query(`
      SELECT
        pid,
        usename as username,
        application_name,
        client_addr as client_address,
        state,
        query_start,
        EXTRACT(EPOCH FROM (now() - query_start)) as query_duration_seconds,
        query
      FROM pg_stat_activity
      WHERE state != 'idle'
      AND pid != pg_backend_pid()
      ORDER BY query_duration_seconds DESC
    `);
    
    // Format the results
    const connections = result.rows.map(conn => ({
      pid: conn.pid,
      username: conn.username,
      application: conn.application_name,
      client_address: conn.client_address,
      state: conn.state,
      query_start: conn.query_start,
      duration_seconds: parseFloat(conn.query_duration_seconds).toFixed(2),
      query: conn.query
    }));
    
    // Check for long-running queries
    const longRunningQueries = connections.filter(conn => parseFloat(conn.duration_seconds) > 60);
    
    // Determine status based on long-running queries
    let status = 'operational';
    const issues = [];
    
    if (longRunningQueries.length > 0) {
      status = 'warning';
      issues.push({
        type: 'long_running_queries',
        severity: 'medium',
        description: `${longRunningQueries.length} queries running for more than 60 seconds detected`
      });
      
      // Check for very long-running queries (> 5 minutes)
      const veryLongQueries = longRunningQueries.filter(conn => parseFloat(conn.duration_seconds) > 300);
      if (veryLongQueries.length > 0) {
        issues.push({
          type: 'very_long_queries',
          severity: 'high',
          description: `${veryLongQueries.length} queries running for more than 5 minutes detected`
        });
      }
    }
    
    return {
      status,
      connections,
      issues,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'warning',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Calculate overall database health status
 * @param {Object} metrics Database metrics
 * @returns {String} Overall status (operational, warning, critical)
 */
function calculateOverallStatus(metrics) {
  // Check for critical issues first
  if (metrics.connectionStatus.status === 'critical' || 
      metrics.poolMetrics.status === 'critical') {
    return 'critical';
  }
  
  // Check for warnings
  if (metrics.connectionStatus.status === 'warning' || 
      metrics.poolMetrics.status === 'warning' ||
      metrics.slowQueries.status === 'warning') {
    return 'warning';
  }
  
  return 'operational';
}

module.exports = {
  getDatabaseHealthMetrics,
  checkConnectionStatus,
  getPoolMetrics,
  getTableStatistics,
  getIndexUsageStatistics,
  getSlowQueryStatistics,
  getActiveConnections
};
