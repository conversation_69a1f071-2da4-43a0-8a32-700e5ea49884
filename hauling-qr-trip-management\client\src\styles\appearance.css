/* Appearance Settings CSS Variables */
:root {
  /* Default font settings */
  --font-header-family: 'Inter, system-ui, sans-serif';
  --font-header-size: 24px;
  --font-header-weight: 600;
  
  --font-content-family: 'Inter, system-ui, sans-serif';
  --font-content-size: 16px;
  --font-content-weight: 400;
  
  --font-footer-family: 'Inter, system-ui, sans-serif';
  --font-footer-size: 14px;
  --font-footer-weight: 400;
  
  /* Default logo settings */
  --logo-width: 40px;
  --logo-height: 40px;
}

/* Apply custom fonts to headers - more selective approach */
.font-custom-header,
h1.font-custom-header,
h2.font-custom-header,
h3.font-custom-header,
h4.font-custom-header,
h5.font-custom-header,
h6.font-custom-header {
  font-family: var(--font-header-family) !important;
  font-size: var(--font-header-size) !important;
  font-weight: var(--font-header-weight) !important;
}

/* Apply custom fonts to content - more selective approach */
.font-custom-content,
body.font-custom-content,
p.font-custom-content,
div.font-custom-content,
span.font-custom-content {
  font-family: var(--font-content-family) !important;
  font-size: var(--font-content-size) !important;
  font-weight: var(--font-content-weight) !important;
}

/* Apply custom fonts to footer elements - more selective approach */
.font-custom-footer,
footer.font-custom-footer,
footer .font-custom-footer {
  font-family: var(--font-footer-family) !important;
  font-size: var(--font-footer-size) !important;
  font-weight: var(--font-footer-weight) !important;
}

/* Logo styling */
.app-logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Responsive font scaling */
@media (max-width: 768px) {
  h1, h2, h3, h4, h5, h6,
  .header-font,
  .text-2xl,
  .text-xl,
  .text-lg {
    font-size: calc(var(--font-header-size) * 0.85) !important;
  }
  
  body,
  p,
  div,
  span,
  .content-font,
  .text-base,
  .text-sm {
    font-size: calc(var(--font-content-size) * 0.9) !important;
  }
}

/* Ensure proper font loading */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Override Tailwind's font utilities when custom fonts are applied */
.font-custom-header {
  font-family: var(--font-header-family) !important;
  font-size: var(--font-header-size) !important;
  font-weight: var(--font-header-weight) !important;
}

.font-custom-content {
  font-family: var(--font-content-family) !important;
  font-size: var(--font-content-size) !important;
  font-weight: var(--font-content-weight) !important;
}

.font-custom-footer {
  font-family: var(--font-footer-family) !important;
  font-size: var(--font-footer-size) !important;
  font-weight: var(--font-footer-weight) !important;
}