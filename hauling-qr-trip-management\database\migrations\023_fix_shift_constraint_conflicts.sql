-- Migration 023: Fix Shift Constraint Conflicts
-- Resolves exclusion constraint violations in update_all_shift_statuses function
-- Implements shift precedence logic: auto_created shifts take priority over scheduled shifts

-- Drop and recreate the update_all_shift_statuses function with conflict resolution
CREATE OR REPLACE FUNCTION update_all_shift_statuses(
    p_reference_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) RETURNS TABLE (
    updated_count INTEGER,
    activated_count INTEGER,
    scheduled_count INTEGER,
    completed_count INTEGER,
    total_count INTEGER
) AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_activated_count INTEGER := 0;
    v_scheduled_count INTEGER := 0;
    v_completed_count INTEGER := 0;
    v_total_count INTEGER := 0;
    v_shift RECORD;
    v_calculated_status TEXT;
    v_existing_active_shift_id INTEGER;
    v_existing_auto_created BOOLEAN;
    v_current_auto_created BOOLEAN;
BEGIN
    -- Count total non-cancelled shifts
    SELECT COUNT(*) INTO v_total_count FROM driver_shifts WHERE status != 'cancelled';
    
    -- Process all non-cancelled shifts with conflict resolution
    FOR v_shift IN 
        SELECT id, driver_id, status::TEXT, auto_created FROM driver_shifts 
        WHERE status != 'cancelled'
        ORDER BY auto_created DESC, created_at DESC  -- Prioritize auto_created shifts
    LOOP
        -- Calculate the correct status
        v_calculated_status := evaluate_shift_status(v_shift.id, p_reference_timestamp);
        
        -- Only update if status has changed and is not 'error'
        IF v_calculated_status != v_shift.status AND v_calculated_status != 'error' THEN
            
            -- Special handling for activations to prevent constraint violations
            IF v_calculated_status = 'active' THEN
                -- Check if there's already an active shift for this driver
                SELECT id, auto_created INTO v_existing_active_shift_id, v_existing_auto_created
                FROM driver_shifts 
                WHERE driver_id = v_shift.driver_id 
                  AND status = 'active' 
                  AND id != v_shift.id;
                
                -- If there's a conflict, apply precedence rules
                IF v_existing_active_shift_id IS NOT NULL THEN
                    v_current_auto_created := v_shift.auto_created;
                    
                    -- Auto-created shifts take precedence over scheduled shifts
                    IF v_current_auto_created AND NOT v_existing_auto_created THEN
                        -- Deactivate the existing scheduled shift
                        UPDATE driver_shifts 
                        SET status = 'scheduled', 
                            updated_at = p_reference_timestamp
                        WHERE id = v_existing_active_shift_id;
                        
                        -- Activate the current auto-created shift
                        UPDATE driver_shifts 
                        SET status = 'active', 
                            updated_at = p_reference_timestamp
                        WHERE id = v_shift.id;
                        
                        v_updated_count := v_updated_count + 2;
                        v_activated_count := v_activated_count + 1;
                        v_scheduled_count := v_scheduled_count + 1;
                        
                    ELSIF NOT v_current_auto_created AND v_existing_auto_created THEN
                        -- Keep the existing auto-created shift active, don't activate scheduled shift
                        -- No update needed, skip this shift
                        CONTINUE;
                        
                    ELSE
                        -- Both are same type (both auto_created or both scheduled)
                        -- Keep the newer one (higher ID), deactivate the older one
                        IF v_shift.id > v_existing_active_shift_id THEN
                            UPDATE driver_shifts 
                            SET status = 'scheduled', 
                                updated_at = p_reference_timestamp
                            WHERE id = v_existing_active_shift_id;
                            
                            UPDATE driver_shifts 
                            SET status = 'active', 
                                updated_at = p_reference_timestamp
                            WHERE id = v_shift.id;
                            
                            v_updated_count := v_updated_count + 2;
                            v_activated_count := v_activated_count + 1;
                            v_scheduled_count := v_scheduled_count + 1;
                        ELSE
                            -- Keep the existing newer shift, don't activate this older one
                            CONTINUE;
                        END IF;
                    END IF;
                ELSE
                    -- No conflict, safe to activate
                    UPDATE driver_shifts 
                    SET status = v_calculated_status::shift_status, 
                        updated_at = p_reference_timestamp
                    WHERE id = v_shift.id;
                    
                    v_updated_count := v_updated_count + 1;
                    v_activated_count := v_activated_count + 1;
                END IF;
            ELSE
                -- Non-activation updates (scheduled, completed) - safe to update
                UPDATE driver_shifts 
                SET status = v_calculated_status::shift_status, 
                    updated_at = p_reference_timestamp
                WHERE id = v_shift.id;
                
                v_updated_count := v_updated_count + 1;
                
                -- Count by status type
                CASE v_calculated_status
                    WHEN 'scheduled' THEN v_scheduled_count := v_scheduled_count + 1;
                    WHEN 'completed' THEN v_completed_count := v_completed_count + 1;
                END CASE;
            END IF;
        END IF;
    END LOOP;
    
    -- Return the statistics
    updated_count := v_updated_count;
    activated_count := v_activated_count;
    scheduled_count := v_scheduled_count;
    completed_count := v_completed_count;
    total_count := v_total_count;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION update_all_shift_statuses(p_reference_timestamp timestamp with time zone) 
IS 'Updates shift statuses with conflict resolution - auto_created shifts take precedence over scheduled shifts';
