# Design Document

## Overview

The deployment script update will modernize the Ubuntu deployment automation to match the current Hauling QR Trip Management System codebase. The design focuses on updating package dependencies, startup commands, database initialization, environment configuration, and monitoring setup to align with the actual project structure.

## Architecture

### Current Deployment Script Structure
```
deploy-hauling-qr-ubuntu/
├── deploy-hauling-qr-ubuntu-fixed.sh    # Main deployment script
├── deployment-config.conf               # Deployment configuration
├── DEPLOYMENT_STEPS.md                  # Documentation
└── Various documentation files
```

### Updated Deployment Flow
```
1. Environment Detection & Prerequisites
2. Repository Cloning (with current structure)
3. Updated Package Installation
4. Current Database Schema Setup
5. Modern Environment Configuration
6. Updated Application Build & Deployment
7. Current Monitoring & Logging Setup
8. Service Configuration with PM2
```

## Components and Interfaces

### 1. Package Installation Updates

**Current Issues:**
- Deployment script contains hardcoded fallback package.json files that don't match current dependencies
- Missing critical packages like wins<PERSON>, joi, m<PERSON>, sharp, @yudiel/react-qr-scanner

**Design Solution:**
- Update `create_fallback_structure()` function to use current package.json contents
- Modify npm installation sections to handle current dependency tree
- Add validation to ensure all critical packages are installed

**Key Changes:**
```bash
# Server dependencies (from current server/package.json)
"dependencies": {
  "@supabase/mcp-server-postgrest": "^0.1.0",
  "axios": "^1.10.0",
  "bcryptjs": "^2.4.3",
  "cors": "^2.8.5",
  "dotenv": "^16.3.1",
  "express": "^4.18.2",
  "express-rate-limit": "^7.1.5",
  "helmet": "^7.1.0",
  "joi": "^17.11.0",
  "jsonwebtoken": "^9.0.2",
  "jsqr": "^1.4.0",
  "multer": "^1.4.5-lts.1",
  "node-fetch": "^3.3.2",
  "pg": "^8.11.3",
  "qrcode": "^1.5.3",
  "sharp": "^0.32.6",
  "uuid": "^9.0.1",
  "winston": "^3.17.0"
}

# Client dependencies (from current client/package.json)
"dependencies": {
  "@yudiel/react-qr-scanner": "^2.3.1",
  "chart.js": "^4.4.0",
  "react-chartjs-2": "^5.2.0",
  "react-hook-form": "^7.48.2",
  "react-hot-toast": "^2.4.1",
  // ... other current dependencies
}
```

### 2. Database Schema Updates

**Current Issues:**
- Deployment script uses basic database schema that doesn't match current init.sql
- Missing complex tables, functions, and triggers from current system

**Design Solution:**
- Update database initialization to use current database/init.sql
- Ensure all required PostgreSQL extensions are installed
- Add validation for complex database objects (materialized views, functions)

**Key Changes:**
```sql
-- Current schema includes:
- Complex trip_logs table with enhanced fields
- driver_shifts table with recurrence patterns
- Materialized views for analytics
- Advanced PostgreSQL functions
- Trigger-based automation
```

### 3. Environment Configuration Updates

**Current Issues:**
- Deployment script creates basic .env that doesn't match current unified configuration
- Missing critical environment variables for monitoring, logging, WebSocket

**Design Solution:**
- Update environment file generation to match current .env structure
- Implement unified configuration system integration
- Add production-specific overrides

**Key Environment Variables to Add:**
```bash
# Monitoring and Logging
LOG_LEVEL=info
MONITORING_LOGS_ENABLED=true
WINSTON_DAILY_ROTATE=true

# WebSocket Configuration
WS_PORT=5000

# Performance Settings
DB_POOL_MAX=25
DB_POOL_MIN=5

# Security Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Production Overrides
JWT_SECRET_PROD=<generated_secure_key>
```

### 4. Startup Command Updates

**Current Issues:**
- Deployment script uses outdated startup commands
- PM2 configuration doesn't match current server structure

**Design Solution:**
- Update PM2 ecosystem file to use correct paths
- Integrate with unified configuration system
- Add proper process monitoring

**Updated PM2 Configuration:**
```javascript
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: '/var/www/hauling-qr-system',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    error_file: './server/logs/pm2-error.log',
    out_file: './server/logs/pm2-out.log',
    log_file: './server/logs/pm2-combined.log'
  }]
};
```

### 5. Build Process Updates

**Current Issues:**
- Client build process doesn't account for current Tailwind CSS setup
- Missing PostCSS configuration handling

**Design Solution:**
- Update client build to handle current build pipeline
- Ensure Tailwind CSS and PostCSS are properly configured
- Add build validation steps

## Data Models

### Deployment Configuration Schema
```bash
# Updated deployment-config.conf structure
DOMAIN_NAME="example.com"
ENV_MODE="production"
SSL_MODE="cloudflare"

# Database Configuration
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="hauling_qr_system"
DB_USER="hauling_app"
DB_PASSWORD="<secure_password>"

# Application Configuration
ENABLE_MONITORING=true
ENABLE_WEBSOCKET=true
LOG_LEVEL="info"

# Security Configuration
JWT_SECRET_PROD="<generated_secure_key>"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="<secure_password>"
ADMIN_EMAIL="<EMAIL>"
```

### Environment File Template
```bash
# Production Environment Template
NODE_ENV=production
ENABLE_HTTPS=true
AUTO_DETECT_IP=false

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_app
DB_PASSWORD=${DB_PASSWORD}

# Application
BACKEND_PORT=5000
FRONTEND_PORT=3000

# Security
JWT_SECRET=${JWT_SECRET_PROD}
RATE_LIMIT_MAX_REQUESTS=5000

# Monitoring
LOG_LEVEL=info
MONITORING_LOGS_ENABLED=true
WINSTON_DAILY_ROTATE=true

# WebSocket
WS_PORT=5000

# Production URLs
REACT_APP_API_URL=https://${DOMAIN_NAME}/api
REACT_APP_WS_URL=wss://${DOMAIN_NAME}
```

## Error Handling

### Package Installation Failures
- Implement retry mechanisms for npm installations
- Add fallback package sources
- Validate critical packages are installed before proceeding

### Database Setup Failures
- Add comprehensive database connectivity testing
- Implement rollback mechanisms for failed schema installations
- Provide detailed error messages for common database issues

### Build Process Failures
- Add validation for Node.js and npm versions
- Implement cleanup for failed builds
- Provide specific error messages for build failures

## Testing Strategy

### Pre-Deployment Validation
- Verify all required files exist in repository
- Validate package.json files are readable
- Test database connectivity before schema installation

### Post-Deployment Validation
- Health check endpoints (/api/health, /api/health/db)
- Verify all services are running (nginx, postgresql, PM2)
- Test WebSocket connectivity
- Validate admin user creation

### Rollback Testing
- Test ability to restore from backup
- Verify service stop/start procedures
- Test configuration rollback mechanisms

## Performance Considerations

### Installation Optimization
- Use npm ci for faster, reliable installs
- Implement parallel installation where possible
- Cache downloaded packages when feasible

### Database Performance
- Ensure proper indexing during schema creation
- Configure PostgreSQL for production workloads
- Set appropriate connection pool sizes

### Monitoring Setup
- Configure log rotation to prevent disk space issues
- Set appropriate log levels for production
- Implement health check endpoints for monitoring