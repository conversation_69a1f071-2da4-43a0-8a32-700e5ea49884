import React, { useState } from 'react';
import { getApiBaseUrl } from '../../../utils/network-utils';

const DatabaseCleanupPanel = () => {
  const [loading, setLoading] = useState({});
  const [results, setResults] = useState({});
  const [confirmTable, setConfirmTable] = useState(null);

  const tables = [
    { name: 'trip_logs', label: 'Trip Logs', description: 'All trip records and tracking data' },
    { name: 'scan_logs', label: 'Scan Logs', description: 'QR code scan history and logs' },
    { name: 'assignments', label: 'Assignments', description: 'Driver-truck assignments' },
    { name: 'migration_log', label: 'Migration Log', description: 'Database migration history' }
  ];

  const handleCleanup = async (tableName) => {
    try {
      setLoading(prev => ({ ...prev, [tableName]: true }));
      setResults(prev => ({ ...prev, [tableName]: null }));

      const apiUrl = getApiBaseUrl();
      console.log(`Cleaning ${tableName}...`);
      
      const response = await fetch(`${apiUrl}/admin/cleanup/${tableName}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setResults(prev => ({ ...prev, [tableName]: result }));
        alert(`✅ ${tableName} cleaned successfully!\n\nDeleted: ${result.data?.deleted_count || 0} records`);
      } else {
        throw new Error(result.message || 'Unknown error');
      }

    } catch (error) {
      console.error(`Error cleaning ${tableName}:`, error);
      alert(`❌ Error cleaning ${tableName}: ${error.message || 'Network error'}`);
    } finally {
      setLoading(prev => ({ ...prev, [tableName]: false }));
      setConfirmTable(null);
    }
  };

  const handleConfirm = (tableName) => {
    setConfirmTable(tableName);
  };

  const handleCancel = () => {
    setConfirmTable(null);
  };

  const getTableColor = (tableName) => {
    switch (tableName) {
      case 'trip_logs': return 'border-red-200 bg-red-50';
      case 'scan_logs': return 'border-blue-200 bg-blue-50';
      case 'assignments': return 'border-yellow-200 bg-yellow-50';
      case 'migration_log': return 'border-green-200 bg-green-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getButtonColor = (tableName) => {
    switch (tableName) {
      case 'trip_logs': return 'bg-red-600 hover:bg-red-700';
      case 'scan_logs': return 'bg-blue-600 hover:bg-blue-700';
      case 'assignments': return 'bg-yellow-600 hover:bg-yellow-700';
      case 'migration_log': return 'bg-green-600 hover:bg-green-700';
      default: return 'bg-gray-600 hover:bg-gray-700';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow border border-secondary-200">
      <div className="px-6 py-4 border-b border-secondary-200">
        <div>
          <h3 className="text-lg font-medium text-secondary-900">
            🗑️ Database Cleanup Tools
          </h3>
          <p className="text-sm text-secondary-500 mt-1">
            Clean specific database tables to reset data or fix display issues
          </p>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tables.map((table) => (
            <div key={table.name} className={`border rounded-lg p-4 ${getTableColor(table.name)}`}>
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium text-secondary-900">{table.label}</h4>
                  <p className="text-sm text-secondary-600 mt-1">{table.description}</p>
                </div>
                <div className="text-2xl">
                  {table.name === 'trip_logs' && '🚛'}
                  {table.name === 'scan_logs' && '📱'}
                  {table.name === 'assignments' && '📝'}
                  {table.name === 'migration_log' && '🔄'}
                </div>
              </div>

              {confirmTable === table.name ? (
                <div className="mt-4 p-3 bg-white rounded-md border border-red-200">
                  <p className="text-sm text-red-700 mb-3">
                    ⚠️ Are you sure you want to delete ALL records from {table.label}?
                  </p>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleCleanup(table.name)}
                      disabled={loading[table.name]}
                      className={`flex-1 px-3 py-1.5 text-sm font-medium text-white rounded ${getButtonColor(table.name)} disabled:opacity-50`}
                    >
                      {loading[table.name] ? '⏳' : '✅ Confirm Delete'}
                    </button>
                    <button
                      onClick={handleCancel}
                      disabled={loading[table.name]}
                      className="flex-1 px-3 py-1.5 text-sm font-medium text-secondary-700 bg-white border border-secondary-300 rounded hover:bg-secondary-50 disabled:opacity-50"
                    >
                      ❌ Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <button
                  onClick={() => handleConfirm(table.name)}
                  disabled={loading[table.name]}
                  className={`mt-4 w-full px-3 py-1.5 text-sm font-medium text-white rounded ${getButtonColor(table.name)} disabled:opacity-50`}
                >
                  {loading[table.name] ? '⏳ Cleaning...' : `🗑️ Clean ${table.label}`}
                </button>
              )}

              {results[table.name] && (
                <div className="mt-3 p-2 bg-white rounded text-xs">
                  <span className="text-green-600">✅ Cleaned: {results[table.name].data?.deleted_count || 0} records</span>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <h5 className="text-sm font-medium text-red-900 mb-2">⚠️ Warning</h5>
          <ul className="text-sm text-red-700 space-y-1">
            <li>• This will permanently delete ALL data from the selected table</li>
            <li>• Cannot be undone - make sure you have backups if needed</li>
            <li>• Use only for testing or when you want to reset specific data</li>
            <li>• Consider exporting data first if you need to preserve it</li>
          </ul>
        </div>

        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h5 className="text-sm font-medium text-blue-900 mb-2">💡 Quick Actions</h5>
          <div className="text-sm text-blue-700 space-y-1">
            <p>• <strong>Fix Assignment Display Issues:</strong> Use the purple button in Shift Synchronization Monitor</p>
            <p>• <strong>Reset Test Data:</strong> Clean specific tables to start fresh</p>
            <p>• <strong>Fix Cache Issues:</strong> Use the "Apply Overnight Fix" button</p>
          </div>
        </div>

        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h5 className="text-sm font-medium text-gray-900 mb-2">🔧 API Status</h5>
          <p className="text-sm text-gray-700">
            API endpoints: <code>/api/admin/cleanup/:table</code> (DELETE method)
          </p>
          <p className="text-sm text-gray-700 mt-1">
            Available tables: trip_logs, scan_logs, assignments, migration_log
          </p>
        </div>
      </div>
    </div>
  );
};

export default DatabaseCleanupPanel;