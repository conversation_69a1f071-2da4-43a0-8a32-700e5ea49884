-- Migration 028: Driver Status Audit System
-- Creates audit table for tracking blocked driver operation attempts for security monitoring
-- DEFENSIVE MIGRATION: Uses conditional logic to skip existing constraints and objects

-- Create driver_status_audit table for security monitoring
CREATE TABLE IF NOT EXISTS driver_status_audit (
    id SERIAL PRIMARY KEY,
    driver_id INTEGER NOT NULL,
    employee_id VARCHAR(20) NOT NULL,
    driver_status VARCHAR(20) NOT NULL,
    operation_attempted VARCHAR(50) NOT NULL,
    blocked_at TIMESTAMP NOT NULL DEFAULT NOW(),
    ip_address VARCHAR(45), -- Supports both IPv4 and IPv6
    user_agent TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Add foreign key constraint to drivers table (skip if exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_driver_status_audit_driver'
        AND table_name = 'driver_status_audit'
    ) THEN
        ALTER TABLE driver_status_audit
        ADD CONSTRAINT fk_driver_status_audit_driver
        FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE CASCADE;

        RAISE NOTICE 'Added foreign key constraint fk_driver_status_audit_driver';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_driver_status_audit_driver already exists, skipping';
    END IF;
END $$;

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_driver_status_audit_driver_id 
ON driver_status_audit (driver_id);

CREATE INDEX IF NOT EXISTS idx_driver_status_audit_employee_id 
ON driver_status_audit (employee_id);

CREATE INDEX IF NOT EXISTS idx_driver_status_audit_blocked_at 
ON driver_status_audit (blocked_at DESC);

CREATE INDEX IF NOT EXISTS idx_driver_status_audit_status_operation 
ON driver_status_audit (driver_status, operation_attempted);

-- Create composite index for security monitoring queries
CREATE INDEX IF NOT EXISTS idx_driver_status_audit_monitoring 
ON driver_status_audit (blocked_at DESC, driver_status, operation_attempted);

-- Add comment to table for documentation
COMMENT ON TABLE driver_status_audit IS 'Audit trail for blocked driver operations due to inactive, suspended, on_leave, or terminated status';
COMMENT ON COLUMN driver_status_audit.driver_id IS 'Foreign key reference to drivers table';
COMMENT ON COLUMN driver_status_audit.employee_id IS 'Driver employee ID for quick reference';
COMMENT ON COLUMN driver_status_audit.driver_status IS 'Driver status at time of blocked operation (inactive, suspended, on_leave, terminated)';
COMMENT ON COLUMN driver_status_audit.operation_attempted IS 'Type of operation that was blocked (check_in, check_out, qr_scan, etc.)';
COMMENT ON COLUMN driver_status_audit.blocked_at IS 'Timestamp when the operation was blocked';
COMMENT ON COLUMN driver_status_audit.ip_address IS 'IP address of the request (for security monitoring)';
COMMENT ON COLUMN driver_status_audit.user_agent IS 'User agent string from the request';

-- Create a view for easy security monitoring (CREATE OR REPLACE handles existing views)
CREATE OR REPLACE VIEW driver_status_audit_summary AS
SELECT 
    dsa.id,
    dsa.employee_id,
    d.full_name,
    dsa.driver_status,
    dsa.operation_attempted,
    dsa.blocked_at,
    dsa.ip_address,
    COUNT(*) OVER (PARTITION BY dsa.driver_id, DATE(dsa.blocked_at)) as daily_attempts,
    COUNT(*) OVER (PARTITION BY dsa.driver_id) as total_attempts
FROM driver_status_audit dsa
JOIN drivers d ON dsa.driver_id = d.id
ORDER BY dsa.blocked_at DESC;

COMMENT ON VIEW driver_status_audit_summary IS 'Summary view of blocked driver operations with attempt counts for security monitoring';

-- Create function to clean up old audit records (optional, for maintenance)
CREATE OR REPLACE FUNCTION cleanup_driver_status_audit(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM driver_status_audit 
    WHERE blocked_at < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_driver_status_audit IS 'Function to clean up old audit records. Default retention is 90 days.';

-- Grant appropriate permissions (adjust based on your user setup)
-- GRANT SELECT, INSERT ON driver_status_audit TO hauling_app_user;
-- GRANT SELECT ON driver_status_audit_summary TO hauling_app_user;
-- GRANT EXECUTE ON FUNCTION cleanup_driver_status_audit TO hauling_admin_user;
