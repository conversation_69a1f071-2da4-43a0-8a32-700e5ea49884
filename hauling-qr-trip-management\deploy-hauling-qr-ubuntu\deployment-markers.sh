#!/bin/bash

# =============================================================================
# DEPLOYMENT MARKERS SYSTEM - IDEMPOTENT DEPLOYMENT UTILITIES
# =============================================================================
# Version: 1.0.0
# Description: Comprehensive deployment marker system for idempotent deployments
# Purpose: Track deployment state, prevent duplicate operations, enable rollback
# =============================================================================

set -euo pipefail

# =============================================================================
# DEPLOYMENT MARKER CONFIGURATION
# =============================================================================
readonly DEPLOYMENT_MARKERS_DIR="/var/log/hauling-qr-deployment/markers"
readonly DEPLOYMENT_STATE_DIR="/var/log/hauling-qr-deployment/state"
readonly DEPLOYMENT_CHECKSUMS_DIR="/var/log/hauling-qr-deployment/checksums"

# Marker format: HAULING-QR-DEPLOYMENT-MANAGED-[PHASE]-[COMPONENT]-[TIMESTAMP]
readonly MARKER_PREFIX="HAULING-QR-DEPLOYMENT-MANAGED"

# =============================================================================
# INITIALIZATION
# =============================================================================
init_deployment_markers() {
  # Create marker directories
  mkdir -p "$DEPLOYMENT_MARKERS_DIR" "$DEPLOYMENT_STATE_DIR" "$DEPLOYMENT_CHECKSUMS_DIR"
  chmod 755 "$DEPLOYMENT_MARKERS_DIR" "$DEPLOYMENT_STATE_DIR" "$DEPLOYMENT_CHECKSUMS_DIR"
}

# =============================================================================
# MARKER MANAGEMENT FUNCTIONS
# =============================================================================
create_deployment_marker() {
  local phase="$1"
  local component="$2"
  local status="${3:-started}"
  local timestamp=$(date +%Y%m%d_%H%M%S)
  
  init_deployment_markers
  
  local marker_name="${MARKER_PREFIX}-${phase}-${component}-${timestamp}"
  local marker_file="${DEPLOYMENT_MARKERS_DIR}/${marker_name}"
  
  cat > "$marker_file" << EOF
# Hauling QR Trip System Deployment Marker
PHASE=$phase
COMPONENT=$component
STATUS=$status
TIMESTAMP=$timestamp
CREATED=$(date -Iseconds)
HOSTNAME=$(hostname)
USER=$(whoami)
DEPLOYMENT_ID=${DEPLOYMENT_ID:-$(date +%Y%m%d_%H%M%S)}
EOF
  
  echo "$marker_file"
}

update_deployment_marker() {
  local phase="$1"
  local component="$2"
  local status="$3"
  
  local marker_pattern="${MARKER_PREFIX}-${phase}-${component}-*"
  local marker_file
  
  # Find the most recent marker for this phase/component
  marker_file=$(find "$DEPLOYMENT_MARKERS_DIR" -name "$marker_pattern" -type f 2>/dev/null | sort | tail -n1)
  
  if [[ -n "$marker_file" && -f "$marker_file" ]]; then
    # Update existing marker
    sed -i "s/^STATUS=.*/STATUS=$status/" "$marker_file"
    sed -i "s/^UPDATED=.*/UPDATED=$(date -Iseconds)/" "$marker_file" 2>/dev/null || \
      echo "UPDATED=$(date -Iseconds)" >> "$marker_file"
  else
    # Create new marker if none exists
    create_deployment_marker "$phase" "$component" "$status" >/dev/null
  fi
}

check_deployment_marker() {
  local phase="$1"
  local component="$2"
  local expected_status="${3:-completed}"
  
  local marker_pattern="${MARKER_PREFIX}-${phase}-${component}-*"
  local marker_file
  
  # Find the most recent marker for this phase/component
  marker_file=$(find "$DEPLOYMENT_MARKERS_DIR" -name "$marker_pattern" -type f 2>/dev/null | sort | tail -n1)
  
  if [[ -n "$marker_file" && -f "$marker_file" ]]; then
    local status
    status=$(grep "^STATUS=" "$marker_file" | cut -d'=' -f2)
    
    if [[ "$status" == "$expected_status" ]]; then
      return 0  # Marker exists with expected status
    else
      return 1  # Marker exists but wrong status
    fi
  else
    return 2  # No marker exists
  fi
}

# =============================================================================
# CONFIGURATION CHECKSUM FUNCTIONS
# =============================================================================
calculate_config_checksum() {
  local config_file="$1"
  local checksum_name="$2"
  
  if [[ -f "$config_file" ]]; then
    local checksum
    checksum=$(sha256sum "$config_file" | cut -d' ' -f1)
    echo "$checksum" > "${DEPLOYMENT_CHECKSUMS_DIR}/${checksum_name}.sha256"
    echo "$checksum"
  else
    echo ""
  fi
}

verify_config_checksum() {
  local config_file="$1"
  local checksum_name="$2"
  local stored_checksum_file="${DEPLOYMENT_CHECKSUMS_DIR}/${checksum_name}.sha256"
  
  if [[ ! -f "$stored_checksum_file" ]]; then
    return 1  # No stored checksum
  fi
  
  if [[ ! -f "$config_file" ]]; then
    return 2  # Config file doesn't exist
  fi
  
  local current_checksum
  local stored_checksum
  
  current_checksum=$(sha256sum "$config_file" | cut -d' ' -f1)
  stored_checksum=$(cat "$stored_checksum_file")
  
  if [[ "$current_checksum" == "$stored_checksum" ]]; then
    return 0  # Checksums match
  else
    return 3  # Checksums don't match (configuration drift)
  fi
}

# =============================================================================
# IDEMPOTENT OPERATION FUNCTIONS
# =============================================================================
is_operation_completed() {
  local operation_name="$1"
  local phase="${2:-general}"
  
  check_deployment_marker "$phase" "$operation_name" "completed"
  return $?
}

mark_operation_started() {
  local operation_name="$1"
  local phase="${2:-general}"
  
  create_deployment_marker "$phase" "$operation_name" "started" >/dev/null
}

mark_operation_completed() {
  local operation_name="$1"
  local phase="${2:-general}"
  
  update_deployment_marker "$phase" "$operation_name" "completed"
}

mark_operation_failed() {
  local operation_name="$1"
  local phase="${2:-general}"
  local error_message="${3:-Unknown error}"
  
  update_deployment_marker "$phase" "$operation_name" "failed"
  
  # Store error details
  local error_file="${DEPLOYMENT_STATE_DIR}/${operation_name}_error.log"
  cat > "$error_file" << EOF
OPERATION=$operation_name
PHASE=$phase
STATUS=failed
ERROR_MESSAGE=$error_message
TIMESTAMP=$(date -Iseconds)
HOSTNAME=$(hostname)
USER=$(whoami)
EOF
}

# =============================================================================
# DEPLOYMENT STATE FUNCTIONS
# =============================================================================
get_deployment_status() {
  local phase="$1"
  local component="$2"
  
  local marker_pattern="${MARKER_PREFIX}-${phase}-${component}-*"
  local marker_file
  
  marker_file=$(find "$DEPLOYMENT_MARKERS_DIR" -name "$marker_pattern" -type f 2>/dev/null | sort | tail -n1)
  
  if [[ -n "$marker_file" && -f "$marker_file" ]]; then
    grep "^STATUS=" "$marker_file" | cut -d'=' -f2
  else
    echo "not_started"
  fi
}

list_deployment_markers() {
  local phase_filter="${1:-}"
  
  init_deployment_markers
  
  echo "=== DEPLOYMENT MARKERS ==="
  echo "Format: PHASE-COMPONENT-TIMESTAMP STATUS"
  echo ""
  
  local marker_files
  if [[ -n "$phase_filter" ]]; then
    marker_files=$(find "$DEPLOYMENT_MARKERS_DIR" -name "${MARKER_PREFIX}-${phase_filter}-*" -type f 2>/dev/null | sort)
  else
    marker_files=$(find "$DEPLOYMENT_MARKERS_DIR" -name "${MARKER_PREFIX}-*" -type f 2>/dev/null | sort)
  fi
  
  if [[ -n "$marker_files" ]]; then
    while IFS= read -r marker_file; do
      local basename_marker
      basename_marker=$(basename "$marker_file")
      local status
      status=$(grep "^STATUS=" "$marker_file" | cut -d'=' -f2)
      local created
      created=$(grep "^CREATED=" "$marker_file" | cut -d'=' -f2)
      
      echo "  ${basename_marker#$MARKER_PREFIX-} -> $status ($created)"
    done <<< "$marker_files"
  else
    echo "  No deployment markers found"
  fi
  
  echo ""
}

# =============================================================================
# ROLLBACK FUNCTIONS
# =============================================================================
create_rollback_point() {
  local rollback_name="$1"
  local description="${2:-Rollback point}"
  
  local rollback_dir="${DEPLOYMENT_STATE_DIR}/rollback_${rollback_name}_$(date +%Y%m%d_%H%M%S)"
  mkdir -p "$rollback_dir"
  
  # Store current deployment state
  cp -r "$DEPLOYMENT_MARKERS_DIR" "$rollback_dir/markers" 2>/dev/null || true
  cp -r "$DEPLOYMENT_CHECKSUMS_DIR" "$rollback_dir/checksums" 2>/dev/null || true
  
  # Store rollback metadata
  cat > "$rollback_dir/rollback_info.txt" << EOF
ROLLBACK_NAME=$rollback_name
DESCRIPTION=$description
CREATED=$(date -Iseconds)
HOSTNAME=$(hostname)
USER=$(whoami)
EOF
  
  echo "$rollback_dir"
}

# =============================================================================
# CLEANUP FUNCTIONS
# =============================================================================
cleanup_old_markers() {
  local days_to_keep="${1:-7}"
  
  init_deployment_markers
  
  # Clean up markers older than specified days
  find "$DEPLOYMENT_MARKERS_DIR" -name "${MARKER_PREFIX}-*" -type f -mtime +$days_to_keep -delete 2>/dev/null || true
  find "$DEPLOYMENT_STATE_DIR" -name "*.log" -type f -mtime +$days_to_keep -delete 2>/dev/null || true
  find "$DEPLOYMENT_CHECKSUMS_DIR" -name "*.sha256" -type f -mtime +$days_to_keep -delete 2>/dev/null || true
}

# =============================================================================
# INITIALIZATION ON SOURCE
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  # Script is being run directly, not sourced
  case "${1:-}" in
    "init")
      init_deployment_markers
      echo "Deployment markers system initialized"
      ;;
    "list")
      list_deployment_markers "${2:-}"
      ;;
    "cleanup")
      cleanup_old_markers "${2:-7}"
      echo "Cleaned up markers older than ${2:-7} days"
      ;;
    *)
      echo "Usage: $0 {init|list [phase]|cleanup [days]}"
      exit 1
      ;;
  esac
else
  # Script is being sourced, initialize automatically
  init_deployment_markers
fi
