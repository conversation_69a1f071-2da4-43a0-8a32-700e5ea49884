import React, { useState, useEffect, useCallback } from 'react';
import { locationsAPI } from '../../services/api';
import LocationsTable from './components/LocationsTable';
import LocationFormModal from './components/LocationFormModal';
import QRCodeModal from '../../components/common/QRCodeModal';
import DeleteConfirmModal from '../../components/common/DeleteConfirmModal';
import toast from 'react-hot-toast';

const LocationsManagement = () => {
  const [locations, setLocations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Modal states
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [qrCodeData, setQRCodeData] = useState(null);

  // Filter and search states
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    is_active: '',
    sortBy: 'name',
    sortOrder: 'asc'
  });

  // Load locations with error handling
  const loadLocations = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pagination.itemsPerPage,
        ...filters
      };

      const response = await locationsAPI.getAll({ params });
      setLocations(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Error loading locations:', error);
      
      // Better error handling
      if (error.code === 'ERR_NETWORK') {
        toast.error('Network error. Please check your connection.');
      } else if (error.response?.status === 429) {
        toast.error('Too many requests. Please wait a moment.');
      } else {
        toast.error(error.response?.data?.message || 'Failed to load locations');
      }
    } finally {
      setLoading(false);
    }
  }, [pagination.itemsPerPage, filters]);

  // Remove debounced function - using proven pattern

  // Initial load
  useEffect(() => {
    loadLocations();
  }, [loadLocations]);

  // Handle page change
  const handlePageChange = (page) => {
    loadLocations(page);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle sorting
  const handleSort = (column) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    handleFilterChange({
      sortBy: column,
      sortOrder: newSortOrder
    });
  };

  // Handle search - use proven pattern
  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      search: '',
      type: '',
      is_active: '',
      sortBy: 'name',
      sortOrder: 'asc'
    });
  };

  // Handle create location
  const handleCreateLocation = () => {
    setSelectedLocation(null);
    setShowLocationModal(true);
  };

  // Handle edit location
  const handleEditLocation = (location) => {
    setSelectedLocation(location);
    setShowLocationModal(true);
  };

  // Handle delete location
  const handleDeleteLocation = (location) => {
    setSelectedLocation(location);
    setShowDeleteModal(true);
  };

  // Handle QR code generation
  const handleShowQR = async (location) => {
    console.log('Show QR clicked for location:', location);
    try {
      const response = await locationsAPI.getQRCode(location.id);
      console.log('QR API response:', response);
      setQRCodeData(response.data.data);
      setShowQRModal(true);
      console.log('QR modal should now be open with data:', response.data.data);
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error('Failed to generate QR code');
    }
  };

  // Handle form submission
  const handleLocationSubmit = async (formData) => {
    try {
      if (selectedLocation) {
        // Update existing location
        await locationsAPI.update(selectedLocation.id, formData);
        toast.success('Location updated successfully');
      } else {
        // Create new location
        await locationsAPI.create(formData);
        toast.success('Location created successfully');
      }
      
      setShowLocationModal(false);
      loadLocations(pagination.currentPage);
    } catch (error) {
      console.error('Error saving location:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save location';
      toast.error(errorMessage);
      throw error; // Re-throw to prevent modal from closing
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      await locationsAPI.delete(selectedLocation.id);
      toast.success('Location deleted successfully');
      setShowDeleteModal(false);
      
      // Reload current page or go to previous page if current page becomes empty
      const newTotalItems = pagination.totalItems - 1;
      const newTotalPages = Math.ceil(newTotalItems / pagination.itemsPerPage);
      const targetPage = pagination.currentPage > newTotalPages ? newTotalPages : pagination.currentPage;
      
      loadLocations(Math.max(1, targetPage));
    } catch (error) {
      console.error('Error deleting location:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete location';
      toast.error(errorMessage);
    }
  };

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== ''
  ).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Locations Management</h1>
          <p className="text-secondary-600 mt-1">Manage loading and unloading points with QR codes</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={handleCreateLocation}
            className="btn btn-primary"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Location
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="lg:col-span-2">
            <label htmlFor="search" className="block text-sm font-medium text-secondary-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search locations..."
              className="input"
            />
          </div>

          {/* Type Filter */}
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-secondary-700 mb-1">
              Type
            </label>
            <select
              id="type"
              value={filters.type}
              onChange={(e) => handleFilterChange({ type: e.target.value })}
              className="input"
            >
              <option value="">All Types</option>
              <option value="loading">Loading</option>
              <option value="unloading">Unloading</option>
              <option value="checkpoint">Checkpoint</option>
            </select>
          </div>

          {/* Status Filter */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-secondary-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={filters.is_active}
              onChange={(e) => handleFilterChange({ is_active: e.target.value })}
              className="input"
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>

          {/* Clear Filters */}
          <div className="flex items-end">
            <button
              onClick={clearFilters}
              disabled={activeFiltersCount === 0}
              className="btn btn-secondary w-full disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Filters
              {activeFiltersCount > 0 && (
                <span className="ml-1 bg-primary-100 text-primary-800 text-xs rounded-full px-2 py-0.5">
                  {activeFiltersCount}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Locations Table */}
      <LocationsTable
        locations={locations}
        loading={loading}
        pagination={pagination}
        filters={filters}
        onPageChange={handlePageChange}
        onSort={handleSort}
        onEdit={handleEditLocation}
        onDelete={handleDeleteLocation}
        onShowQR={handleShowQR}
      />

      {/* Location Form Modal */}
      {showLocationModal && (
        <LocationFormModal
          location={selectedLocation}
          onClose={() => setShowLocationModal(false)}
          onSubmit={handleLocationSubmit}
        />
      )}

      {/* QR Code Modal */}
      {showQRModal && qrCodeData && (
        <QRCodeModal
          qrData={qrCodeData}
          onClose={() => {
            setShowQRModal(false);
            setQRCodeData(null);
          }}
        />
      )}      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedLocation && (
        <DeleteConfirmModal
          title="Delete Location"
          message={`Are you sure you want to delete "${selectedLocation.name}"? This action cannot be undone.`}
          onConfirm={handleDeleteConfirm}
          onCancel={() => setShowDeleteModal(false)}
        />
      )}
    </div>
  );
};

export default LocationsManagement;