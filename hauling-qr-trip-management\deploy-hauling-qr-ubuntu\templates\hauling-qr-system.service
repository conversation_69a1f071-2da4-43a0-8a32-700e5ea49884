[Unit]
Description=Hauling QR Trip Management System
Documentation=https://github.com/mightybadz18/hauling-qr-trip-management
After=network.target postgresql.service nginx.service
Wants=postgresql.service nginx.service
Requires=network.target

[Service]
Type=forking
User=ubuntu
Group=ubuntu
WorkingDirectory=/var/www/hauling-qr-system
Environment=NODE_ENV=production
Environment=PATH=/usr/bin:/usr/local/bin:/home/<USER>/.nvm/versions/node/v20.18.0/bin:/usr/local/bin
Environment=NVM_DIR=/home/<USER>/.nvm
Environment=PM2_HOME=/home/<USER>/.pm2

# CRITICAL: CORS Configuration Environment Variables for Restart Persistence
# These variables disable Express.js CORS when behind NGINX proxy
# This prevents duplicate Access-Control-Allow-Origin headers that cause browser rejection
Environment=NGINX_PROXY_MODE=true
Environment=EXPRESS_CORS_DISABLED=true
Environment=CORS_HANDLED_BY_NGINX=true

# Database Configuration
Environment=DB_HOST=localhost
Environment=DB_PORT=5432
Environment=DB_NAME=hauling_qr_system
Environment=DB_USER=postgres
Environment=DB_PASSWORD=PostgreSQLPassword123

# JWT Configuration
Environment=JWT_SECRET=hauling_qr_jwt_secret_2025_secure_key_for_production

# Server Configuration
Environment=SERVER_PORT=8080
Environment=BACKEND_HTTP_PORT=8080
Environment=PORT=8080

# Production Domain Configuration - DYNAMIC (will be replaced by deployment script)
Environment=PRODUCTION_DOMAIN=${PRODUCTION_DOMAIN}
Environment=API_BASE_URL=https://api.${PRODUCTION_DOMAIN}
Environment=FRONTEND_URL=https://${PRODUCTION_DOMAIN}
Environment=CLIENT_URL=https://${PRODUCTION_DOMAIN}

# Security Configuration
Environment=SECURE_COOKIES=true

# Logging Configuration
Environment=LOG_LEVEL=warn
Environment=SUPPRESS_CORS_CONFIG_MESSAGES=true
Environment=SUPPRESS_CORS_REQUEST_LOGS=true
Environment=SUPPRESS_CORS_PRODUCTION_LOGS=true

# Enhanced pre-start checks with CORS validation
ExecStartPre=/bin/bash -c 'echo "Starting Hauling QR Trip System with CORS configuration validation..."'
ExecStartPre=/bin/bash -c 'systemctl is-active --quiet postgresql || (echo "PostgreSQL not running" && exit 1)'
ExecStartPre=/bin/bash -c 'systemctl is-active --quiet nginx || (echo "NGINX not running" && exit 1)'
ExecStartPre=/bin/bash -c 'pg_isready -h localhost -p 5432 -U postgres -d hauling_qr_system || (echo "Database not accessible" && exit 1)'

# CRITICAL: Validate CORS environment variables are set
ExecStartPre=/bin/bash -c 'test "$NGINX_PROXY_MODE" = "true" || (echo "NGINX_PROXY_MODE not set to true - CORS may fail" && exit 1)'
ExecStartPre=/bin/bash -c 'test "$EXPRESS_CORS_DISABLED" = "true" || (echo "EXPRESS_CORS_DISABLED not set to true - duplicate CORS headers possible" && exit 1)'

# Validate .env file exists as fallback
ExecStartPre=/bin/bash -c 'test -f /var/www/hauling-qr-system/.env || (echo "Warning: .env file not found, relying on systemd environment variables")'

# Validate PM2 ecosystem configuration
ExecStartPre=/bin/bash -c 'test -f /var/www/hauling-qr-system/ecosystem.config.js || (echo "PM2 ecosystem.config.js not found" && exit 1)'

# Start command - Use shell to source NVM and find PM2
ExecStart=/bin/bash -c 'source /home/<USER>/.nvm/nvm.sh && pm2 start /var/www/hauling-qr-system/ecosystem.config.js --env production --no-daemon'

# Reload command
ExecReload=/bin/bash -c 'source /home/<USER>/.nvm/nvm.sh && pm2 reload /var/www/hauling-qr-system/ecosystem.config.js --env production'

# Stop command
ExecStop=/bin/bash -c 'source /home/<USER>/.nvm/nvm.sh && pm2 stop all'
ExecStop=/bin/bash -c 'source /home/<USER>/.nvm/nvm.sh && pm2 delete all'

# Enhanced health check with CORS validation
ExecStartPost=/bin/bash -c 'sleep 10 && curl -f http://localhost:8080/api/health || (echo "Health check failed" && exit 1)'

# CRITICAL: Validate CORS configuration is working - DYNAMIC DOMAIN
ExecStartPost=/bin/bash -c 'sleep 5 && curl -f -H "Origin: https://${PRODUCTION_DOMAIN}" http://localhost:8080/cors-test || (echo "CORS test failed - authentication may not work" && exit 1)'

# Validate PM2 process is running with correct environment
ExecStartPost=/bin/bash -c 'sleep 3 && sudo -u ubuntu pm2 list | grep -q "hauling-qr-server.*online" || (echo "PM2 process not running properly" && exit 1)'

# Restart configuration
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/hauling-qr-system /home/<USER>/.pm2 /tmp

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=hauling-qr-system

[Install]
WantedBy=multi-user.target
