// Hauling QR Trip Management System - Service Worker
// PWA Service Worker with Offline Support and Background Sync

const CACHE_NAME = 'hauling-qr-v1.9.1-pwa-fix';
const OFFLINE_CACHE = 'hauling-qr-offline-v1.9.1-pwa-fix';
const CHUNK_CACHE = 'hauling-qr-chunks-v1.9.1-getCachedApppwa-fix';

// Service Worker Configuration
const SW_CONFIG = {
  TIMEOUTS: {
    ASSET_FETCH: 10000,
    OFFLINE_ROUTE_NETWORK: 1500,
    ONLINE_ROUTE_NETWORK: 5000,
    REGULAR_ROUTE_NETWORK: 5000
  },
  BATCH_PROCESSING: {
    SIZE: 5,
    DELAY_BETWEEN_BATCHES: 50
  },
  SECURITY_HEADERS: {
    'Content-Security-Policy': "default-src 'self'; style-src 'unsafe-inline'; script-src 'unsafe-inline'",
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY'
  },
  // Production logging control
  PRODUCTION_LOGGING: false // Set to true for debugging in production
};

// Global variable to track PWA mode status from clients
let clientPWAMode = false;
let pwaStatusLastUpdated = null;

// Core app files to cache for offline functionality (ONLY for driver-connect)
const CORE_CACHE_FILES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/driver-connect'  // Only DriverConnect needs offline access
];

// PWA routes that MUST work offline (only DriverConnect)
const PWA_OFFLINE_ROUTES = [
  '/driver-connect'
];

// Online-only routes that should NOT be cached
const ONLINE_ONLY_ROUTES = [
  '/admin',
  '/dashboard', 
  '/analytics',
  '/analytics-reports',
  '/management',
  '/reports',
  '/users',
  '/trucks',
  '/drivers',
  '/driver-attendance',
  '/locations',
  '/assignments',
  '/assignment-monitoring',
  '/shifts',
  '/trips',
  '/scanner',
  '/settings',
  '/trip-scanner'  // Trip scanner is online-only
];

// All PWA routes (including online-only ones) - REMOVED trip-scanner
const PWA_ROUTES = [
  '/driver-connect'  // Only driver-connect is a true PWA route
];

// Check if route is PWA (any PWA route)
function isPWARoute(url) {
  const pathname = typeof url === 'string' ? url : url.pathname;

  return PWA_ROUTES.some(route => {
    return pathname === route || pathname.startsWith(route + '/');
  });
}

// Check if route should NEVER be cached (admin/dashboard routes)
function isOnlineOnlyRoute(url) {
  const pathname = typeof url === 'string' ? url : url.pathname;

  return ONLINE_ONLY_ROUTES.some(route => {
    return pathname === route || pathname.startsWith(route + '/');
  });
}

// Check if route MUST work offline (only DriverConnect)
function isOfflinePWARoute(url) {
  const pathname = typeof url === 'string' ? url : url.pathname;

  return PWA_OFFLINE_ROUTES.some(route => {
    return pathname === route || pathname.startsWith(route + '/');
  });
}

// Dynamic chunk caching strategy - removed unused variable

// PWA mode detection handled via client messages only

// Fetch and cache all JavaScript chunks from asset-manifest.json
async function loadAssetManifest() {
  try {
    const response = await fetch('/asset-manifest.json');
    const manifest = await response.json();

    // Extract all JavaScript, CSS, and WASM files
    const assets = [];
    for (const path of Object.values(manifest.files)) {
      if (path.endsWith('.js') || path.endsWith('.css') || path.endsWith('.wasm')) {
        assets.push(path);
      }
    }

    // Assets loaded and will be cached
    if (SW_CONFIG.PRODUCTION_LOGGING) {
      console.log('[SW] Loaded asset manifest:', assets.length, 'assets found');
    }
    return assets;
  } catch (error) {
    console.error('[SW] Failed to load asset manifest:', error);
    // Fallback to common chunk patterns including WASM
    return [
      '/static/css/main.css',
      '/static/js/main.js',
      '/static/js/vendors-node_modules_yudiel_react-qr-scanner_dist_index_esm_mjs.chunk.js'
    ];
  }
}

// API endpoints that can work offline (removed unused variable)

// Install event - cache core files, PWA routes, and all JavaScript chunks
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      // Cache core files and PWA routes
      caches.open(CACHE_NAME).then(async (cache) => {
        try {
          // Cache core files first
          await cache.addAll(CORE_CACHE_FILES);

          // CRITICAL: Cache React app for DriverConnect offline access
          if (SW_CONFIG.PRODUCTION_LOGGING) {
            console.log('[SW] CRITICAL: Starting React app caching for offline DriverConnect');
          }

          try {
            // Fetch the main React app
            const mainAppResponse = await fetch('/', {
              cache: 'no-cache',
              headers: {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
              }
            });

            if (mainAppResponse.ok) {
              if (SW_CONFIG.PRODUCTION_LOGGING) {
                console.log('[SW] ✅ Successfully fetched React app for caching');
              }

              // Cache the React app for multiple keys to ensure it's found
              const cachePromises = [
                cache.put('/', mainAppResponse.clone()),
                cache.put('/driver-connect', mainAppResponse.clone()),
                cache.put('/index.html', mainAppResponse.clone())
              ];

              await Promise.all(cachePromises);
              if (SW_CONFIG.PRODUCTION_LOGGING) {
                console.log('[SW] ✅ React app cached for all keys: /, /driver-connect, /index.html');
              }
            } else {
              console.error('[SW] ❌ Failed to fetch React app:', mainAppResponse.status, mainAppResponse.statusText);
            }
          } catch (mainAppError) {
            console.error('[SW] ❌ Error fetching React app for caching:', mainAppError);

            // If we can't fetch the main app, try to cache what we can
            try {
              await cache.add('/');
              if (SW_CONFIG.PRODUCTION_LOGGING) {
                console.log('[SW] ⚠️ Fallback: cached root path only');
              }
            } catch (rootError) {
              console.error('[SW] ❌ Even fallback caching failed:', rootError);
            }
          }

          // Ensure index.html is explicitly cached (if not already cached above)
          try {
            const existingIndex = await cache.match('/index.html');
            if (!existingIndex) {
              const indexResponse = await fetch('/index.html', {
                cache: 'no-cache',
                headers: {
                  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                }
              });

              if (indexResponse.ok) {
                await cache.put('/index.html', indexResponse);
              }
            }
          } catch (indexError) {
            // Continue even if index.html caching fails
          }

          // Cache essential React app files
          const essentialFiles = [
            '/static/css/main.css',
            '/static/js/main.js'
          ];

          for (const file of essentialFiles) {
            try {
              const response = await fetch(file);
              if (response.ok) {
                await cache.put(file, response);
              }
            } catch (fileError) {
              // Continue installation even if some files fail
            }
          }

        } catch (coreError) {
          // Try to cache at least the basic files
          try {
            await cache.add('/');
            await cache.add('/index.html');
          } catch (minimalError) {
            // Installation continues even if minimal caching fails
          }
        }
      }),

      // Load and cache JavaScript chunks
      loadAssetManifest().then(async (assets) => {
        try {
          const chunkCache = await caches.open(CHUNK_CACHE);
          await cacheInBatches(chunkCache, assets, 5);
        } catch (chunkError) {
          // Continue installation even if chunk caching fails
        }
      })
    ])
      .then(() => {
        return self.skipWaiting(); // Activate immediately
      })
      .catch(() => {
        // Still try to activate to prevent broken state
        return self.skipWaiting();
      })
  );
});

// Cache files in batches with improved performance and error handling
async function cacheInBatches(cache, urls, batchSize = 5) {
  const stats = { success: 0, failure: 0, skipped: 0 };
  const failedUrls = [];

  // Filter out already cached URLs to avoid unnecessary work
  const uncachedUrls = await filterUncachedUrls(cache, urls);
  if (uncachedUrls.length === 0) {
    if (SW_CONFIG.PRODUCTION_LOGGING) {
      console.log('[SW] All assets already cached');
    }
    return;
  }

  for (let i = 0; i < uncachedUrls.length; i += batchSize) {
    const batch = uncachedUrls.slice(i, i + batchSize);
    await processBatch(cache, batch, stats, failedUrls);

    // Add small delay between batches to prevent overwhelming the browser
    if (i + batchSize < uncachedUrls.length) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  logCachingResults(stats, failedUrls);
}

async function filterUncachedUrls(cache, urls) {
  const uncached = [];
  for (const url of urls) {
    try {
      const cached = await cache.match(url);
      if (!cached) uncached.push(url);
    } catch (error) {
      uncached.push(url); // Include if we can't check
    }
  }
  return uncached;
}

async function processBatch(cache, batch, stats, failedUrls) {
  try {
    const results = await Promise.allSettled(
      batch.map(url => cacheAsset(cache, url))
    );

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        stats.success++;
      } else {
        stats.failure++;
        failedUrls.push({
          url: batch[index],
          error: result.reason?.message || 'Unknown error'
        });
      }
    });
  } catch (batchError) {
    console.error('[SW] Batch processing error:', batchError);
    stats.failure += batch.length;
    batch.forEach(url => failedUrls.push({ url, error: batchError.message }));
  }
}

async function cacheAsset(cache, url) {
  try {
    const response = await fetch(url, {
      cache: 'no-cache',
      signal: AbortSignal.timeout(10000)
    });

    if (response.ok) {
      await cache.put(url, response);
      return { success: true, url };
    }
    throw new Error(`HTTP ${response.status}`);
  } catch (error) {
    console.warn('[SW] Failed to cache asset:', url, error.message);
    throw error;
  }
}

function logCachingResults(stats, failedUrls) {
  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log(`[SW] Asset caching completed: ${stats.success} successful, ${stats.failure} failed`);
  }

  if (failedUrls.length > 0 && failedUrls.length <= 5) {
    console.warn('[SW] Failed URLs:', failedUrls.map(f => f.url));
  } else if (failedUrls.length > 5) {
    console.warn(`[SW] ${failedUrls.length} URLs failed to cache`);
  }
}

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] Activating service worker...');
  }

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME &&
              cacheName !== OFFLINE_CACHE &&
              cacheName !== CHUNK_CACHE) {
              if (SW_CONFIG.PRODUCTION_LOGGING) {
                console.log('[SW] Deleting old cache:', cacheName);
              }
              return caches.delete(cacheName);
            }
            return Promise.resolve(); // Return resolved promise for non-matching caches
          })
        );
      })
      .then(() => {
        if (SW_CONFIG.PRODUCTION_LOGGING) {
          console.log('[SW] Service worker activated');
        }
        return self.clients.claim(); // Take control immediately
      })
  );
});

// Fetch event - handle network requests with offline fallback
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }

  // CRITICAL: Skip ALL webpack hot-update files and development resources
  // This prevents the service worker from intercepting these requests entirely
  if (url.pathname.includes('.hot-update.') ||
    url.pathname.includes('webpack') ||
    url.pathname.includes('sockjs-node') ||
    url.pathname.includes('__webpack_dev_server__') ||
    url.search.includes('hot=true') ||
    url.search.includes('live-reload') ||
    url.pathname.includes('.chunk.js.map') ||
    url.pathname.includes('main.') ||
    url.pathname.match(/\w+\.\w+\.hot-update\.(js|json)$/) ||
    url.href.includes('hot-update')) {
    // COMPLETELY BYPASS service worker for these requests
    if (SW_CONFIG.PRODUCTION_LOGGING) {
      console.log('[SW] BYPASSING hot-update file:', url.pathname);
    }
    return;
  }

  // Check if the request is coming from PWA pages using enhanced detection
  const referrer = request.referrer ? new URL(request.referrer) : null;
  const isPWAPage = referrer && isPWARoute(referrer);

  // Handle API requests - ONLY intercept if from PWA pages AND offline
  if (url.pathname.startsWith('/api/')) {
    // Only intercept API requests from PWA pages when offline
    if (isPWAPage && !navigator.onLine) {
      event.respondWith(handleApiRequest(request));
    }
    // Otherwise, let all API requests pass through normally
    return;
  }

  // Handle app navigation
  if (request.mode === 'navigate') {
    // CRITICAL: Don't intercept admin/dashboard routes - let them fail naturally
    if (isOnlineOnlyRoute(url)) {
      if (SW_CONFIG.PRODUCTION_LOGGING) {
        console.log('[SW] BYPASSING online-only route:', url.pathname);
      }
      return; // Let browser handle normally - will show "site can't be reached" when offline
    }
    
    event.respondWith(handleNavigation(request));
    return;
  }

  // Handle static assets (including WASM files)
  event.respondWith(handleStaticAssets(request));
});

// Handle API requests with offline fallback (only called when offline from PWA pages)
async function handleApiRequest(request) {
  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] Handling offline API request from PWA page');
  }

  // Try cache first for offline requests
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    if (SW_CONFIG.PRODUCTION_LOGGING) {
      console.log('[SW] Serving API response from cache for PWA page');
    }
    return cachedResponse;
  }

  // Return offline indicator for scan endpoints
  if (request.url.includes('/api/scanner/')) {
    return new Response(
      JSON.stringify({
        success: false,
        offline: true,
        message: 'Offline mode - scan will be queued for sync'
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  // Return offline indicator for driver endpoints
  if (request.url.includes('/api/driver/')) {
    return new Response(
      JSON.stringify({
        success: false,
        offline: true,
        message: 'Offline mode - connection will be queued for sync'
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  // For other API endpoints from PWA pages, return a generic offline response
  return new Response(
    JSON.stringify({
      success: false,
      offline: true,
      message: 'Service unavailable offline'
    }),
    {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}

// Get cached React app with multiple cache key fallbacks
async function getCachedApp() {
  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] 🔍 SEARCHING for cached React app...');
  }

  // Cache keys to try in order of preference (DriverConnect focused)
  const cacheKeys = [
    '/driver-connect',  // Try DriverConnect first
    '/',
    '/index.html',
    new URL('/', self.location.origin).href,
    new URL('/index.html', self.location.origin).href,
    self.location.origin + '/',
    self.location.origin + '/index.html'
  ];

  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] Cache keys to try:', cacheKeys);
  }

  // Try all cache stores in priority order
  const cacheStores = [CACHE_NAME, OFFLINE_CACHE, CHUNK_CACHE];

  for (const cacheName of cacheStores) {
    if (SW_CONFIG.PRODUCTION_LOGGING) {
      console.log('[SW] Checking cache store:', cacheName);
    }
    try {
      const cache = await caches.open(cacheName);

      for (const key of cacheKeys) {
        try {
          const response = await cache.match(key, {
            ignoreSearch: true,
            ignoreVary: true
          });

          if (response && response.ok) {
            const contentType = response.headers.get('content-type') || '';
            if (SW_CONFIG.PRODUCTION_LOGGING) {
              console.log('[SW] Found response for key:', key, 'Content-Type:', contentType);
            }

            if (contentType.includes('text/html') || key.endsWith('.html') || key === '/') {
              if (SW_CONFIG.PRODUCTION_LOGGING) {
                console.log('[SW] ✅ FOUND VALID CACHED REACT APP with key:', key);
              }
              return response.clone();
            } else {
              if (SW_CONFIG.PRODUCTION_LOGGING) {
                console.log('[SW] Response not HTML, continuing search...');
              }
            }
          } else {
            if (SW_CONFIG.PRODUCTION_LOGGING) {
              console.log('[SW] No response or not ok for key:', key);
            }
          }
        } catch (keyError) {
          console.warn('[SW] Error checking key:', key, keyError);
        }
      }
    } catch (storeError) {
      console.warn('[SW] Error accessing cache store:', cacheName, storeError);
    }
  }

  // Final attempt: find any HTML file that could serve as the React app
  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] Final attempt: searching for any HTML file...');
  }
  try {
    const cache = await caches.open(CACHE_NAME);
    const requests = await cache.keys();
    if (SW_CONFIG.PRODUCTION_LOGGING) {
      console.log('[SW] Cache contents:', requests.map(r => r.url));
    }

    for (const request of requests) {
      const url = new URL(request.url);
      if (url.pathname.endsWith('.html') || url.pathname === '/') {
        const response = await cache.match(request);
        if (response && response.ok) {
          if (SW_CONFIG.PRODUCTION_LOGGING) {
            console.log('[SW] ✅ Found HTML fallback:', url.pathname);
          }
          return response.clone();
        }
      }
    }
  } catch (fallbackError) {
    console.error('[SW] Error in fallback search:', fallbackError);
  }

  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] ❌ NO CACHED REACT APP FOUND ANYWHERE!');
  }
  return null;
}

// CRITICAL: Force get cached React app - more aggressive search
async function forceGetCachedReactApp(_url) {
  // Try every possible way to find the cached React app
  const strategies = [
    // Strategy 1: Try getCachedApp again
    async () => {
      const cached = await getCachedApp();
      if (cached) return cached;
      throw new Error('No cached app');
    },

    // Strategy 2: Search all caches for ANY HTML content
    async () => {
      const allCacheNames = await caches.keys();
      for (const cacheName of allCacheNames) {
        try {
          const cache = await caches.open(cacheName);
          const requests = await cache.keys();

          for (const request of requests) {
            const reqUrl = new URL(request.url);
            // Look for any HTML file
            if (reqUrl.pathname === '/' ||
              reqUrl.pathname.endsWith('.html') ||
              reqUrl.pathname.includes('index') ||
              reqUrl.pathname === '/driver-connect') {

              const response = await cache.match(request);
              if (response && response.ok) {
                const contentType = response.headers.get('content-type') || '';
                if (contentType.includes('text/html') ||
                  reqUrl.pathname.endsWith('.html') ||
                  reqUrl.pathname === '/') {
                  return response.clone();
                }
              }
            }
          }
        } catch (cacheError) {
          // Continue to next cache
        }
      }
      throw new Error('No HTML found in any cache');
    },

    // Strategy 3: NEVER give up - create functional DriverConnect interface
    async () => {
      return createDriverConnectPWAResponse();
    }
  ];

  // Try each strategy
  for (const strategy of strategies) {
    try {
      const response = await strategy();
      if (response) return response;
    } catch (error) {
      // Continue to next strategy
    }
  }

  // This should never happen, but just in case
  return createDriverConnectPWAResponse();
}

// Navigation handler optimized for PWA offline access
let navigationInProgress = new Set();

async function handleNavigation(request) {
  const url = new URL(request.url);
  const requestKey = `${request.method}:${url.pathname}`;

  if (navigationInProgress.has(requestKey)) {
    return new Response('Navigation in progress', { status: 202 });
  }

  navigationInProgress.add(requestKey);

  try {
    const isOfflineRoute = isOfflinePWARoute(url);
    
    if (isOfflineRoute) {
      // ONLY DriverConnect: Cache-first, must work offline
      if (SW_CONFIG.PRODUCTION_LOGGING) {
        console.log('[SW] Handling offline PWA route (driver-connect):', url.pathname);
      }
      return await handleOfflinePWARoute(request, url);
    } else {
      // ALL other routes: Network-first, no special caching
      if (SW_CONFIG.PRODUCTION_LOGGING) {
        console.log('[SW] Handling regular route (no special caching):', url.pathname);
      }
      return await handleRegularRouteNavigation(request);
    }
  } finally {
    navigationInProgress.delete(requestKey);
  }
}

// Base class for navigation handlers (DEPRECATED - removed unused class)

// DriverConnect: Cache-first, MUST work offline
async function handleOfflinePWARoute(request, url) {
  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] 🎯 HANDLING OFFLINE PWA ROUTE:', url.pathname);
  }

  // Step 1: ALWAYS try cache first - this is CRITICAL for offline PWA
  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] Step 1: Trying cache first for DriverConnect');
  }
  try {
    const cachedResponse = await getCachedApp();
    if (cachedResponse) {
      if (SW_CONFIG.PRODUCTION_LOGGING) {
        console.log('[SW] ✅ SUCCESS: Found cached React app - serving immediately!');
      }
      return cachedResponse;
    } else {
      if (SW_CONFIG.PRODUCTION_LOGGING) {
        console.log('[SW] ❌ No cached React app found in getCachedApp()');
      }
    }
  } catch (cacheError) {
    console.error('[SW] ❌ Cache lookup error:', cacheError);
  }

  // Step 2: If online, try network with very short timeout (PWA should be cache-first)
  if (navigator.onLine) {
    if (SW_CONFIG.PRODUCTION_LOGGING) {
      console.log('[SW] Step 2: Online detected, trying network with short timeout');
    }
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 1000); // Very aggressive timeout

      const response = await fetch(request, {
        signal: controller.signal,
        cache: 'no-cache'
      });
      clearTimeout(timeoutId);

      if (response.ok) {
        if (SW_CONFIG.PRODUCTION_LOGGING) {
          console.log('[SW] ✅ Network response successful, caching for future');
        }
        // Cache the response for future offline use
        try {
          const cache = await caches.open(CACHE_NAME);
          await cache.put(request, response.clone());
        } catch (cacheStoreError) {
          console.warn('[SW] Failed to cache network response:', cacheStoreError);
        }
        return response;
      }
    } catch (networkError) {
      if (SW_CONFIG.PRODUCTION_LOGGING) {
        console.log('[SW] Network failed (expected for offline):', networkError.message);
      }
    }
  } else {
    if (SW_CONFIG.PRODUCTION_LOGGING) {
      console.log('[SW] Step 2: OFFLINE detected - skipping network attempt');
    }
  }

  // Step 3: CRITICAL - Try harder to find cached React app before giving up
  if (SW_CONFIG.PRODUCTION_LOGGING) {
    console.log('[SW] Step 3: CRITICAL FALLBACK - Force searching for cached React app');
  }
  return await forceGetCachedReactApp(url);
}

// Trip Scanner: Network-first, online only (DEPRECATED - removed unused function)

// Create reusable internet required response (DEPRECATED - removed unused function)

// Regular route navigation (non-PWA)
async function handleRegularRouteNavigation(request) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), SW_CONFIG.TIMEOUTS.REGULAR_ROUTE_NETWORK);

  try {
    const response = await fetch(request, {
      signal: controller.signal,
      cache: 'no-cache'
    });
    clearTimeout(timeoutId);

    if (response.ok) {
      return response;
    }
    throw new Error(`Navigation failed: ${response.status}`);
  } catch (fetchError) {
    clearTimeout(timeoutId);

    // Try cache as fallback for regular routes
    try {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    } catch (cacheError) {
      // No cache available
    }

    throw fetchError;
  }
}

// Create a proper DriverConnect PWA response that mimics the actual interface
function createDriverConnectPWAResponse() {
  // This should only be called for DriverConnect routes
  const routeName = 'Driver Connect';

  return new Response(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8" />
      <link rel="icon" href="/favicon.ico" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="theme-color" content="#000000" />
      <meta name="description" content="Hauling QR Trip Management System - ${routeName}" />
      <title>${routeName}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          background: #f8f9fa; color: #333; min-height: 100vh;
        }
        .app-container { min-height: 100vh; display: flex; flex-direction: column; }
        .header { 
          background: #007bff; color: white; padding: 1rem; text-align: center;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 { font-size: 1.5rem; font-weight: 600; }
        .main-content { 
          flex: 1; padding: 2rem 1rem; max-width: 600px; margin: 0 auto; width: 100%;
        }
        .offline-banner {
          background: #ffc107; color: #856404; padding: 0.75rem; border-radius: 8px;
          margin-bottom: 1.5rem; text-align: center; font-weight: 500;
        }
        .scanner-section {
          background: white; border-radius: 12px; padding: 2rem; margin-bottom: 1.5rem;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;
        }
        .scanner-icon { font-size: 4rem; margin-bottom: 1rem; }
        .scanner-title { font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; }
        .scanner-subtitle { color: #666; margin-bottom: 1.5rem; }
        .scan-button {
          background: #007bff; color: white; border: none; padding: 1rem 2rem;
          border-radius: 8px; font-size: 1.1rem; font-weight: 600; cursor: pointer;
          width: 100%; margin-bottom: 1rem; transition: background 0.2s;
        }
        .scan-button:hover { background: #0056b3; }
        .scan-button:disabled { background: #ccc; cursor: not-allowed; }
        .manual-input {
          width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 8px;
          font-size: 1rem; margin-bottom: 1rem;
        }
        .status-section {
          background: white; border-radius: 12px; padding: 1.5rem;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status-item {
          display: flex; justify-content: space-between; align-items: center;
          padding: 0.75rem 0; border-bottom: 1px solid #eee;
        }
        .status-item:last-child { border-bottom: none; }
        .status-label { font-weight: 500; }
        .status-value { color: #666; }
        .loading-spinner {
          border: 3px solid #f3f3f3; border-top: 3px solid #007bff;
          border-radius: 50%; width: 30px; height: 30px;
          animation: spin 1s linear infinite; margin: 1rem auto;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .retry-section { text-align: center; margin-top: 1rem; }
        .retry-button {
          background: #28a745; color: white; border: none; padding: 0.75rem 1.5rem;
          border-radius: 6px; font-size: 1rem; cursor: pointer; margin: 0 0.5rem;
        }
        .retry-button:hover { background: #218838; }
      </style>
    </head>
    <body>
      <div class="app-container">
        <header class="header">
          <h1>${routeName}</h1>
        </header>
        
        <main class="main-content">
          <div class="offline-banner">
            📱 Offline Mode - Limited functionality available
          </div>
          
          <div class="scanner-section">
            <div class="scanner-icon">👤</div>
            <h2 class="scanner-title">Driver Authentication</h2>
            <p class="scanner-subtitle">Scan your driver QR code or enter manually</p>
            
            <button class="scan-button" onclick="startDriverScan()" disabled>
              📷 Scan Driver QR Code (Offline)
            </button>
            
            <input type="text" class="manual-input" placeholder="Enter Driver ID manually" 
                   id="driverInput" onchange="handleDriverInput(this.value)">
          </div>
          
          <div class="scanner-section" id="truckSection" style="display: none;">
            <div class="scanner-icon">🚛</div>
            <h2 class="scanner-title">Truck Selection</h2>
            <p class="scanner-subtitle">Scan truck QR code or enter manually</p>
            
            <button class="scan-button" onclick="startTruckScan()" disabled>
              📷 Scan Truck QR Code (Offline)
            </button>
            
            <input type="text" class="manual-input" placeholder="Enter Truck ID manually" 
                   id="truckInput" onchange="handleTruckInput(this.value)">
          </div>
          
          <div class="scanner-section" id="actionSection" style="display: none;">
            <div class="scanner-icon">✅</div>
            <h2 class="scanner-title">Select Action</h2>
            <p class="scanner-subtitle">Choose your check-in or check-out action</p>
            
            <button class="scan-button" onclick="handleAction('checkin')" style="background: #28a745;">
              ✅ Check In
            </button>
            <button class="scan-button" onclick="handleAction('checkout')" style="background: #dc3545;">
              ❌ Check Out
            </button>
          </div>
          
          <div class="status-section">
            <h3 style="margin-bottom: 1rem;">Status</h3>
            <div class="status-item">
              <span class="status-label">Connection</span>
              <span class="status-value" id="connectionStatus">Offline</span>
            </div>
            <div class="status-item">
              <span class="status-label">Mode</span>
              <span class="status-value">PWA Offline</span>
            </div>
            <div class="status-item">
              <span class="status-label">Cached Data</span>
              <span class="status-value" id="cachedDataStatus">Available</span>
            </div>
          </div>
          
          <div class="retry-section">
            <div class="loading-spinner" id="loadingSpinner"></div>
            <p>Attempting to load full application...</p>
            <button class="retry-button" onclick="retryFullApp()">Reload App</button>
            <button class="retry-button" onclick="clearCacheAndReload()">Clear Cache</button>
          </div>
        </main>
      </div>

      <script>
        // DriverConnect PWA Offline Functionality
        let currentDriver = null;
        let currentTruck = null;
        
        function handleDriverInput(value) {
          if (value.trim()) {
            currentDriver = value.trim();
            document.getElementById('truckSection').style.display = 'block';
            console.log('[PWA] Driver authenticated offline:', currentDriver);
          }
        }
        
        function handleTruckInput(value) {
          if (value.trim()) {
            currentTruck = value.trim();
            document.getElementById('actionSection').style.display = 'block';
            console.log('[PWA] Truck selected offline:', currentTruck);
          }
        }
        
        function handleAction(action) {
          if (currentDriver && currentTruck) {
            const actionData = {
              driver: currentDriver,
              truck: currentTruck,
              action: action,
              timestamp: new Date().toISOString(),
              offline: true
            };
            
            // Store in localStorage for sync when online
            const offlineActions = JSON.parse(localStorage.getItem('offlineActions') || '[]');
            offlineActions.push(actionData);
            localStorage.setItem('offlineActions', JSON.stringify(offlineActions));
            
            alert(\`\${action === 'checkin' ? 'Check-in' : 'Check-out'} recorded offline!\\nDriver: \${currentDriver}\\nTruck: \${currentTruck}\\n\\nWill sync when online.\`);
            
            // Reset for next action
            currentDriver = null;
            currentTruck = null;
            document.getElementById('driverInput').value = '';
            document.getElementById('truckInput').value = '';
            document.getElementById('truckSection').style.display = 'none';
            document.getElementById('actionSection').style.display = 'none';
          }
        }
        

        
        function retryFullApp() {
          console.log('[PWA] Attempting to reload full application');
          window.location.reload();
        }
        
        function clearCacheAndReload() {
          if ('caches' in window) {
            caches.keys().then(names => {
              return Promise.all(names.map(name => caches.delete(name)));
            }).then(() => {
              window.location.reload(true);
            });
          } else {
            window.location.reload(true);
          }
        }
        
        // Connection monitoring
        function updateConnectionStatus() {
          const status = document.getElementById('connectionStatus');
          if (navigator.onLine) {
            status.textContent = 'Online';
            status.style.color = '#28a745';
            // Try to reload full app when online
            setTimeout(() => {
              if (confirm('Connection restored! Load full application?')) {
                retryFullApp();
              }
            }, 1000);
          } else {
            status.textContent = 'Offline';
            status.style.color = '#dc3545';
          }
        }
        
        // Auto-retry loading full app
        let retryCount = 0;
        function attemptFullAppLoad() {
          retryCount++;
          if (retryCount <= 3 && navigator.onLine) {
            console.log('[PWA] Auto-retry attempt:', retryCount);
            setTimeout(() => {
              window.location.reload();
            }, 2000 * retryCount);
          } else if (retryCount > 3) {
            document.getElementById('loadingSpinner').style.display = 'none';
          }
        }
        
        // Initialize
        updateConnectionStatus();
        attemptFullAppLoad();
        
        // Event listeners
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        console.log('[PWA] ${routeName} offline interface loaded');
        console.log('[PWA] Service Worker active:', !!navigator.serviceWorker?.controller);
        console.log('[PWA] Offline actions stored:', localStorage.getItem('offlineActions'));
      </script>
    </body>
    </html>
  `, {
    status: 200,
    headers: {
      'Content-Type': 'text/html',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  });
}

// Handle static assets with enhanced chunk caching
async function handleStaticAssets(request) {
  try {
    const url = new URL(request.url);

    // NEVER cache development/hot-update files - AGGRESSIVE CHECK
    if (url.pathname.includes('.hot-update.') ||
      url.pathname.includes('webpack') ||
      url.pathname.includes('sockjs-node') ||
      url.pathname.includes('__webpack_dev_server__') ||
      url.pathname.includes('main.') ||
      url.pathname.match(/\w+\.\w+\.hot-update\.(js|json)$/) ||
      url.href.includes('hot-update')) {
      console.log('[SW] NEVER CACHING hot-update file:', url.pathname);
      return fetch(request); // Always fetch from network, never cache
    }

    // Try cache first - check all cache stores
    let cachedResponse = await caches.match(request);
    if (!cachedResponse) {
      // Check chunk cache specifically for JavaScript and WASM files
      if (request.url.includes('/static/js/') ||
        request.url.includes('.chunk.js') ||
        request.url.includes('.wasm')) {
        cachedResponse = await caches.match(request, { cacheName: CHUNK_CACHE });
      }
    }

    if (cachedResponse) {
      // Reduced logging - only log for non-hot-update files
      if (!request.url.includes('.hot-update.')) {
        console.log('[SW] Serving from cache:', request.url);
      }
      return cachedResponse;
    }

    // If online, fetch from network and cache (but not development files)
    if (navigator.onLine) {
      const response = await fetch(request);
      if (response.ok && !url.pathname.includes('.hot-update.')) {
        // Determine which cache to use
        const cache = (request.url.includes('/static/js/') ||
          request.url.includes('.chunk.js') ||
          request.url.includes('.wasm'))
          ? await caches.open(CHUNK_CACHE)
          : await caches.open(CACHE_NAME);
        cache.put(request, response.clone());
        // Reduced logging - only log important assets, not hot-updates
        if (!request.url.includes('.hot-update.')) {
          console.log('[SW] Cached new asset:', request.url);
        }
      }
      return response;
    } else {
      // Offline and no cache - provide fallback
      throw new Error('Asset not available offline');
    }
  } catch (error) {
    console.log('[SW] Static asset failed:', request.url, error.message);

    // For JavaScript chunks, provide a fallback that prevents app crash
    if (request.url.includes('.chunk.js') || request.url.includes('/static/js/')) {
      return new Response(
        `console.warn('Chunk ${request.url} not available offline - providing fallback');`,
        {
          status: 200,
          headers: { 'Content-Type': 'application/javascript' }
        }
      );
    }

    // For WASM files, provide a specific error that the QR scanner can handle
    if (request.url.includes('.wasm')) {
      console.warn('[SW] WASM file not available offline:', request.url);
      // Return a 404 so the QR scanner can fall back to alternative detection methods
      return new Response('WASM file not available offline', {
        status: 404,
        statusText: 'Not Found'
      });
    }

    throw error;
  }
}

// Removed unused shouldCacheApiResponse function

// Background Sync for offline data
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);

  // Check if this is Driver Connect PWA - skip background sync to avoid conflicts with manual sync
  const isDriverConnectPWA = clientPWAMode === true &&
    (self.registration.scope.includes('/driver-connect') ||
      event.tag === 'driver-connect-sync');

  if (isDriverConnectPWA && event.tag === 'driver-connect-sync') {
    console.log('[SW] Skipping driver-connect-sync for PWA to avoid manual sync conflicts');
    return;
  }

  if (event.tag === 'trip-scan-sync') {
    event.waitUntil(syncOfflineData('trip-scans'));
  } else if (event.tag === 'driver-connect-sync') {
    event.waitUntil(syncOfflineData('driver-connections'));
  } else if (event.tag === 'comprehensive-sync') {
    event.waitUntil(syncOfflineData('all'));
  }
});

// Sync offline data using centralized background sync service
async function syncOfflineData(syncType) {
  console.log(`[SW] Starting ${syncType} sync...`);

  try {
    // Send message to main thread to trigger sync
    const clients = await self.clients.matchAll();

    for (const client of clients) {
      client.postMessage({
        type: 'TRIGGER_SYNC',
        syncType: syncType,
        source: 'service-worker'
      });
    }

    console.log(`[SW] ${syncType} sync message sent to clients`);
  } catch (error) {
    console.error(`[SW] Failed to trigger ${syncType} sync:`, error);
  }
}

// Removed unused syncOfflineConnections function

// Enhanced message handling for communication with main app with comprehensive error handling
self.addEventListener('message', (event) => {
  try {
    console.log('[SW] Received message:', event.data);

    // Validate message structure
    if (!event.data || typeof event.data !== 'object') {
      console.warn('[SW] Invalid message format received:', event.data);
      return;
    }

    const messageType = event.data.type;
    const messageSource = event.data.source || 'unknown';
    const messageTimestamp = event.data.timestamp || new Date().toISOString();

    console.log('[SW] Processing message type:', messageType, 'from:', messageSource, 'at:', messageTimestamp);

    // Handle skip waiting requests
    if (messageType === 'SKIP_WAITING') {
      try {
        console.log('[SW] Processing skip waiting request');
        self.skipWaiting();
      } catch (skipError) {
        console.error('[SW] Error during skip waiting:', skipError);
      }
    }

    // Handle cache status requests
    if (messageType === 'GET_CACHE_STATUS') {
      try {
        console.log('[SW] Processing cache status request');
        if (event.ports && event.ports[0]) {
          event.ports[0].postMessage({
            type: 'CACHE_STATUS',
            cached: true, // We'll implement proper cache checking later
            timestamp: new Date().toISOString(),
            source: 'service-worker'
          });
        } else {
          console.warn('[SW] No port available for cache status response');
        }
      } catch (cacheError) {
        console.error('[SW] Error handling cache status request:', cacheError);
      }
    }

    // Handle PWA debug test messages
    if (messageType === 'PWA_DEBUG_TEST') {
      try {
        console.log('[SW] PWA debug test received:', event.data);

        // Send debug response with service worker status
        if (event.source) {
          event.source.postMessage({
            type: 'PWA_DEBUG_RESPONSE',
            timestamp: new Date().toISOString(),
            source: 'service-worker',
            serviceWorkerInfo: {
              scope: self.registration?.scope,
              state: self.registration?.active?.state,
              clientPWAMode: clientPWAMode,
              pwaStatusLastUpdated: pwaStatusLastUpdated,
              cacheNames: [CACHE_NAME, OFFLINE_CACHE, CHUNK_CACHE]
            },
            requestId: event.data.requestId || null
          });
          console.log('[SW] PWA debug response sent');
        }
      } catch (debugError) {
        console.error('[SW] Error handling PWA debug test:', debugError);
      }
    }

    // Enhanced PWA mode status handling from clients
    if (messageType === 'PWA_MODE_STATUS') {
      try {
        const previousMode = clientPWAMode;
        const newMode = event.data.isPWA;

        // Validate PWA mode value
        if (typeof newMode !== 'boolean') {
          console.warn('[SW] Invalid PWA mode value received:', newMode, 'expected boolean');
          return;
        }

        clientPWAMode = newMode;
        pwaStatusLastUpdated = Date.now();

        console.log('[SW] PWA mode status updated:', clientPWAMode, 'at', new Date().toISOString());

        // Log mode changes with enhanced context
        if (previousMode !== clientPWAMode) {
          console.log('[SW] PWA mode changed from', previousMode, 'to', clientPWAMode);

          // Log additional context for mode changes
          console.log('[SW] PWA mode change context:', {
            previousMode,
            newMode: clientPWAMode,
            source: messageSource,
            timestamp: messageTimestamp,
            currentPath: event.data.currentPath,
            userAgent: event.data.userAgent?.substring(0, 100) + '...' || 'unknown'
          });
        }

        // Log additional details if provided
        if (event.data.currentPath) {
          console.log('[SW] PWA mode status for path:', event.data.currentPath);
        }
        if (event.data.displayMode) {
          console.log('[SW] Display mode:', event.data.displayMode);
        }
        if (event.data.detectionMethods) {
          console.log('[SW] PWA detection methods used:', event.data.detectionMethods);
        }

        // Send confirmation back to client with error handling
        if (event.source) {
          try {
            event.source.postMessage({
              type: 'PWA_MODE_CONFIRMATION',
              confirmedMode: clientPWAMode,
              previousMode: previousMode,
              timestamp: new Date().toISOString(),
              source: 'service-worker',
              requestId: event.data.requestId || null
            });
            console.log('[SW] PWA mode confirmation sent to client');
          } catch (confirmError) {
            console.error('[SW] Error sending PWA mode confirmation:', confirmError);
          }
        } else {
          console.warn('[SW] No event source available for PWA mode confirmation');
        }

      } catch (pwaError) {
        console.error('[SW] Error processing PWA mode status:', pwaError);
      }
    }

    // Handle location change notifications with enhanced logging
    if (messageType === 'LOCATION_CHANGE') {
      try {
        console.log('[SW] Location change notification:', event.data.currentPath, 'PWA:', event.data.isPWA);

        // Update PWA mode if provided and valid
        if (typeof event.data.isPWA === 'boolean') {
          const previousMode = clientPWAMode;
          clientPWAMode = event.data.isPWA;
          pwaStatusLastUpdated = Date.now();

          if (previousMode !== clientPWAMode) {
            console.log('[SW] PWA mode updated via location change from', previousMode, 'to', clientPWAMode);
          }
        }

        // Log navigation context
        console.log('[SW] Navigation context:', {
          path: event.data.currentPath,
          isPWA: event.data.isPWA,
          source: messageSource,
          timestamp: messageTimestamp
        });

      } catch (locationError) {
        console.error('[SW] Error processing location change:', locationError);
      }
    }

    // Handle client ready notifications with enhanced response
    if (messageType === 'CLIENT_READY') {
      try {
        console.log('[SW] Client ready notification received from:', messageSource);

        // Log client capabilities if provided
        if (event.data.capabilities) {
          console.log('[SW] Client capabilities:', event.data.capabilities);
        }

        // Send service worker ready confirmation with enhanced info
        if (event.source) {
          try {
            event.source.postMessage({
              type: 'SERVICE_WORKER_READY',
              timestamp: new Date().toISOString(),
              currentPWAMode: clientPWAMode,
              pwaStatusAge: pwaStatusLastUpdated ? Date.now() - pwaStatusLastUpdated : null,
              source: 'service-worker',
              swVersion: CACHE_NAME,
              capabilities: {
                backgroundSync: 'sync' in self.registration,
                pushNotifications: 'showNotification' in self.registration,
                cacheAPI: 'caches' in self
              }
            });
            console.log('[SW] Service worker ready confirmation sent');
          } catch (readyError) {
            console.error('[SW] Error sending ready confirmation:', readyError);
          }
        } else {
          console.warn('[SW] No event source available for ready confirmation');
        }

      } catch (clientReadyError) {
        console.error('[SW] Error processing client ready notification:', clientReadyError);
      }
    }

    // Handle legacy PWA mode information (for backward compatibility)
    if (messageType === 'PWA_MODE_INFO') {
      try {
        console.log('[SW] Processing legacy PWA mode info');
        const previousMode = clientPWAMode;
        clientPWAMode = event.data.isPWAMode;
        pwaStatusLastUpdated = Date.now();

        console.log('[SW] PWA mode updated (legacy):', clientPWAMode, 'for path:', event.data.currentPath);

        if (previousMode !== clientPWAMode) {
          console.log('[SW] Legacy PWA mode change from', previousMode, 'to', clientPWAMode);
        }

      } catch (legacyError) {
        console.error('[SW] Error processing legacy PWA mode info:', legacyError);
      }
    }

    // Handle sync trigger messages from main thread
    if (messageType === 'TRIGGER_SYNC') {
      try {
        const { syncType } = event.data;
        console.log(`[SW] Triggering ${syncType} sync from main thread`);

        // Validate sync type
        if (!syncType || typeof syncType !== 'string') {
          console.warn('[SW] Invalid sync type received:', syncType);
          return;
        }

        // Delegate sync back to main thread via message
        if (event.source) {
          try {
            event.source.postMessage({
              type: 'SYNC_DELEGATED',
              syncType: syncType,
              message: `${syncType} sync delegated to main thread`,
              timestamp: new Date().toISOString(),
              source: 'service-worker'
            });
            console.log(`[SW] ${syncType} sync delegation message sent`);
          } catch (syncError) {
            console.error('[SW] Error sending sync delegation message:', syncError);
          }
        } else {
          console.warn('[SW] No event source available for sync delegation');
        }

      } catch (triggerError) {
        console.error('[SW] Error processing sync trigger:', triggerError);
      }
    }

    // Enhanced requests for PWA mode status from service worker
    if (messageType === 'REQUEST_PWA_MODE') {
      try {
        console.log('[SW] Processing PWA mode request from client');
        console.log('[SW] Current PWA mode status:', {
          mode: clientPWAMode,
          lastUpdated: pwaStatusLastUpdated,
          age: pwaStatusLastUpdated ? Date.now() - pwaStatusLastUpdated : null
        });

        // This message will be handled by the PWA status hook
        // The hook will respond with enhanced PWA mode detection
        // No direct response needed here as the client will send PWA_MODE_STATUS

      } catch (requestError) {
        console.error('[SW] Error processing PWA mode request:', requestError);
      }
    }

    // ENHANCED: Handle cache strategy updates for PWA pages
    if (messageType === 'UPDATE_CACHE_STRATEGY') {
      try {
        console.log('[SW] Cache strategy update requested for pages:', event.data.pages);

        const pagesToCache = event.data.pages || [];

        // Use async IIFE to handle await
        (async () => {
          const cache = await caches.open(CACHE_NAME);

          // Force cache the specified pages
          for (const page of pagesToCache) {
            try {
              // Cache the main app for SPA routing
              const mainAppResponse = await fetch('/');
              if (mainAppResponse.ok) {
                await cache.put(page, mainAppResponse.clone());
                await cache.put('/', mainAppResponse.clone());
                console.log(`[SW] Force cached page: ${page}`);
              }
            } catch (cacheError) {
              console.error(`[SW] Failed to cache page ${page}:`, cacheError);
            }
          }

          // Send confirmation back to client
          const clients = await self.clients.matchAll();
          clients.forEach(client => {
            client.postMessage({
              type: 'CACHE_STRATEGY_UPDATED',
              pages: pagesToCache,
              timestamp: new Date().toISOString()
            });
          });
        })().catch(error => {
          console.error('[SW] Error in cache strategy update:', error);
        });

      } catch (error) {
        console.error('[SW] Error updating cache strategy:', error);
      }
    }

    // ENHANCED: Handle diagnostic test messages
    if (messageType === 'DIAGNOSTIC_TEST') {
      try {
        console.log('[SW] Diagnostic test message received');

        // Use async IIFE to handle await
        (async () => {
          // Send diagnostic response
          const clients = await self.clients.matchAll();
          const cacheNames = await caches.keys();

          clients.forEach(client => {
            client.postMessage({
              type: 'DIAGNOSTIC_RESPONSE',
              serviceWorkerActive: true,
              cacheNames: cacheNames,
              timestamp: new Date().toISOString()
            });
          });
        })().catch(error => {
          console.error('[SW] Error in diagnostic test:', error);
        });

      } catch (error) {
        console.error('[SW] Error handling diagnostic test:', error);
      }
    }

    // Handle unknown message types
    if (!['SKIP_WAITING', 'GET_CACHE_STATUS', 'PWA_MODE_STATUS', 'LOCATION_CHANGE',
      'CLIENT_READY', 'PWA_MODE_INFO', 'TRIGGER_SYNC', 'REQUEST_PWA_MODE',
      'UPDATE_CACHE_STRATEGY', 'DIAGNOSTIC_TEST'].includes(messageType)) {
      console.warn('[SW] Unknown message type received:', messageType, 'from:', messageSource);
    }

  } catch (error) {
    console.error('[SW] Critical error in message handler:', error);
    console.error('[SW] Message that caused error:', event.data);
    console.error('[SW] Error context:', {
      timestamp: new Date().toISOString(),
      eventSource: !!event.source,
      eventPorts: event.ports?.length || 0
    });
  }
});



if (SW_CONFIG.PRODUCTION_LOGGING) {
  console.log('[SW] Service worker script loaded');
}
