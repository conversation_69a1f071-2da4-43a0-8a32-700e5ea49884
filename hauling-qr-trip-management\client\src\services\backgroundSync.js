// Background Sync Service
// Handles offline data synchronization for Driver Connect PWA mode only
// Trip Scanner operates online-only for data integrity

import { driverConnectOffline } from './driverConnectOffline.js';
import { offlineShiftState } from './offlineShiftState.js';
import { conflictResolution, referenceData, SYNC_STATUS } from './offlineDB.js';
import { getDriverStatus, getTruckStatus } from './driverAPI.js';
import toast from 'react-hot-toast';

// Background sync coordination service for Driver Connect offline functionality
export class BackgroundSyncService {
  constructor() {
    this.isOnline = navigator.onLine;
    this.syncInProgress = false;
    this.syncQueue = [];
    this.retryDelays = [1000, 5000, 15000, 30000]; // Progressive retry delays
    this.maxConcurrentSyncs = 3;
    
    // Bind network event handlers
    this.handleOnline = this.handleOnline.bind(this);
    this.handleOffline = this.handleOffline.bind(this);
    
    // Initialize network monitoring
    this.initializeNetworkMonitoring();
  }

  // Initialize network event monitoring
  initializeNetworkMonitoring() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    console.log('[BackgroundSync] Network monitoring initialized');
  }

  // Handle network connection restored
  async handleOnline() {
    this.isOnline = true;
    console.log('[BackgroundSync] Network connection restored - automatic sync disabled');

    // AUTOMATIC SYNC DISABLED - Only manual sync via sync button is allowed
    // Do not trigger immediate sync or register background sync
    console.log('[BackgroundSync] Use manual sync button to transfer offline data');
  }

  // Handle network connection lost
  handleOffline() {
    this.isOnline = false;
    console.log('[BackgroundSync] Network connection lost, entering offline mode');
  }

  // Start comprehensive sync process
  async startSync() {
    if (this.syncInProgress || !this.isOnline) {
      console.log('[BackgroundSync] Sync already in progress or offline');
      return;
    }

    this.syncInProgress = true;
    console.log('[BackgroundSync] Starting comprehensive sync...');

    try {
      // Sync only driver connections (TripScanner operates online-only)
      const syncResults = {
        driverConnections: await this.syncDriverConnections(),
        shiftStates: await this.syncShiftStates(),
        conflicts: await this.resolveConflicts(),
        referenceData: await this.updateReferenceData()
      };

      console.log('[BackgroundSync] Sync completed:', syncResults);
      
      // Notify components of sync completion
      this.notifySyncComplete(syncResults);
      
      return syncResults;
      
    } catch (error) {
      console.error('[BackgroundSync] Sync failed:', error);
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Public method for manual sync trigger (alias for startSync)
  async syncAll() {
    return await this.startSync();
  }

  // Sync driver connections with enhanced status validation and progress notifications
  async syncDriverConnections() {
    console.log('[BackgroundSync] Syncing driver connections...');

    try {
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      console.log(`[BackgroundSync] Found ${pendingConnections.length} pending driver connections`);

      if (pendingConnections.length === 0) {
        return {
          total: 0,
          synced: 0,
          failed: 0,
          conflicts: 0,
          statusBlocked: 0,
          message: 'No connections to sync'
        };
      }

      // Show initial progress notification
      toast.loading(`🔄 Validating and syncing ${pendingConnections.length} connection${pendingConnections.length !== 1 ? 's' : ''}...`, {
        id: 'sync-progress',
        duration: Infinity
      });

      // Debug: Log details of pending connections
      console.log('[BackgroundSync] Pending connections details:',
        pendingConnections.map(conn => ({
          id: conn.id,
          status: conn.status,
          action: conn.action,
          hasApiPayload: !!conn.apiPayload,
          employeeId: conn.employeeId,
          truckId: conn.truckId,
          timestamp: conn.timestamp
        }))
      );

      const results = {
        total: pendingConnections.length,
        synced: 0,
        failed: 0,
        conflicts: 0,
        statusBlocked: 0,
        validationErrors: [],
        syncErrors: []
      };

      // Process connections sequentially to avoid conflicts
      for (let i = 0; i < pendingConnections.length; i++) {
        const connection = pendingConnections[i];
        const progress = i + 1;

        // Update progress notification
        toast.loading(`🔄 Processing connection ${progress}/${pendingConnections.length}...`, {
          id: 'sync-progress'
        });

        try {
          const result = await this.syncSingleDriverConnection(connection, progress, pendingConnections.length);

          if (result.success) {
            results.synced++;
          } else if (result.conflict) {
            results.conflicts++;
          } else if (result.statusBlocked) {
            results.statusBlocked++;
            results.validationErrors.push({
              connectionId: connection.id,
              error: result.error,
              message: result.message,
              entityType: result.entityType || 'driver',
              status: result.status,
              statusDisplayName: result.statusDisplayName
            });
          } else {
            results.failed++;
            results.syncErrors.push({
              connectionId: connection.id,
              error: result.error,
              message: result.message
            });
          }
        } catch (error) {
          results.failed++;
          results.syncErrors.push({
            connectionId: connection.id,
            error: 'SYNC_EXCEPTION',
            message: error.message
          });
          console.error('[BackgroundSync] Driver connection sync failed:', error);
        }
      }

      // Dismiss progress notification and show completion summary
      toast.dismiss('sync-progress');

      // Show comprehensive completion notification
      this.showSyncCompletionNotification(results);

      console.log('[BackgroundSync] Driver connection sync results:', results);
      return results;

    } catch (error) {
      console.error('[BackgroundSync] Driver connection sync error:', error);
      toast.dismiss('sync-progress');
      toast.error(`❌ Sync failed: ${error.message}`, { duration: 6000 });
      return {
        total: 0,
        synced: 0,
        failed: 0,
        conflicts: 0,
        statusBlocked: 0,
        error: error.message
      };
    }
  }

  // Sync single driver connection with enhanced status validation
  async syncSingleDriverConnection(connection, progress = 1, total = 1) {
    console.log(`[BackgroundSync] Syncing connection ${connection.id} (${progress}/${total})...`);

    try {
      // Update connection status to syncing
      await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.SYNCING);

      // Validate and sanitize connection data before sync
      const validationResult = this.validateConnectionData(connection);
      if (!validationResult.isValid) {
        console.error(`[BackgroundSync] Invalid connection data for ${connection.id}:`, validationResult.errors);
        throw new Error(`Invalid connection data: ${validationResult.errors.join(', ')}`);
      }

      // Attempt to sync with server using exact API format
      let apiPayload = connection.apiPayload || connection.connectionData;

      if (!apiPayload) {
        throw new Error('No API payload found in connection');
      }

      // ENHANCED: Intelligent auto-detection during sync
      // Check driver's current shift status in production database to determine correct action
      const correctedPayload = await this.intelligentActionDetection(apiPayload);

      // Debug: Log API payload details
      console.log(`[BackgroundSync] API payload for connection ${connection.id}:`, {
        hasPayload: !!correctedPayload,
        originalAction: apiPayload?.action,
        correctedAction: correctedPayload?.action,
        actionChanged: apiPayload?.action !== correctedPayload?.action,
        employeeId: correctedPayload?.driver_qr_data?.employee_id,
        truckId: correctedPayload?.truck_qr_data?.id,
        isValid: validationResult.isValid
      });

      // Use corrected payload for sync
      apiPayload = correctedPayload;

      // ENHANCED: Comprehensive Status Validation during sync (Driver + Truck)
      console.log(`[BackgroundSync] Starting status validation for connection ${connection.id}...`);
      const statusValidation = await this.validateDriverAndTruckStatusForSync(apiPayload);
      console.log(`[BackgroundSync] Status validation result for connection ${connection.id}:`, {
        valid: statusValidation.valid,
        error: statusValidation.error,
        entityType: statusValidation.entityType,
        status: statusValidation.status
      });

      if (!statusValidation.valid) {
        console.warn(`[BackgroundSync] 🚫 Status validation BLOCKED connection ${connection.id} - sync will NOT proceed to server`);
        console.warn(`[BackgroundSync] Blocking details:`, statusValidation);

        // Mark connection as failed with status reason
        await driverConnectOffline.updateConnectionStatus(
          connection.id,
          SYNC_STATUS.FAILED,
          {
            error: statusValidation.error,
            message: statusValidation.message,
            entityType: statusValidation.entityType,
            status: statusValidation.status,
            statusDisplayName: statusValidation.statusDisplayName,
            blockedAt: new Date().toISOString(),
            reason: `${statusValidation.entityType}_status_blocked`
          }
        );

        console.log(`[BackgroundSync] ✅ Connection ${connection.id} marked as FAILED and will NOT be sent to server`);
        return {
          success: false,
          statusBlocked: true,
          error: statusValidation.error,
          message: statusValidation.message,
          entityType: statusValidation.entityType,
          status: statusValidation.status,
          statusDisplayName: statusValidation.statusDisplayName
        };
      }

      console.log(`[BackgroundSync] ✅ Status validation PASSED for connection ${connection.id} - proceeding to server sync`);
      console.log(`[BackgroundSync] Making API call to /api/driver/connect for connection ${connection.id}...`);

      const response = await fetch('/api/driver/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiPayload)
      });

      console.log(`[BackgroundSync] Server response for connection ${connection.id}: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log(`[BackgroundSync] Server response data for connection ${connection.id}:`, {
          success: result.success,
          action: result.action,
          message: result.message,
          hasShiftId: !!result.shift_id,
          hasDriver: !!result.driver
        });
        
        // Check for conflicts (e.g., driver already checked in)
        if (result.conflict) {
          console.log(`[BackgroundSync] Conflict detected for connection ${connection.id}`);
          const conflict = await conflictResolution.detectConflict(
            connection.connectionData,
            result,
            'duplicate'
          );
          return { success: false, conflict: true, conflictId: conflict.id };
        }
        
        // Check if server response indicates success
        if (result.success === false) {
          console.log(`[BackgroundSync] Server returned success=false for connection ${connection.id}:`, result);
          throw new Error(result.message || 'Server returned success=false');
        }

        // ENHANCED: Update offline shift state based on synced connection
        await offlineShiftState.handleSyncedConnection(connection, result);

        await driverConnectOffline.removeSyncedConnection(connection.id);
        console.log(`[BackgroundSync] Driver connection ${connection.id} synced successfully`);
        return { success: true, connectionId: connection.id };
        
      } else {
        let errorDetails;
        try {
          errorDetails = await response.json();
          console.error(`[BackgroundSync] Server error for connection ${connection.id}: ${response.status}`, errorDetails);
          throw new Error(`Server error: ${response.status} - ${errorDetails.message || errorDetails.error || 'Unknown error'}`);
        } catch (jsonError) {
          const errorText = await response.text();
          console.error(`[BackgroundSync] Server error for connection ${connection.id}: ${response.status} - ${errorText}`);
          throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
      }
      
    } catch (error) {
      console.error(`[BackgroundSync] Driver connection ${connection.id} sync failed:`, error);
      
      await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.FAILED, {
        lastError: error.message,
        failedAt: new Date().toISOString()
      });
      
      return { success: false, error: error.message };
    }
  }

  // Resolve pending conflicts
  async resolveConflicts() {
    console.log('[BackgroundSync] Resolving conflicts...');
    
    try {
      const pendingConflicts = await conflictResolution.getPendingConflicts();
      console.log(`[BackgroundSync] Found ${pendingConflicts.length} pending conflicts`);
      
      const results = {
        total: pendingConflicts.length,
        autoResolved: 0,
        manualRequired: 0
      };

      for (const conflict of pendingConflicts) {
        const resolution = await conflictResolution.autoResolveConflict(conflict.id);
        
        if (resolution) {
          results.autoResolved++;
          console.log(`[BackgroundSync] Auto-resolved conflict ${conflict.id}`);
        } else {
          results.manualRequired++;
          console.log(`[BackgroundSync] Conflict ${conflict.id} requires manual resolution`);
        }
      }

      console.log('[BackgroundSync] Conflict resolution results:', results);
      return results;
      
    } catch (error) {
      console.error('[BackgroundSync] Conflict resolution error:', error);
      return { total: 0, autoResolved: 0, manualRequired: 0, error: error.message };
    }
  }

  // Sync offline shift states with server data
  async syncShiftStates() {
    console.log('[BackgroundSync] Syncing shift states...');

    try {
      // Fetch current active shifts from server
      const response = await fetch('/api/driver/active-shifts', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const serverShifts = await response.json();
        const syncResults = await offlineShiftState.syncWithServerShifts(serverShifts);

        console.log('[BackgroundSync] Shift states synced:', syncResults);
        return {
          success: true,
          ...syncResults
        };
      } else {
        console.warn('[BackgroundSync] Failed to fetch active shifts from server:', response.status);
        return {
          success: false,
          error: `Server error: ${response.status}`
        };
      }
    } catch (error) {
      console.error('[BackgroundSync] Shift state sync error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Update reference data cache
  async updateReferenceData() {
    console.log('[BackgroundSync] Checking if reference data update is needed...');

    try {
      // Enhanced Driver Connect PWA detection with multiple methods
      const detectDriverConnectPWA = () => {
        // Path-based detection
        const pathCheck = window.location.pathname.includes('/driver-connect') ||
                          window.location.pathname === '/driver-connect';

        // PWA mode detection
        const pwaCheck = window.matchMedia('(display-mode: standalone)').matches ||
                         window.navigator.standalone === true ||
                         document.referrer.includes('android-app://');

        // Service worker scope detection (if available)
        const scopeCheck = navigator.serviceWorker?.controller?.scriptURL?.includes('driver-connect');

        // Must be both Driver Connect path AND PWA mode for enhanced reliability
        return pathCheck && (pwaCheck || scopeCheck);
      };

      const isDriverConnectPWA = detectDriverConnectPWA();

      if (isDriverConnectPWA) {
        console.log('[BackgroundSync] Skipping reference data update for Driver Connect PWA (no authentication required)');
        console.log('[BackgroundSync] PWA Detection Details:', {
          pathCheck: window.location.pathname,
          pwaMode: window.matchMedia('(display-mode: standalone)').matches,
          iOSStandalone: window.navigator.standalone,
          serviceWorkerScope: navigator.serviceWorker?.controller?.scriptURL,
          finalDecision: isDriverConnectPWA
        });

        return {
          locations: { success: true, skipped: true, reason: 'Driver Connect PWA - no auth required' },
          trucks: { success: true, skipped: true, reason: 'Driver Connect PWA - no auth required' },
          drivers: { success: true, skipped: true, reason: 'Driver Connect PWA - no auth required' },
          summary: {
            successful: 3, // Count as successful since skipping is the correct behavior
            skipped: 3,
            total: 3
          }
        };
      }

      const updates = {
        locations: await this.fetchAndCacheReferenceData('locations', '/api/locations'),
        trucks: await this.fetchAndCacheReferenceData('trucks', '/api/trucks'),
        drivers: await this.fetchAndCacheReferenceData('drivers', '/api/drivers')
      };

      // Count successful updates
      const successCount = Object.values(updates).filter(update => update.success).length;
      const skippedCount = Object.values(updates).filter(update => update.skipped).length;

      console.log('[BackgroundSync] Reference data update completed:', {
        successful: successCount,
        skipped: skippedCount,
        total: Object.keys(updates).length,
        details: updates
      });

      return {
        ...updates,
        summary: {
          successful: successCount,
          skipped: skippedCount,
          total: Object.keys(updates).length
        }
      };

    } catch (error) {
      console.error('[BackgroundSync] Reference data update error:', error);
      return {
        error: error.message,
        summary: { successful: 0, skipped: 0, total: 3 }
      };
    }
  }

  // Fetch and cache reference data
  async fetchAndCacheReferenceData(type, endpoint) {
    try {
      // Get auth token if available
      const authToken = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json'
      };
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }
      
      const response = await fetch(endpoint, { headers });
      
      if (response.ok) {
        const data = await response.json();
        await referenceData.cacheReferenceData(type, data);
        return { success: true, count: data.length || 0 };
      } else if (response.status === 401) {
        // Authentication required but not available - this is not critical for driver connect sync
        console.warn(`[BackgroundSync] Authentication required for ${type}, skipping reference data update`);
        return { success: false, error: 'Authentication required', skipped: true };
      } else {
        throw new Error(`Failed to fetch ${type}: ${response.status}`);
      }
    } catch (error) {
      console.error(`[BackgroundSync] Failed to update ${type}:`, error);
      return { success: false, error: error.message };
    }
  }

  // ENHANCED: Intelligent action detection during sync
  // Checks driver's current shift status in production database to determine correct action
  async intelligentActionDetection(apiPayload) {
    try {
      const employeeId = apiPayload.driver_qr_data?.employee_id;
      const truckId = apiPayload.truck_qr_data?.id;

      if (!employeeId || !truckId) {
        console.warn('[BackgroundSync] Missing employee ID or truck ID for intelligent detection');
        return apiPayload; // Return original if data is incomplete
      }

      // Fetch driver's current shift status from production database
      const response = await fetch(`/api/driver/status/${encodeURIComponent(employeeId)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn('[BackgroundSync] Failed to fetch driver status for intelligent detection');
        return apiPayload; // Return original if server request fails
      }

      const driverStatus = await response.json();
      console.log('[BackgroundSync] Driver status for intelligent detection:', driverStatus);

      // Determine correct action based on current shift status
      let correctedAction = apiPayload.action; // Default to original action

      if (driverStatus.status === 'checked_out') {
        // Driver has no active shift → should be check_in
        correctedAction = 'check_in';
      } else if (driverStatus.status === 'checked_in' && driverStatus.current_truck) {
        // Driver has active shift → check truck match
        if (driverStatus.current_truck.id?.toString() === truckId ||
            driverStatus.current_truck.truck_number === truckId) {
          // Same truck → should be check_out
          correctedAction = 'check_out';
        } else {
          // Different truck → should be check_in (handover)
          correctedAction = 'check_in';
        }
      }

      // Log action correction if it changed
      if (correctedAction !== apiPayload.action) {
        console.log(`[BackgroundSync] Action corrected: ${apiPayload.action} → ${correctedAction}`, {
          employeeId,
          truckId,
          driverStatus: driverStatus.status,
          currentTruck: driverStatus.current_truck?.truck_number,
          reason: 'intelligent_auto_detection'
        });
      }

      // Return corrected payload
      return {
        ...apiPayload,
        action: correctedAction
      };

    } catch (error) {
      console.error('[BackgroundSync] Error in intelligent action detection:', error);
      return apiPayload; // Return original payload if detection fails
    }
  }

  // Validate sync response data integrity
  validateSyncResponse(response, _originalData) {
    // Basic validation - can be enhanced based on business rules
    return response && response.success !== false;
  }

  // Notify components of sync completion
  notifySyncComplete(results) {
    // Dispatch custom event for components to listen to
    const event = new CustomEvent('backgroundSyncComplete', {
      detail: results
    });
    window.dispatchEvent(event);
  }

  // Get sync statistics
  async getSyncStats() {
    // Only get driver connection stats - TripScanner operates online-only
    const connectionStats = await driverConnectOffline.getStats();

    return {
      driverConnections: connectionStats,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      lastSync: localStorage.getItem('lastBackgroundSync') || null
    };
  }

  // Validate connection data before sync
  validateConnectionData(connection) {
    const errors = [];
    
    // Check basic connection structure
    if (!connection || typeof connection !== 'object') {
      errors.push('Connection is not a valid object');
      return { isValid: false, errors };
    }
    
    if (!connection.id) {
      errors.push('Connection missing ID');
    }
    
    // Check for API payload or connection data
    const payload = connection.apiPayload || connection.connectionData;
    if (!payload) {
      errors.push('Connection missing API payload and connection data');
      return { isValid: false, errors };
    }
    
    // Validate payload structure
    if (!payload.action) {
      errors.push('Payload missing action field');
    }
    
    if (payload.action === 'check_in' || payload.action === 'check_out') {
      if (!payload.driver_qr_data || !payload.driver_qr_data.employee_id) {
        errors.push('Payload missing valid driver QR data');
      }
      
      if (!payload.truck_qr_data || !payload.truck_qr_data.id) {
        errors.push('Payload missing valid truck QR data');
      }
    }
    
    // Check for required timestamps
    if (!payload.timestamp && !connection.timestamp) {
      errors.push('Connection missing timestamp');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Enhanced error recovery for failed connections
  async recoverFailedConnections() {
    console.log('[BackgroundSync] Starting failed connection recovery...');
    
    try {
      const failedConnections = await driverConnectOffline.getFailedConnections();
      console.log(`[BackgroundSync] Found ${failedConnections.length} failed connections to recover`);
      
      const recoveryResults = {
        total: failedConnections.length,
        recovered: 0,
        permanentlyFailed: 0
      };
      
      for (const connection of failedConnections) {
        try {
          // Check if connection can be recovered
          const validationResult = this.validateConnectionData(connection);
          
          if (validationResult.isValid) {
            // Reset status and retry
            await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.PENDING);
            recoveryResults.recovered++;
            console.log(`[BackgroundSync] Recovered connection ${connection.id}`);
          } else {
            // Mark as permanently failed
            await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.FAILED, {
              permanentFailure: true,
              validationErrors: validationResult.errors,
              recoveryAttemptedAt: new Date().toISOString()
            });
            recoveryResults.permanentlyFailed++;
            console.log(`[BackgroundSync] Permanently failed connection ${connection.id}:`, validationResult.errors);
          }
        } catch (error) {
          console.error(`[BackgroundSync] Error recovering connection ${connection.id}:`, error);
          recoveryResults.permanentlyFailed++;
        }
      }
      
      console.log('[BackgroundSync] Recovery completed:', recoveryResults);
      return recoveryResults;
      
    } catch (error) {
      console.error('[BackgroundSync] Failed connection recovery error:', error);
      return { total: 0, recovered: 0, permanentlyFailed: 0, error: error.message };
    }
  }

  // Clear corrupted data that might cause sync errors
  async clearCorruptedData() {
    console.log('[BackgroundSync] Clearing corrupted data...');
    
    try {
      const clearResults = await driverConnectOffline.clearCorruptedConnections();
      console.log('[BackgroundSync] Corrupted data cleared:', clearResults);
      return clearResults;
    } catch (error) {
      console.error('[BackgroundSync] Error clearing corrupted data:', error);
      return { error: error.message };
    }
  }

  // Cleanup - remove event listeners
  destroy() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
  }

  /**
   * Validate driver status during sync operations (deferred validation)
   * @param {Object} apiPayload - The API payload containing driver QR data
   * @returns {Promise<Object>} Validation result
   */
  async validateDriverStatusForSync(apiPayload) {
    try {
      // Extract employee ID from driver QR data
      const driverQrData = apiPayload.driver_qr_data;
      if (!driverQrData || !driverQrData.employee_id) {
        return {
          valid: false,
          error: 'MISSING_DRIVER_DATA',
          message: 'Driver information missing from sync data.',
          status: null,
          statusDisplayName: null
        };
      }

      const employeeId = driverQrData.employee_id;
      console.log(`[BackgroundSync] Validating driver status for ${employeeId} during sync...`);

      // Call the driver status API to get current status
      const statusResponse = await getDriverStatus(employeeId);

      if (!statusResponse.data.success) {
        // Check if this is a status blocked error
        if (statusResponse.status === 403 && statusResponse.data.error === 'DRIVER_STATUS_BLOCKED') {
          return {
            valid: false,
            error: statusResponse.data.error,
            message: statusResponse.data.message,
            status: statusResponse.data.status,
            statusDisplayName: statusResponse.data.statusDisplayName,
            driver: statusResponse.data.driver
          };
        }

        // Other errors (driver not found, etc.)
        return {
          valid: false,
          error: statusResponse.data.error || 'DRIVER_STATUS_ERROR',
          message: statusResponse.data.message || 'Unable to validate driver status.',
          status: null,
          statusDisplayName: null
        };
      }

      // Driver status is valid (active)
      return {
        valid: true,
        error: null,
        message: 'Driver status validated successfully.',
        status: statusResponse.data.driver_status,
        statusDisplayName: statusResponse.data.status_display_name,
        driver: statusResponse.data.driver
      };

    } catch (error) {
      console.error('[BackgroundSync] Driver status validation error during sync:', error);

      // Handle network errors vs status validation errors
      if (error.response && error.response.status === 403) {
        const errorData = error.response.data;
        return {
          valid: false,
          error: errorData.error || 'DRIVER_STATUS_BLOCKED',
          message: errorData.message || 'Driver status prevents operations.',
          status: errorData.status,
          statusDisplayName: errorData.statusDisplayName,
          driver: errorData.driver
        };
      }

      // STRICT VALIDATION: Only allow graceful degradation for genuine network connectivity issues
      // Module import errors, API failures, and other errors should block sync for safety
      const isNetworkConnectivityError = error.code === 'ENOTFOUND' ||
                                        error.code === 'ECONNREFUSED' ||
                                        error.code === 'ETIMEDOUT' ||
                                        error.message.includes('fetch is not defined') ||
                                        (error.message.includes('Network Error') && !error.message.includes('Cannot find module'));

      if (isNetworkConnectivityError) {
        console.warn('[BackgroundSync] Network connectivity error during driver status validation, allowing sync to proceed:', error.message);
        return {
          valid: true,
          error: null,
          message: 'Driver status validation skipped due to network connectivity error.',
          status: 'unknown',
          statusDisplayName: 'Unknown (Network Error)'
        };
      } else {
        // For all other errors (module imports, API failures, etc.), block sync for safety
        console.error('[BackgroundSync] Driver status validation failed, blocking sync for safety:', error.message);
        return {
          valid: false,
          error: 'DRIVER_VALIDATION_ERROR',
          message: 'Unable to validate driver status. Sync blocked for safety.',
          status: 'unknown',
          statusDisplayName: 'Validation Failed'
        };
      }
    }
  }

  /**
   * Show comprehensive sync completion notification with detailed results
   * @param {Object} results - Sync results object
   */
  showSyncCompletionNotification(results) {
    const { total, synced, failed, conflicts, statusBlocked, validationErrors, syncErrors } = results;

    if (total === 0) {
      toast.success('✅ No connections to sync', { duration: 3000 });
      return;
    }

    // Determine overall success
    const allSuccessful = synced === total;

    if (allSuccessful) {
      // All connections synced successfully
      toast.success(`🎉 Successfully synced all ${synced} connection${synced !== 1 ? 's' : ''}!`, {
        duration: 5000
      });
    } else if (synced > 0) {
      // Partial success
      const successMessage = `✅ ${synced} synced successfully`;
      const errorParts = [];

      if (statusBlocked > 0) {
        errorParts.push(`${statusBlocked} blocked by status`);
      }
      if (failed > 0) {
        errorParts.push(`${failed} failed`);
      }
      if (conflicts > 0) {
        errorParts.push(`${conflicts} conflicts`);
      }

      const errorMessage = errorParts.length > 0 ? ` • ❌ ${errorParts.join(', ')}` : '';

      toast.success(`${successMessage}${errorMessage}`, {
        duration: 6000
      });
    } else {
      // No successful syncs
      const errorParts = [];

      if (statusBlocked > 0) {
        errorParts.push(`${statusBlocked} blocked by status validation`);
      }
      if (failed > 0) {
        errorParts.push(`${failed} sync failed`);
      }
      if (conflicts > 0) {
        errorParts.push(`${conflicts} conflicts detected`);
      }

      toast.error(`❌ Sync failed: ${errorParts.join(', ')}`, {
        duration: 8000
      });
    }

    // Show detailed error information for status blocked connections
    if (statusBlocked > 0 && validationErrors.length > 0) {
      setTimeout(() => {
        const entityCounts = validationErrors.reduce((acc, error) => {
          acc[error.entityType] = (acc[error.entityType] || 0) + 1;
          return acc;
        }, {});

        const entityMessages = Object.entries(entityCounts).map(([type, count]) =>
          `${count} ${type}${count !== 1 ? 's' : ''}`
        );

        toast.error(`🚫 Status validation blocked: ${entityMessages.join(', ')} inactive`, {
          duration: 10000
        });
      }, 1000);
    }

    // Log detailed results for debugging
    console.log('[BackgroundSync] Sync completion summary:', {
      total,
      synced,
      failed,
      conflicts,
      statusBlocked,
      validationErrorCount: validationErrors.length,
      syncErrorCount: syncErrors.length,
      validationErrors: validationErrors.map(e => ({
        entityType: e.entityType,
        error: e.error,
        status: e.status
      })),
      syncErrors: syncErrors.map(e => ({
        error: e.error,
        message: e.message
      }))
    });
  }

  /**
   * Enhanced validation for both driver and truck status during sync operations
   * @param {Object} apiPayload - The API payload containing driver and truck QR data
   * @returns {Promise<Object>} Combined validation result
   */
  async validateDriverAndTruckStatusForSync(apiPayload) {
    try {
      // Extract data from API payload
      const driverQrData = apiPayload.driver_qr_data;
      const truckQrData = apiPayload.truck_qr_data;

      if (!driverQrData || !driverQrData.employee_id) {
        return {
          valid: false,
          error: 'MISSING_DRIVER_DATA',
          message: 'Driver information missing from sync data.',
          entityType: 'driver',
          status: null,
          statusDisplayName: null
        };
      }

      if (!truckQrData || !truckQrData.id) {
        return {
          valid: false,
          error: 'MISSING_TRUCK_DATA',
          message: 'Truck information missing from sync data.',
          entityType: 'truck',
          status: null,
          statusDisplayName: null
        };
      }

      const employeeId = driverQrData.employee_id;
      const truckNumber = truckQrData.id;

      console.log(`[BackgroundSync] Validating driver ${employeeId} and truck ${truckNumber} status during sync...`);

      // Validate both driver and truck status in parallel
      console.log(`[BackgroundSync] Calling validation APIs for driver ${employeeId} and truck ${truckNumber}...`);
      const [driverValidation, truckValidation] = await Promise.all([
        this.validateDriverStatusForSync(apiPayload),
        this.validateTruckStatusForSync(apiPayload)
      ]);

      console.log(`[BackgroundSync] Validation results - Driver: ${driverValidation.valid ? 'VALID' : 'INVALID'}, Truck: ${truckValidation.valid ? 'VALID' : 'INVALID'}`);
      if (!driverValidation.valid) {
        console.log(`[BackgroundSync] Driver validation failed:`, driverValidation);
      }
      if (!truckValidation.valid) {
        console.log(`[BackgroundSync] Truck validation failed:`, truckValidation);
      }

      // Check if both validations passed
      if (driverValidation.valid && truckValidation.valid) {
        return {
          valid: true,
          error: null,
          message: 'Both driver and truck status validated successfully.',
          entityType: null,
          status: null,
          statusDisplayName: null,
          driver: driverValidation.driver,
          truck: truckValidation.truck
        };
      }

      // Priority: Driver errors first, then truck errors (matching online mode behavior)
      if (!driverValidation.valid) {
        return {
          valid: false,
          error: driverValidation.error,
          message: driverValidation.message,
          entityType: 'driver',
          status: driverValidation.status,
          statusDisplayName: driverValidation.statusDisplayName,
          driver: driverValidation.driver,
          truck: truckValidation.truck
        };
      } else if (!truckValidation.valid) {
        return {
          valid: false,
          error: truckValidation.error,
          message: truckValidation.message,
          entityType: 'truck',
          status: truckValidation.status,
          statusDisplayName: truckValidation.statusDisplayName,
          driver: driverValidation.driver,
          truck: truckValidation.truck
        };
      }

      // This shouldn't happen, but handle it gracefully
      return {
        valid: false,
        error: 'VALIDATION_ERROR',
        message: 'Unknown validation error occurred.',
        entityType: 'unknown',
        status: null,
        statusDisplayName: null
      };

    } catch (error) {
      console.error('[BackgroundSync] Combined status validation error during sync:', error);

      // Handle network errors gracefully - allow sync to proceed
      console.warn('[BackgroundSync] Network error during combined status validation, allowing sync to proceed:', error.message);
      return {
        valid: true,
        error: null,
        message: 'Status validation skipped due to network error.',
        entityType: null,
        status: 'unknown',
        statusDisplayName: 'Unknown (Network Error)'
      };
    }
  }

  /**
   * Validate truck status during sync operations (deferred validation)
   * @param {Object} apiPayload - The API payload containing truck QR data
   * @returns {Promise<Object>} Validation result
   */
  async validateTruckStatusForSync(apiPayload) {
    try {
      // Extract truck number from truck QR data
      const truckQrData = apiPayload.truck_qr_data;
      if (!truckQrData || !truckQrData.id) {
        return {
          valid: false,
          error: 'MISSING_TRUCK_DATA',
          message: 'Truck information missing from sync data.',
          status: null,
          statusDisplayName: null
        };
      }

      const truckNumber = truckQrData.id;
      console.log(`[BackgroundSync] Validating truck status for ${truckNumber} during sync...`);

      // Call the truck status API to get current status
      console.log(`[BackgroundSync] Calling getTruckStatus API for truck ${truckNumber}...`);
      const statusResponse = await getTruckStatus(truckNumber);
      console.log(`[BackgroundSync] getTruckStatus response for ${truckNumber}:`, {
        status: statusResponse.status,
        success: statusResponse.data?.success,
        truck_status: statusResponse.data?.truck_status,
        error: statusResponse.data?.error
      });

      if (!statusResponse.data.success) {
        // Check if this is a status blocked error
        if (statusResponse.status === 403 && statusResponse.data.error === 'TRUCK_STATUS_BLOCKED') {
          return {
            valid: false,
            error: statusResponse.data.error,
            message: statusResponse.data.message,
            status: statusResponse.data.truck_status,
            statusDisplayName: statusResponse.data.status_display_name,
            truck: statusResponse.data.truck
          };
        }

        // Other errors (truck not found, etc.)
        return {
          valid: false,
          error: statusResponse.data.error || 'TRUCK_STATUS_ERROR',
          message: statusResponse.data.message || 'Unable to validate truck status.',
          status: null,
          statusDisplayName: null
        };
      }

      // Truck status is valid (active)
      return {
        valid: true,
        error: null,
        message: 'Truck status validated successfully.',
        status: statusResponse.data.truck_status,
        statusDisplayName: statusResponse.data.status_display_name,
        truck: statusResponse.data.truck
      };

    } catch (error) {
      console.error('[BackgroundSync] Truck status validation error during sync:', error);

      // Handle network errors vs status validation errors
      if (error.response && error.response.status === 403) {
        const errorData = error.response.data;
        return {
          valid: false,
          error: errorData.error || 'TRUCK_STATUS_BLOCKED',
          message: errorData.message || 'Truck status prevents operations.',
          status: errorData.truck_status,
          statusDisplayName: errorData.status_display_name,
          truck: errorData.truck
        };
      }

      // STRICT VALIDATION: Only allow graceful degradation for genuine network connectivity issues
      // Module import errors, API failures, and other errors should block sync for safety
      const isNetworkConnectivityError = error.code === 'ENOTFOUND' ||
                                        error.code === 'ECONNREFUSED' ||
                                        error.code === 'ETIMEDOUT' ||
                                        error.message.includes('fetch is not defined') ||
                                        (error.message.includes('Network Error') && !error.message.includes('Cannot find module'));

      if (isNetworkConnectivityError) {
        console.warn('[BackgroundSync] Network connectivity error during truck status validation, allowing sync to proceed:', error.message);
        return {
          valid: true,
          error: null,
          message: 'Truck status validation skipped due to network connectivity error.',
          status: 'unknown',
          statusDisplayName: 'Unknown (Network Error)'
        };
      } else {
        // For all other errors (module imports, API failures, etc.), block sync for safety
        console.error('[BackgroundSync] Truck status validation failed, blocking sync for safety:', error.message);
        return {
          valid: false,
          error: 'TRUCK_VALIDATION_ERROR',
          message: 'Unable to validate truck status. Sync blocked for safety.',
          status: 'unknown',
          statusDisplayName: 'Validation Failed'
        };
      }
    }
  }
}

// Create singleton instance
export const backgroundSync = new BackgroundSyncService();

// Export service instance
export default backgroundSync;