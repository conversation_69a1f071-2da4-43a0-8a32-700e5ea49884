/**
 * Status Synchronization Service
 * 
 * Monitors and maintains status synchronization between:
 * - Shift Management
 * - Assignment Management
 * - Trip Monitoring
 * 
 * Provides automated conflict detection and resolution.
 */

const { getClient } = require('../config/database');
const DataFlowLogger = require('../utils/DataFlowLogger');
const { logInfo, logError } = require('../utils/logger');

class StatusSynchronizationService {
  /**
   * Monitor status synchronization across all systems
   * @returns {Promise<Object>} Monitoring results
   */
  static async monitorStatusSynchronization() {
    const monitoringStartTime = Date.now();
    const correlationId = DataFlowLogger.createCorrelationId('status_sync');
    
    const monitoringResults = {
      correlation_id: correlationId,
      overall_status: 'operational',
      sync_issues: [],
      auto_fixes_applied: 0,
      metrics: {},
      monitoring_duration_ms: 0,
      timestamp: new Date().toISOString()
    };

    const client = await getClient();

    try {
      await client.query('BEGIN');

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'MONITORING_START', 'Status synchronization monitoring started');

      // Step 1: Check shift-assignment synchronization
      const shiftAssignmentSync = await this.checkShiftAssignmentSync(client, correlationId);
      monitoringResults.sync_issues.push(...shiftAssignmentSync.issues);
      monitoringResults.auto_fixes_applied += shiftAssignmentSync.fixes_applied;

      // Step 2: Check assignment-trip synchronization
      const assignmentTripSync = await this.checkAssignmentTripSync(client, correlationId);
      monitoringResults.sync_issues.push(...assignmentTripSync.issues);
      monitoringResults.auto_fixes_applied += assignmentTripSync.fixes_applied;

      // Step 3: Check for status conflicts
      const statusConflicts = await this.detectStatusConflicts(client, correlationId);
      monitoringResults.sync_issues.push(...statusConflicts.issues);
      monitoringResults.auto_fixes_applied += statusConflicts.fixes_applied;

      // Step 4: Validate QR-created shift protection
      const qrShiftProtection = await this.validateQRShiftProtection(client, correlationId);
      monitoringResults.sync_issues.push(...qrShiftProtection.issues);

      // Aggregate metrics
      monitoringResults.metrics = {
        shifts_checked: shiftAssignmentSync.shifts_checked,
        assignments_checked: assignmentTripSync.assignments_checked,
        trips_checked: assignmentTripSync.trips_checked,
        total_issues_found: monitoringResults.sync_issues.length,
        critical_issues: monitoringResults.sync_issues.filter(i => i.severity === 'critical').length,
        warning_issues: monitoringResults.sync_issues.filter(i => i.severity === 'warning').length,
        qr_shifts_protected: qrShiftProtection.qr_shifts_checked
      };

      // Determine overall status
      const criticalIssues = monitoringResults.sync_issues.filter(i => i.severity === 'critical');
      const warningIssues = monitoringResults.sync_issues.filter(i => i.severity === 'warning');

      if (criticalIssues.length > 0) {
        monitoringResults.overall_status = 'critical';
      } else if (warningIssues.length > 0) {
        monitoringResults.overall_status = 'warning';
      }

      await client.query('COMMIT');

      monitoringResults.monitoring_duration_ms = Date.now() - monitoringStartTime;

      DataFlowLogger.logStatusSyncMonitoring(monitoringResults);

      return monitoringResults;

    } catch (error) {
      await client.query('ROLLBACK');
      
      logError('STATUS_SYNC_MONITORING_ERROR', error, {
        correlation_id: correlationId,
        monitoring_duration_ms: Date.now() - monitoringStartTime
      });

      monitoringResults.overall_status = 'critical';
      monitoringResults.sync_issues.push({
        type: 'monitoring_system_error',
        severity: 'critical',
        description: `Status synchronization monitoring error: ${error.message}`,
        system: 'monitoring_framework',
        auto_fixed: false
      });
      monitoringResults.monitoring_duration_ms = Date.now() - monitoringStartTime;

      return monitoringResults;
    } finally {
      client.release();
    }
  }

  /**
   * Check synchronization between shift management and assignment management
   * @param {Object} client Database client
   * @param {string} correlationId Correlation ID
   * @returns {Promise<Object>} Sync check results
   */
  static async checkShiftAssignmentSync(client, correlationId) {
    const result = {
      issues: [],
      fixes_applied: 0,
      shifts_checked: 0
    };

    try {
      // Find active shifts that should have synchronized assignments
      const shiftAssignmentQuery = `
        WITH active_shifts AS (
          SELECT 
            ds.id as shift_id,
            ds.truck_id,
            ds.driver_id as shift_driver_id,
            ds.shift_type,
            ds.status as shift_status,
            ds.auto_created,
            dt.truck_number,
            d.full_name as shift_driver_name
          FROM driver_shifts ds
          JOIN dump_trucks dt ON ds.truck_id = dt.id
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.status = 'active'
            AND ds.start_date <= CURRENT_DATE
            AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
        ),
        sync_check AS (
          SELECT 
            ash.shift_id,
            ash.truck_id,
            ash.shift_driver_id,
            ash.truck_number,
            ash.shift_driver_name,
            ash.auto_created,
            a.id as assignment_id,
            a.driver_id as assignment_driver_id,
            a.status as assignment_status,
            ad.full_name as assignment_driver_name,
            CASE 
              WHEN a.id IS NULL THEN 'no_assignment'
              WHEN a.driver_id IS NULL THEN 'assignment_no_driver'
              WHEN a.driver_id = ash.shift_driver_id THEN 'synchronized'
              ELSE 'driver_mismatch'
            END as sync_status
          FROM active_shifts ash
          LEFT JOIN assignments a ON ash.truck_id = a.truck_id 
            AND a.status IN ('assigned', 'in_progress')
          LEFT JOIN drivers ad ON a.driver_id = ad.id
        )
        SELECT * FROM sync_check WHERE sync_status != 'synchronized'
      `;

      const syncResult = await client.query(shiftAssignmentQuery);
      result.shifts_checked = syncResult.rows.length;

      for (const row of syncResult.rows) {
        if (row.sync_status === 'driver_mismatch' && row.assignment_id) {
          // Auto-fix: Update assignment to match active shift
          try {
            await client.query(`
              UPDATE assignments 
              SET driver_id = $1, updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
            `, [row.shift_driver_id, row.assignment_id]);

            result.fixes_applied++;

            DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'AUTO_FIX_APPLIED', 'Assignment driver synchronized with active shift', {
              assignment_id: row.assignment_id,
              truck_number: row.truck_number,
              previous_driver: row.assignment_driver_name,
              new_driver: row.shift_driver_name,
              fix_type: 'driver_sync'
            });

          } catch (fixError) {
            result.issues.push({
              type: 'sync_fix_failure',
              severity: 'warning',
              description: `Failed to auto-fix assignment driver sync for ${row.truck_number}: ${fixError.message}`,
              system: 'assignment_management',
              auto_fixed: false,
              affected_records: [row.assignment_id]
            });
          }
        } else if (row.sync_status === 'assignment_no_driver' && row.assignment_id) {
          // Auto-fix: Set assignment driver from active shift
          try {
            await client.query(`
              UPDATE assignments 
              SET driver_id = $1, updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
            `, [row.shift_driver_id, row.assignment_id]);

            result.fixes_applied++;

            DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'AUTO_FIX_APPLIED', 'Assignment driver populated from active shift', {
              assignment_id: row.assignment_id,
              truck_number: row.truck_number,
              new_driver: row.shift_driver_name,
              fix_type: 'driver_population'
            });

          } catch (fixError) {
            result.issues.push({
              type: 'sync_fix_failure',
              severity: 'warning',
              description: `Failed to populate assignment driver for ${row.truck_number}: ${fixError.message}`,
              system: 'assignment_management',
              auto_fixed: false,
              affected_records: [row.assignment_id]
            });
          }
        } else if (row.sync_status === 'no_assignment') {
          result.issues.push({
            type: 'missing_assignment',
            severity: 'info',
            description: `Active shift for ${row.truck_number} has no corresponding assignment`,
            system: 'assignment_management',
            auto_fixed: false,
            affected_records: [row.shift_id]
          });
        }
      }

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'SHIFT_ASSIGNMENT_CHECK', 'Shift-assignment synchronization check complete', {
        shifts_checked: result.shifts_checked,
        issues_found: result.issues.length,
        fixes_applied: result.fixes_applied
      });

    } catch (error) {
      result.issues.push({
        type: 'sync_check_error',
        severity: 'critical',
        description: `Error checking shift-assignment sync: ${error.message}`,
        system: 'monitoring_framework',
        auto_fixed: false
      });

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'SHIFT_ASSIGNMENT_ERROR', 'Shift-assignment sync check failed', { error: error.message });
    }

    return result;
  }

  /**
   * Check synchronization between assignment management and trip monitoring
   * @param {Object} client Database client
   * @param {string} correlationId Correlation ID
   * @returns {Promise<Object>} Sync check results
   */
  static async checkAssignmentTripSync(client, correlationId) {
    const result = {
      issues: [],
      fixes_applied: 0,
      assignments_checked: 0,
      trips_checked: 0
    };

    try {
      // Check recent trips for driver capture accuracy
      const tripSyncQuery = `
        WITH recent_trips AS (
          SELECT 
            tl.id as trip_id,
            tl.assignment_id,
            tl.performed_by_driver_id as trip_driver_id,
            tl.performed_by_driver_name as trip_driver_name,
            tl.performed_by_shift_id as trip_shift_id,
            tl.created_at,
            a.truck_id,
            a.driver_id as assignment_driver_id,
            dt.truck_number,
            ad.full_name as assignment_driver_name,
            -- Check what the active shift was at the time of trip creation
            ds.id as expected_shift_id,
            ds.driver_id as expected_driver_id,
            d.full_name as expected_driver_name
          FROM trip_logs tl
          JOIN assignments a ON tl.assignment_id = a.id
          JOIN dump_trucks dt ON a.truck_id = dt.id
          LEFT JOIN drivers ad ON a.driver_id = ad.id
          LEFT JOIN driver_shifts ds ON (
            ds.truck_id = a.truck_id
            AND ds.status = 'active'
            AND tl.created_at::date BETWEEN ds.start_date AND COALESCE(ds.end_date, ds.start_date)
          )
          LEFT JOIN drivers d ON ds.driver_id = d.id
          WHERE tl.created_at >= CURRENT_DATE - INTERVAL '24 hours'
        ),
        sync_analysis AS (
          SELECT 
            trip_id,
            truck_number,
            assignment_driver_name,
            trip_driver_name,
            expected_driver_name,
            created_at,
            CASE 
              WHEN trip_driver_id IS NULL THEN 'no_driver_captured'
              WHEN expected_driver_id IS NULL THEN 'no_active_shift'
              WHEN trip_driver_id = expected_driver_id THEN 'accurate_capture'
              WHEN trip_driver_id = assignment_driver_id THEN 'assignment_match'
              ELSE 'driver_mismatch'
            END as sync_status
          FROM recent_trips
        )
        SELECT * FROM sync_analysis WHERE sync_status IN ('no_driver_captured', 'driver_mismatch')
      `;

      const tripSyncResult = await client.query(tripSyncQuery);
      result.trips_checked = tripSyncResult.rows.length;

      for (const row of tripSyncResult.rows) {
        if (row.sync_status === 'no_driver_captured') {
          result.issues.push({
            type: 'trip_driver_capture_failure',
            severity: 'critical',
            description: `Trip ${row.trip_id} for ${row.truck_number} has no driver information captured`,
            system: 'trip_monitoring',
            auto_fixed: false,
            affected_records: [row.trip_id]
          });
        } else if (row.sync_status === 'driver_mismatch') {
          result.issues.push({
            type: 'trip_driver_mismatch',
            severity: 'warning',
            description: `Trip ${row.trip_id} for ${row.truck_number} captured wrong driver: expected ${row.expected_driver_name}, got ${row.trip_driver_name}`,
            system: 'trip_monitoring',
            auto_fixed: false,
            affected_records: [row.trip_id]
          });
        }
      }

      // Check assignment display status for trip monitoring
      const displaySyncQuery = `
        WITH assignment_display AS (
          SELECT 
            a.id as assignment_id,
            dt.truck_number,
            a.status as assignment_status,
            CASE 
              WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
                CONCAT('✅ ', ds.shift_type, ' Shift Active')
              WHEN ds.id IS NOT NULL AND ds.status = 'scheduled' THEN
                CONCAT('📅 ', ds.shift_type, ' Shift Scheduled')
              ELSE '⚠️ No Active Shift'
            END as trip_monitoring_display
          FROM assignments a
          JOIN dump_trucks dt ON a.truck_id = dt.id
          LEFT JOIN driver_shifts ds ON (
            ds.truck_id = a.truck_id
            AND ds.status = 'active'
            AND ds.start_date <= CURRENT_DATE
            AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
          )
          WHERE a.status IN ('assigned', 'in_progress')
        )
        SELECT * FROM assignment_display 
        WHERE trip_monitoring_display = '⚠️ No Active Shift'
      `;

      const displaySyncResult = await client.query(displaySyncQuery);
      result.assignments_checked = displaySyncResult.rows.length;

      for (const row of displaySyncResult.rows) {
        result.issues.push({
          type: 'trip_monitoring_display_issue',
          severity: 'warning',
          description: `Assignment for ${row.truck_number} shows "No Active Shift" in Trip Monitoring despite being active`,
          system: 'trip_monitoring',
          auto_fixed: false,
          affected_records: [row.assignment_id]
        });
      }

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'ASSIGNMENT_TRIP_CHECK', 'Assignment-trip synchronization check complete', {
        assignments_checked: result.assignments_checked,
        trips_checked: result.trips_checked,
        issues_found: result.issues.length,
        fixes_applied: result.fixes_applied
      });

    } catch (error) {
      result.issues.push({
        type: 'sync_check_error',
        severity: 'critical',
        description: `Error checking assignment-trip sync: ${error.message}`,
        system: 'monitoring_framework',
        auto_fixed: false
      });

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'ASSIGNMENT_TRIP_ERROR', 'Assignment-trip sync check failed', { error: error.message });
    }

    return result;
  }

  /**
   * Detect status conflicts across all systems
   * @param {Object} client Database client
   * @param {string} correlationId Correlation ID
   * @returns {Promise<Object>} Conflict detection results
   */
  static async detectStatusConflicts(client, correlationId) {
    const result = {
      issues: [],
      fixes_applied: 0
    };

    try {
      // Detect complex status conflicts
      const conflictQuery = `
        WITH system_status AS (
          SELECT 
            dt.id as truck_id,
            dt.truck_number,
            -- Shift Management status
            ds.id as shift_id,
            ds.status as shift_status,
            ds.driver_id as shift_driver_id,
            ds.auto_created,
            sd.full_name as shift_driver_name,
            -- Assignment Management status
            a.id as assignment_id,
            a.status as assignment_status,
            a.driver_id as assignment_driver_id,
            ad.full_name as assignment_driver_name,
            -- Trip Monitoring status (active trips)
            COUNT(tl.id) FILTER (WHERE tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')) as active_trips
          FROM dump_trucks dt
          LEFT JOIN driver_shifts ds ON (
            ds.truck_id = dt.id 
            AND ds.status = 'active'
            AND ds.start_date <= CURRENT_DATE
            AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
          )
          LEFT JOIN drivers sd ON ds.driver_id = sd.id
          LEFT JOIN assignments a ON (
            a.truck_id = dt.id 
            AND a.status IN ('assigned', 'in_progress')
          )
          LEFT JOIN drivers ad ON a.driver_id = ad.id
          LEFT JOIN trip_logs tl ON (
            tl.assignment_id = a.id
            AND tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')
            AND tl.created_at >= CURRENT_DATE
          )
          WHERE dt.status = 'active'
          GROUP BY dt.id, dt.truck_number, ds.id, ds.status, ds.driver_id, ds.auto_created, sd.full_name,
                   a.id, a.status, a.driver_id, ad.full_name
        ),
        conflict_analysis AS (
          SELECT 
            truck_id,
            truck_number,
            shift_status,
            assignment_status,
            active_trips,
            shift_driver_name,
            assignment_driver_name,
            auto_created,
            CASE 
              WHEN shift_status = 'active' AND assignment_status IS NULL THEN 'active_shift_no_assignment'
              WHEN shift_status = 'active' AND assignment_status = 'assigned' AND active_trips > 0 THEN 'active_with_trips'
              WHEN shift_status IS NULL AND assignment_status = 'in_progress' THEN 'no_shift_active_assignment'
              WHEN shift_status = 'active' AND assignment_status = 'assigned' AND shift_driver_name != assignment_driver_name THEN 'driver_conflict'
              ELSE 'no_conflict'
            END as conflict_type
          FROM system_status
        )
        SELECT * FROM conflict_analysis WHERE conflict_type != 'no_conflict'
      `;

      const conflictResult = await client.query(conflictQuery);

      for (const row of conflictResult.rows) {
        let severity = 'warning';
        let autoFixed = false;

        if (row.conflict_type === 'driver_conflict') {
          severity = 'critical';
          
          // Try to auto-fix driver conflicts by prioritizing QR-created shifts
          if (row.auto_created) {
            try {
              await client.query(`
                UPDATE assignments 
                SET driver_id = (
                  SELECT driver_id FROM driver_shifts 
                  WHERE truck_id = $1 AND status = 'active' AND auto_created = true
                  ORDER BY created_at DESC LIMIT 1
                ), updated_at = CURRENT_TIMESTAMP
                WHERE truck_id = $1 AND status IN ('assigned', 'in_progress')
              `, [row.truck_id]);

              result.fixes_applied++;
              autoFixed = true;

              DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'CONFLICT_AUTO_FIXED', 'Driver conflict resolved by prioritizing QR-created shift', {
                truck_number: row.truck_number,
                conflict_type: row.conflict_type,
                fix_type: 'qr_shift_priority'
              });

            } catch (fixError) {
              // Fix failed, will be reported as unresolved conflict
            }
          }
        }

        if (!autoFixed) {
          result.issues.push({
            type: 'status_conflict',
            severity: severity,
            description: `Status conflict detected for ${row.truck_number}: ${row.conflict_type}`,
            system: 'cross_system',
            auto_fixed: false,
            affected_records: [row.truck_id],
            conflict_details: {
              conflict_type: row.conflict_type,
              shift_status: row.shift_status,
              assignment_status: row.assignment_status,
              active_trips: row.active_trips,
              shift_driver: row.shift_driver_name,
              assignment_driver: row.assignment_driver_name
            }
          });
        }
      }

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'CONFLICT_DETECTION', 'Status conflict detection complete', {
        conflicts_found: conflictResult.rows.length,
        fixes_applied: result.fixes_applied,
        unresolved_conflicts: result.issues.length
      });

    } catch (error) {
      result.issues.push({
        type: 'conflict_detection_error',
        severity: 'critical',
        description: `Error detecting status conflicts: ${error.message}`,
        system: 'monitoring_framework',
        auto_fixed: false
      });

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'CONFLICT_DETECTION_ERROR', 'Status conflict detection failed', { error: error.message });
    }

    return result;
  }

  /**
   * Validate QR-created shift protection
   * @param {Object} client Database client
   * @param {string} correlationId Correlation ID
   * @returns {Promise<Object>} Validation results
   */
  static async validateQRShiftProtection(client, correlationId) {
    const result = {
      issues: [],
      qr_shifts_checked: 0
    };

    try {
      // Check that QR-created active shifts are protected from automated status changes
      const qrShiftQuery = `
        SELECT 
          ds.id as shift_id,
          ds.truck_id,
          dt.truck_number,
          ds.driver_id,
          d.full_name as driver_name,
          ds.status,
          ds.start_date,
          ds.end_date,
          ds.start_time,
          ds.end_time,
          ds.auto_created,
          ds.created_at,
          ds.updated_at,
          -- Check if this shift should still be active based on time
          CASE 
            WHEN ds.end_date IS NULL AND ds.end_time IS NULL THEN 'should_be_active'
            WHEN ds.end_date IS NOT NULL AND ds.end_time IS NOT NULL THEN 'should_be_completed'
            ELSE 'unclear_status'
          END as expected_status
        FROM driver_shifts ds
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.auto_created = true
          AND ds.start_date IS NOT NULL
          AND ds.created_at >= CURRENT_DATE - INTERVAL '7 days'
      `;

      const qrShiftResult = await client.query(qrShiftQuery);
      result.qr_shifts_checked = qrShiftResult.rows.length;

      for (const row of qrShiftResult.rows) {
        // Check for improper status changes
        if (row.expected_status === 'should_be_active' && row.status !== 'active') {
          result.issues.push({
            type: 'qr_shift_protection_violation',
            severity: 'critical',
            description: `QR-created shift ${row.shift_id} for ${row.truck_number} was improperly changed from active to ${row.status}`,
            system: 'shift_management',
            auto_fixed: false,
            affected_records: [row.shift_id],
            protection_details: {
              shift_id: row.shift_id,
              truck_number: row.truck_number,
              driver_name: row.driver_name,
              current_status: row.status,
              expected_status: row.expected_status,
              auto_created: row.auto_created,
              end_date: row.end_date,
              end_time: row.end_time
            }
          });
        }

        // Check for suspicious rapid status changes
        const timeSinceCreation = Date.now() - new Date(row.created_at).getTime();
        const timeSinceUpdate = Date.now() - new Date(row.updated_at).getTime();
        
        if (timeSinceCreation < 60000 && timeSinceUpdate < 30000 && row.status === 'completed') {
          // Shift was created and completed within 1 minute - suspicious
          result.issues.push({
            type: 'suspicious_qr_shift_completion',
            severity: 'warning',
            description: `QR-created shift ${row.shift_id} for ${row.truck_number} was completed suspiciously quickly (${Math.round(timeSinceCreation/1000)}s after creation)`,
            system: 'shift_management',
            auto_fixed: false,
            affected_records: [row.shift_id]
          });
        }
      }

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'QR_SHIFT_PROTECTION', 'QR shift protection validation complete', {
        qr_shifts_checked: result.qr_shifts_checked,
        protection_violations: result.issues.filter(i => i.type === 'qr_shift_protection_violation').length,
        suspicious_completions: result.issues.filter(i => i.type === 'suspicious_qr_shift_completion').length
      });

    } catch (error) {
      result.issues.push({
        type: 'qr_protection_check_error',
        severity: 'critical',
        description: `Error validating QR shift protection: ${error.message}`,
        system: 'monitoring_framework',
        auto_fixed: false
      });

      DataFlowLogger.logWithCorrelation(correlationId, 'status_sync', 'QR_PROTECTION_ERROR', 'QR shift protection validation failed', { error: error.message });
    }

    return result;
  }

  /**
   * Create automated alerts for status synchronization issues
   * @param {Object} syncResults Synchronization monitoring results
   * @returns {Promise<Array>} Alert configurations
   */
  static async createSyncAlerts(syncResults) {
    const alerts = [];

    // Critical issues require immediate attention
    const criticalIssues = syncResults.sync_issues.filter(i => i.severity === 'critical');
    if (criticalIssues.length > 0) {
      alerts.push({
        type: 'critical_sync_issues',
        priority: 'high',
        title: `${criticalIssues.length} Critical Status Synchronization Issues`,
        description: 'Critical data flow issues detected that require immediate attention',
        affected_systems: [...new Set(criticalIssues.map(i => i.system))],
        recommended_action: 'Review and manually resolve critical synchronization issues',
        auto_executable: false
      });
    }

    // QR shift protection violations
    const protectionViolations = syncResults.sync_issues.filter(i => i.type === 'qr_shift_protection_violation');
    if (protectionViolations.length > 0) {
      alerts.push({
        type: 'qr_shift_protection_violation',
        priority: 'high',
        title: `${protectionViolations.length} QR Shift Protection Violations`,
        description: 'QR-created active shifts were improperly modified by automated functions',
        affected_systems: ['shift_management'],
        recommended_action: 'Review automated shift functions and restore QR shift protection',
        auto_executable: false
      });
    }

    // Driver capture failures
    const captureFailures = syncResults.sync_issues.filter(i => i.type === 'trip_driver_capture_failure');
    if (captureFailures.length > 0) {
      alerts.push({
        type: 'driver_capture_failures',
        priority: 'medium',
        title: `${captureFailures.length} Driver Capture Failures`,
        description: 'Recent trips have missing driver information',
        affected_systems: ['trip_monitoring'],
        recommended_action: 'Review captureActiveDriverInfo function and shift query logic',
        auto_executable: false
      });
    }

    // Auto-fixes applied
    if (syncResults.auto_fixes_applied > 0) {
      alerts.push({
        type: 'auto_fixes_applied',
        priority: 'info',
        title: `${syncResults.auto_fixes_applied} Synchronization Issues Auto-Fixed`,
        description: 'Status synchronization issues were automatically resolved',
        affected_systems: ['assignment_management'],
        recommended_action: 'Monitor for recurring issues that may indicate systemic problems',
        auto_executable: false
      });
    }

    return alerts;
  }

  /**
   * Generate status synchronization report
   * @param {Object} syncResults Synchronization monitoring results
   * @returns {Object} Formatted report
   */
  static generateSyncReport(syncResults) {
    const report = {
      summary: {
        overall_status: syncResults.overall_status,
        monitoring_timestamp: syncResults.timestamp,
        total_issues: syncResults.sync_issues.length,
        auto_fixes_applied: syncResults.auto_fixes_applied,
        monitoring_duration_ms: syncResults.monitoring_duration_ms
      },
      metrics: syncResults.metrics,
      issues_by_severity: {
        critical: syncResults.sync_issues.filter(i => i.severity === 'critical').length,
        warning: syncResults.sync_issues.filter(i => i.severity === 'warning').length,
        info: syncResults.sync_issues.filter(i => i.severity === 'info').length
      },
      issues_by_system: {},
      detailed_issues: syncResults.sync_issues,
      recommendations: []
    };

    // Group issues by system
    syncResults.sync_issues.forEach(issue => {
      if (!report.issues_by_system[issue.system]) {
        report.issues_by_system[issue.system] = 0;
      }
      report.issues_by_system[issue.system]++;
    });

    // Generate recommendations
    if (report.issues_by_severity.critical > 0) {
      report.recommendations.push('Immediate attention required for critical synchronization issues');
    }
    
    if (report.issues_by_system.trip_monitoring > 0) {
      report.recommendations.push('Review driver capture functionality in trip monitoring system');
    }
    
    if (report.auto_fixes_applied > 0) {
      report.recommendations.push('Monitor for recurring synchronization issues that may indicate systemic problems');
    }
    
    if (syncResults.sync_issues.some(i => i.type === 'qr_shift_protection_violation')) {
      report.recommendations.push('Review and strengthen QR-created shift protection mechanisms');
    }

    return report;
  }
}

module.exports = StatusSynchronizationService;