/**
 * Contact Information Settings API Routes
 * 
 * Provides endpoints for managing configurable contact information
 * Used by DriverStatusErrorModal and other components that need contact details
 */

const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const { logError } = require('../utils/logger');

/**
 * @route   GET /api/contact-settings
 * @desc    Get all contact information settings
 * @access  Private
 */
router.get('/', auth, async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        id,
        category,
        status_type,
        primary_contact,
        phone,
        email,
        description,
        created_at,
        updated_at
      FROM contact_settings 
      ORDER BY category, status_type
    `);

    // Group by category for easier frontend consumption
    const groupedSettings = {
      driver: {},
      truck: {}
    };

    result.rows.forEach(setting => {
      if (!groupedSettings[setting.category]) {
        groupedSettings[setting.category] = {};
      }
      groupedSettings[setting.category][setting.status_type] = {
        id: setting.id,
        primary: setting.primary_contact,
        phone: setting.phone,
        email: setting.email,
        description: setting.description,
        updated_at: setting.updated_at
      };
    });

    res.json({
      success: true,
      data: groupedSettings
    });
  } catch (error) {
    logError('CONTACT_SETTINGS_GET_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip
    });
    
    res.status(500).json({
      success: false,
      error: 'Database Error',
      message: 'Failed to retrieve contact settings'
    });
  }
});

/**
 * @route   POST /api/contact-settings
 * @desc    Create or update contact information setting
 * @access  Private (Admin only)
 */
router.post('/', auth, async (req, res) => {
  try {
    // Check if user has admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Access Denied',
        message: 'Admin privileges required to modify contact settings'
      });
    }

    const { category, status_type, primary_contact, phone, email, description } = req.body;

    // Validate required fields
    if (!category || !status_type || !primary_contact || !phone || !email) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Category, status_type, primary_contact, phone, and email are required'
      });
    }

    // Validate category
    if (!['driver', 'truck'].includes(category)) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Category must be either "driver" or "truck"'
      });
    }

    // Validate status_type based on category
    const validDriverStatuses = ['inactive', 'suspended', 'on_leave', 'terminated'];
    const validTruckStatuses = ['inactive', 'maintenance', 'retired'];
    
    if (category === 'driver' && !validDriverStatuses.includes(status_type)) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: `Invalid driver status type. Must be one of: ${validDriverStatuses.join(', ')}`
      });
    }
    
    if (category === 'truck' && !validTruckStatuses.includes(status_type)) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: `Invalid truck status type. Must be one of: ${validTruckStatuses.join(', ')}`
      });
    }

    // Use UPSERT to create or update
    const result = await query(`
      INSERT INTO contact_settings (category, status_type, primary_contact, phone, email, description, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW())
      ON CONFLICT (category, status_type) 
      DO UPDATE SET 
        primary_contact = EXCLUDED.primary_contact,
        phone = EXCLUDED.phone,
        email = EXCLUDED.email,
        description = EXCLUDED.description,
        updated_at = NOW()
      RETURNING id, category, status_type, primary_contact, phone, email, description, updated_at
    `, [category, status_type, primary_contact, phone, email, description || null]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Contact setting saved successfully'
    });
  } catch (error) {
    logError('CONTACT_SETTINGS_POST_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip,
      request_body: req.body
    });
    
    res.status(500).json({
      success: false,
      error: 'Database Error',
      message: 'Failed to save contact setting'
    });
  }
});

/**
 * @route   DELETE /api/contact-settings/:id
 * @desc    Delete a contact information setting
 * @access  Private (Admin only)
 */
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if user has admin role
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Access Denied',
        message: 'Admin privileges required to delete contact settings'
      });
    }

    const { id } = req.params;

    const result = await query(`
      DELETE FROM contact_settings 
      WHERE id = $1 
      RETURNING category, status_type
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Contact setting not found'
      });
    }

    res.json({
      success: true,
      message: `Contact setting for ${result.rows[0].category} ${result.rows[0].status_type} deleted successfully`
    });
  } catch (error) {
    logError('CONTACT_SETTINGS_DELETE_ERROR', error, {
      user_id: req.user?.id,
      ip_address: req.ip,
      setting_id: req.params.id
    });
    
    res.status(500).json({
      success: false,
      error: 'Database Error',
      message: 'Failed to delete contact setting'
    });
  }
});

/**
 * @route   GET /api/contact-settings/health
 * @desc    Health check for contact settings API
 * @access  Public
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'Contact Settings API',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    available_endpoints: [
      'GET / - Get all contact settings',
      'POST / - Create or update contact setting (Admin only)',
      'DELETE /:id - Delete contact setting (Admin only)',
      'GET /health - Health check'
    ]
  });
});

module.exports = router;
