#!/usr/bin/env node

/**
 * Development SSL Certificate Generator
 * Generates self-signed certificates for local HTTPS testing
 * Supports localhost and local network IP (**************)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  keySize: 2048,
  validDays: 365,
  country: 'US',
  state: 'Development',
  city: 'Local',
  organization: 'Hauling QR Trip System',
  organizationalUnit: 'Development',
  commonName: 'localhost',
  email: '<EMAIL>',
  subjectAltNames: [
    'DNS:localhost',
    'DNS:*.localhost',
    'IP:127.0.0.1',
    'IP:************',
    'DNS:************'
  ]
};

// Paths
const SSL_DIR = path.join(__dirname);
const DEV_DIR = path.join(SSL_DIR, 'dev');
const PRIVATE_KEY = path.join(DEV_DIR, 'server.key');
const CERTIFICATE = path.join(DEV_DIR, 'server.crt');
const CSR_FILE = path.join(DEV_DIR, 'server.csr');

console.log('🔐 Generating Development SSL Certificates for Hauling QR Trip System');
console.log('📁 SSL Directory:', SSL_DIR);

// Create directories
function createDirectories() {
  console.log('\n📂 Creating SSL directories...');
  
  if (!fs.existsSync(DEV_DIR)) {
    fs.mkdirSync(DEV_DIR, { recursive: true });
    console.log('✅ Created dev/ directory');
  }
  
  const prodDir = path.join(SSL_DIR, 'production');
  if (!fs.existsSync(prodDir)) {
    fs.mkdirSync(prodDir, { recursive: true });
    console.log('✅ Created production/ directory');
  }
}

// Generate OpenSSL configuration
function generateOpenSSLConfig() {
  const configPath = path.join(DEV_DIR, 'openssl.conf');
  
  const config = `
[req]
default_bits = ${CONFIG.keySize}
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=${CONFIG.country}
ST=${CONFIG.state}
L=${CONFIG.city}
O=${CONFIG.organization}
OU=${CONFIG.organizationalUnit}
CN=${CONFIG.commonName}
emailAddress=${CONFIG.email}

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
${CONFIG.subjectAltNames.map((name, index) => `${name.startsWith('DNS:') ? 'DNS' : 'IP'}.${index + 1} = ${name.replace(/^(DNS:|IP:)/, '')}`).join('\n')}
`;

  fs.writeFileSync(configPath, config.trim());
  console.log('✅ Generated OpenSSL configuration');
  return configPath;
}

// Generate private key
function generatePrivateKey() {
  console.log('\n🔑 Generating private key...');
  
  try {
    execSync(`openssl genrsa -out "${PRIVATE_KEY}" ${CONFIG.keySize}`, { stdio: 'pipe' });
    
    // Set secure permissions (Unix/Linux/macOS)
    if (process.platform !== 'win32') {
      fs.chmodSync(PRIVATE_KEY, 0o600);
    }
    
    console.log('✅ Private key generated:', PRIVATE_KEY);
  } catch (error) {
    console.error('❌ Failed to generate private key:', error.message);
    process.exit(1);
  }
}

// Generate certificate signing request
function generateCSR(configPath) {
  console.log('\n📝 Generating Certificate Signing Request...');
  
  try {
    execSync(`openssl req -new -key "${PRIVATE_KEY}" -out "${CSR_FILE}" -config "${configPath}"`, { stdio: 'pipe' });
    console.log('✅ CSR generated:', CSR_FILE);
  } catch (error) {
    console.error('❌ Failed to generate CSR:', error.message);
    process.exit(1);
  }
}

// Generate self-signed certificate
function generateCertificate(configPath) {
  console.log('\n📜 Generating self-signed certificate...');
  
  try {
    execSync(`openssl x509 -req -in "${CSR_FILE}" -signkey "${PRIVATE_KEY}" -out "${CERTIFICATE}" -days ${CONFIG.validDays} -extensions v3_req -extfile "${configPath}"`, { stdio: 'pipe' });
    console.log('✅ Certificate generated:', CERTIFICATE);
  } catch (error) {
    console.error('❌ Failed to generate certificate:', error.message);
    process.exit(1);
  }
}

// Verify certificate
function verifyCertificate() {
  console.log('\n🔍 Verifying certificate...');
  
  try {
    const certInfo = execSync(`openssl x509 -in "${CERTIFICATE}" -text -noout`, { encoding: 'utf8' });
    
    // Check for Subject Alternative Names
    if (certInfo.includes('Subject Alternative Name')) {
      console.log('✅ Certificate includes Subject Alternative Names');
    } else {
      console.warn('⚠️  Certificate may not include all required SANs');
    }
    
    // Extract validity dates
    const validFrom = certInfo.match(/Not Before: (.+)/)?.[1];
    const validTo = certInfo.match(/Not After : (.+)/)?.[1];
    
    console.log('📅 Valid from:', validFrom);
    console.log('📅 Valid until:', validTo);
    
  } catch (error) {
    console.error('❌ Failed to verify certificate:', error.message);
  }
}

// Create .gitignore
function createGitIgnore() {
  const gitignorePath = path.join(SSL_DIR, '.gitignore');
  const gitignoreContent = `
# SSL Private Keys - NEVER COMMIT THESE
*.key
*.pem

# Certificate Signing Requests
*.csr

# Production certificates (should be deployed separately)
production/

# Temporary files
*.tmp
*.temp
openssl.conf

# Backup files
*.bak
*.backup
`.trim();

  fs.writeFileSync(gitignorePath, gitignoreContent);
  console.log('✅ Created .gitignore for SSL directory');
}

// Main execution
function main() {
  try {
    // Check if OpenSSL is available
    execSync('openssl version', { stdio: 'pipe' });
  } catch (error) {
    console.error('❌ OpenSSL is not installed or not in PATH');
    console.error('   Please install OpenSSL to generate certificates');
    process.exit(1);
  }
  
  createDirectories();
  createGitIgnore();
  
  const configPath = generateOpenSSLConfig();
  generatePrivateKey();
  generateCSR(configPath);
  generateCertificate(configPath);
  verifyCertificate();
  
  console.log('\n🎉 Development SSL certificates generated successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Update server/.env with SSL configuration:');
  console.log('   SSL_CERT_PATH=./ssl/dev/server.crt');
  console.log('   SSL_KEY_PATH=./ssl/dev/server.key');
  console.log('   ENABLE_HTTPS=true');
  console.log('2. Start the server and test HTTPS access');
  console.log('3. For mobile testing, install the certificate on your device');
  console.log('\n🔗 Test URLs:');
  console.log('   https://localhost:5443/api/health');
  console.log('   https://**************:5443/api/health');
  
  // Clean up temporary files
  try {
    fs.unlinkSync(configPath);
    fs.unlinkSync(CSR_FILE);
  } catch (error) {
    // Ignore cleanup errors
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, CONFIG };
