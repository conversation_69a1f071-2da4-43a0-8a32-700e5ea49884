/**
 * PWA Cleanup Service
 * Handles cleanup of service workers and caches to prevent caching issues
 */

class PWACleanupService {
  static async cleanupServiceWorkers() {
    if (!('serviceWorker' in navigator)) {
      console.log('Service Worker not supported');
      return { success: true, count: 0 };
    }
    
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      const results = await Promise.allSettled(
        registrations.map(registration => 
          registration.unregister().then(success => {
            console.log(`SW unregistered: ${success}`, registration.scope);
            return success;
          })
        )
      );
      
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length;
      console.log(`Successfully unregistered ${successCount}/${registrations.length} service workers`);
      
      return { success: true, count: successCount };
    } catch (error) {
      console.warn('Service worker cleanup failed:', error);
      return { success: false, error: error.message };
    }
  }
  
  static async clearCaches() {
    if (!('caches' in window)) {
      console.log('Cache API not supported');
      return { success: true, count: 0 };
    }
    
    try {
      const cacheNames = await caches.keys();
      const results = await Promise.allSettled(
        cacheNames.map(name => 
          caches.delete(name).then(success => {
            console.log(`Cache '${name}' deleted: ${success}`);
            return success;
          })
        )
      );
      
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length;
      console.log(`Successfully cleared ${successCount}/${cacheNames.length} caches`);
      
      return { success: true, count: successCount };
    } catch (error) {
      console.warn('Cache cleanup failed:', error);
      return { success: false, error: error.message };
    }
  }
  
  static isCleanupSafe() {
    // Only perform cleanup in development or when explicitly enabled
    if (process.env.NODE_ENV === 'production' && !process.env.REACT_APP_ALLOW_PWA_CLEANUP) {
      return false;
    }
    
    // Check if we're in a secure context
    if (!window.isSecureContext) {
      console.warn('PWA cleanup skipped: not in secure context');
      return false;
    }
    
    return true;
  }
  
  static async performCleanup() {
    if (!this.isCleanupSafe()) {
      console.log('PWA cleanup skipped due to safety checks');
      return { skipped: true };
    }
    
    console.log('Starting PWA cleanup...');
    
    const [swResult, cacheResult] = await Promise.allSettled([
      this.cleanupServiceWorkers(),
      this.clearCaches()
    ]);
    
    const result = {
      serviceWorkers: swResult.status === 'fulfilled' ? swResult.value : { success: false, error: swResult.reason },
      caches: cacheResult.status === 'fulfilled' ? cacheResult.value : { success: false, error: cacheResult.reason }
    };
    
    console.log('PWA cleanup completed', result);
    return result;
  }
}

export default PWACleanupService;